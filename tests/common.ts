import { MedplumClient, stringify, MedplumRequestOptions, isResourceType } from '@medplum/core';
import { Condition, Encounter, Organization, Patient, Practitioner } from '@medplum/fhirtypes'

import { readFileSync } from 'fs'
import { join } from 'path';

export const common = {
  facilityPortal: 'https://relic-facility-portal.vercel.app/',
  patientListPage: 'https://relic-facility-portal.vercel.app/patient*',
  patientDetailsPage: 'https://relic-facility-portal.vercel.app/patients/edit/*',
  timeout: 30000,
  medplumLoginCredentials: {
    username: '<EMAIL>',
    password: 'reliccare'
  },
  pccLoginCredentials: {
    username: 'relfac5911.registerednurse',
    password: '2x5KUBvKj4OQtD5'
  },
  pcc_portal_loginCredentials: {
    URL:'https://login.pointclickcare.com/home/<USER>',
    Username:'relfac5911.uiaccess',
    Password:'Rel!cc@re2024'	
  },
  
  medplumCredentials: {
    MEDPLUM_CLIENT_ID: '1940c3ef-8b6f-4ac0-a66c-d6aca3bff786',
    MEDPLUM_CLIENT_SECRET: '0151de6a95f61082b358ca61618dcc422b47d46701a499811927d86710c560b7',
    MEDPLUM_FHIR_ENDPOINT: 'https://api.medplum.com/fhir/R4',
    MEDPLUM_AUTH_ENDPOINT: 'https://api.medplum.com/oauth2/'
  }
};

export const medplum = new MedplumClient();
medplum.startClientLogin(common.medplumCredentials.MEDPLUM_CLIENT_ID, common.medplumCredentials.MEDPLUM_CLIENT_SECRET);

export const loginToRelicaiPcc = async ({ page }) => {
  await Promise.all([
    page.goto(common.facilityPortal),
    page.waitForURL(common.facilityPortal),
  ]);
  page.getByRole('link', { name: 'PointClickCare' }).click();
  await page.getByPlaceholder('example.johnsmith').fill(common.pccLoginCredentials.username);
  await page.getByPlaceholder('****').fill(common.pccLoginCredentials.password);
  await Promise.all([
    page.getByRole('button', { name: 'Sign in' }).click(),
    page.waitForURL(common.patientListPage),
  ]);
  await page.getByRole('heading', { level: 6, name: /registerednurse/i }).waitFor();
}

export const loginToPcc = async ({ page }) => {
  await page.goto(common.pcc_portal_loginCredentials.URL)
  await page.getByLabel('Username').fill(common.pcc_portal_loginCredentials.Username)
  await page.getByRole('button', { name: 'Next' }).click()
  await page.locator('input#password, input[name="password"]').fill(common.pcc_portal_loginCredentials.Password)
  await page.getByRole('button', { name: 'Sign In' }).click()
}

export async function deletePatient(patient:Patient) {
  try {

    const familyName = patient.name?.[0]?.family || ''
    const givenName = patient.name?.[0]?.given?.[0] || ''
    await searchAndDeletePatients(givenName, familyName);
    
  } catch (error) {
    console.error('Error searching or deleting patients:', error);
  }
}

export async function searchAndDeletePatients(givenName: string, familyName: string) {
  try {

    // Step 1: Search for Patient resources based on given and family names ..
    const searchResult = await medplum.search('Patient', {
      name: `${givenName} ${familyName}`
    });

    // Step 2: Loop through each Patient and delete
    if (searchResult.entry && searchResult.entry.length > 0) {
      for (const entry of searchResult.entry) {
        const patient = entry.resource as Patient;
        console.log(`Deleting Patient: ${patient.id} - ${patient.name?.[0].given} ${patient.name?.[0].family}`);

        // Delete the patient resource
        await medplum.deleteResource('Patient', patient.id as string);
        console.log(`Patient ${patient.id} deleted successfully.`);
      }
    } else {
      console.log('No patients found with the given name and family name.');
    }
  } catch (error) {
    console.error('Error searching or deleting patients:', error);
  }
}

// Function to search and delete Condition resource based on display name
export async function deleteConditionByDisplayName(displayName: string): Promise<void> {
  try {
    // Search for Condition resource with the given display name
    const searchResult = await medplum.search('Condition', {
      code: displayName
    });

    if (searchResult.entry && searchResult.entry.length > 0) {
      // Extract the ID of the first matched Condition
      const conditionId = searchResult.entry[0].resource?.id;

      if (conditionId) {
        // Delete the Condition resource
        await medplum.deleteResource('Condition', conditionId);
        console.log(`Condition with display name "${displayName}" and ID ${conditionId} deleted successfully.`);
      } else {
        console.log('Condition resource found, but no ID available.');
      }
    } else {
      console.log(`No Condition resource found with display name "${displayName}".`);
    }
  } catch (error) {
    console.error('Error deleting Condition resource:', error);
  }
}

//export async function createPatient(patientJson: any): Promise<string> {
export async function createPatient(patientIndex: number): Promise<Patient> {
  try {

    const templatePath = join(__dirname, 'src','relic-ai-portal', 'medplum', 'js_patient.json');
    const templateString = readFileSync(templatePath, 'utf-8');
    const template = JSON.parse(templateString);
    const firstPatient = template.patients[patientIndex];

    const requestOptions: MedplumRequestOptions = {
      maxRetries: 5
    }

    let autoPatient = await medplum.createResource<Patient>(firstPatient as Patient, requestOptions);
    let patientID = autoPatient.id ?? ''
    console.log(`Created a new patient: ${patientID}`);
    return autoPatient;

  } catch (error) {
    console.error('Error creating a new patient:', error);
  }
  throw new Error("No patient found");
}

//export async function createPatient(patientJson: any): Promise<string> {
  export async function getPatient(patientIndex: number): Promise<Patient> {
    try {
  
      const templatePath = join(__dirname, 'src','relic-ai-portal', 'medplum', 'js_patient.json');
      const templateString = readFileSync(templatePath, 'utf-8');
      const template = JSON.parse(templateString);
      const firstPatient = template.patients[patientIndex];
      
      let jsonPatient = firstPatient as Patient;

      return jsonPatient;

  
    } catch (error) {
      console.error('Error creating a new patient:', error);
    }

    throw new Error("No patient found");

  }

  export async function getPCCPatient(patientIndex: number): Promise<any> {
    try {
  
      const templatePath = join(__dirname, 'src','relic-ai-portal', 'pcc', 'js_resident.json');
      const templateString = readFileSync(templatePath, 'utf-8');
      const template = JSON.parse(templateString);
      const firstPatient = template.patients[patientIndex];
      
      return firstPatient;

  
    } catch (error) {
      console.error('Error creating a new patient:', error);
    }

    throw new Error("No patient found");

  }

  //export async function createPatient(patientJson: any): Promise<string> {
export async function createPatientCondition(patientIndex: number, conditionIndex: number): Promise<Condition> {
  try {

    let newPatient: Patient = await createPatient(patientIndex);

    const templatePath = join(__dirname, 'src','relic-ai-portal', 'medplum', 'js_patient.json');
    const templateString = readFileSync(templatePath, 'utf-8');
    const template = JSON.parse(templateString);
    const newCondition: Condition = template.conditions[conditionIndex];

    newCondition.subject.reference = `Patient/${newPatient.id}`
    

    const requestOptions: MedplumRequestOptions = {
      maxRetries: 5
    }

    let autoCondition = await medplum.createResource<Condition>(newCondition as Condition, requestOptions);
    let conditionId = autoCondition.id ?? ''
    console.log(`Created a new condition: ${conditionId}`);
    return autoCondition;

  } catch (error) {
    console.error('Error creating a new patient:', error);
  }
  throw new Error("No condition found");
}

export async function deleteCondition(conditionID: string) {
  try {

    const requestOptions: MedplumRequestOptions = {
      maxRetries: 5
    }

    await medplum.deleteResource('Condition',conditionID, requestOptions);
    

  } catch (error) {
    console.error('Error deleting condition:', error);
    }
}

