import { test as base, expect } from '@playwright/test';
import { common } from '../../../common';

base.beforeEach(async ({ page }) => {
  await Promise.all([
    page.goto(common.facilityPortal),
    page.waitForURL(common.facilityPortal),
  ]);
  await page.getByRole('link', { name: 'Medplum' }).click();
  await page.getByPlaceholder('<EMAIL>').fill(common.medplumLoginCredentials.username);
  await page.getByRole('button', { name: 'Next' }).click();
  await page.getByLabel('Password *').click();
  await page.getByLabel('Password *').fill(common.medplumLoginCredentials.password);
  await Promise.all([
    page.getByRole('button', { name: 'Sign in' }).click(),
    page.waitForURL(common.patientListPage),
  ]);
  await expect(page.getByRole('link', { name: 'Relic AI Portal' })).toBeVisible();
});

export { base as test };
export { expect };