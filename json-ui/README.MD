# Relic Ai

Relic Ai project is the front end for using Relic Ai Assistants

## Getting Started with Relic Ai
Relic Ai is built using [**Refine**](https://refine.dev), which is a React-based framework for building data-intensive applications. Refine offers a number of out-of-the box functionality for rapid user interface development, without compromising extreme customizability.

For Relic Ai, we are using Refine to:
- <b>Auto-generate a UI</b> that has basic CRUD screens. After auto-generation, the UI can be modified as per requirements. Refine offers a headless architecture & hence full customizations on the user-interface are possible.
- <b>Create quick playgrounds</b> for back-end REST services. This would allow back-end developers to test/verify their system behavior for any issues that are raised. This playground can be customized over time for specific REST end points (also called as [refine resources](https://refine.dev/docs/tutorial/understanding-resources/index/#what-is-resource)).
- <b>Develop Relic Ai</b> using [Refine Core API](https://refine.dev/docs/api-reference/core/) so we can follow refine's standard core APIs as a best practice for front-end development. If we need to deviate from refine core APIs for front end development then it should require a discussion and approval from Technical Leadership.

## Login & Access using Web Browser
To login to Relic Ai and obtain access, a pre-registered user account is needed. Currently Relic Ai supports organizations using [Medplum](https://www.medplum.com/) and [PointClickCare](https://pointclickcare.com/) EHRs. The registration process is manual where an organization needs to be registered to use Relic Ai. 
- If an organization is not registered then the users will not be able to login. 
- For a registered organization, no separate user registration is required. Users are registered automatically on first login.

## Login & Access using Microsoft Teams
@infysumanta to add more details.

## Available Scripts

Scripts mentioned below are available 
### Installation of the package

```bash
    pnpm i
```

### Running the development server.

```bash
    pnpm dev
```

### Building for production.

```bash
    pnpm build
```

### Running the production server.

```bash
    pnpm start
```