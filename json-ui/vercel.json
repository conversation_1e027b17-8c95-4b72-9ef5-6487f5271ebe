{"rewrites": [{"source": "/((?!lobby|demo|teams).*)", "destination": "/"}, {"source": "/lobby/template", "destination": "/templates/b2c.html"}, {"source": "/lobby/(.*)", "destination": "/lobby/"}, {"source": "/demo/(.*)", "destination": "/demo/"}, {"source": "/teams/(.*)", "destination": "/teams/"}], "headers": [{"source": "**", "headers": [{"key": "Content-Security-Policy", "value": "frame-ancestors 'self' https://*.cloud.microsoft https://teams.microsoft.com https://*.teams.microsoft.com;"}, {"key": "X-Frame-Options", "value": "ALLOW-FROM https://*.cloud.microsoft https://teams.microsoft.com https://*.teams.microsoft.com;"}]}, {"source": "/lobby/template", "headers": [{"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Access-Control-Allow-Origin", "value": "https://patientauth.b2clogin.com"}, {"key": "Access-Control-Allow-Methods", "value": "GET,OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "*"}, {"key": "Access-Control-Max-Age", "value": "200"}]}, {"source": "/demo/template", "headers": [{"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Access-Control-Allow-Origin", "value": "https://patientauth.b2clogin.com"}, {"key": "Access-Control-Allow-Methods", "value": "GET,OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "*"}, {"key": "Access-Control-Max-Age", "value": "200"}]}]}