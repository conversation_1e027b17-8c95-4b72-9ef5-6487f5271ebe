{"$schema": "https://developer.microsoft.com/en-us/json-schemas/teams/v1.16/MicrosoftTeams.schema.json", "version": "1.0.0", "manifestVersion": "1.16", "id": "51996bf4-8aa0-4bd4-99ef-147b97c8ee20", "name": {"short": "Relic AI", "full": "Use Relic Ai Assistants for Healthcare in Microsoft Teams"}, "description": {"short": "Ai Assistants for Healthcare meeting your team members where they operate.", "full": "This app provides access to Relic Ai Assistants for Healthcare, allowing users to interact with AI-driven healthcare solutions directly within Microsoft Teams."}, "developer": {"name": "Relic Care, Inc.", "websiteUrl": "https://reliccare.com", "privacyUrl": "https://portals.docsie.io/reliccare/ws/relic-ai-user-manual/conversational-ai-multi-lingual/deployment_J3thpDIFs5Zbl4EsU/?doc=/privacy-statement/", "termsOfUseUrl": "https://reliccare.com"}, "icons": {"outline": "outline.png", "color": "color.png"}, "accentColor": "#95A72C", "staticTabs": [{"entityId": "relicFacilityTab", "name": "Relic Portal", "contentUrl": "https://relic-facility-portal.vercel.app/", "scopes": ["personal"]}, {"entityId": "localapp", "name": "Local App", "contentUrl": "https://localhost:5173", "scopes": ["personal"]}, {"entityId": "ngrokapp", "name": "Ngrok App", "contentUrl": "https://cat-precious-terminally.ngrok-free.app", "scopes": ["personal"]}], "validDomains": ["relic-facility-portal.vercel.app", "www.reliccare.com", "localhost", "cat-precious-terminally.ngrok-free.app"], "permissions": ["identity", "messageTeamMembers"], "webApplicationInfo": {"id": "0e32f196-3cca-448a-b80f-fcfe5f0657ff", "resource": "api://cat-precious-terminally.ngrok-free.app/0e32f196-3cca-448a-b80f-fcfe5f0657ff"}}