import { ServerError } from '@azure/msal-browser';
import { AuthProvider, OpenNotificationParams } from '@refinedev/core';
import {
  AuthActionResponse,
  CheckResponse,
  OnErrorResponse,
} from '@refinedev/core/dist/contexts/auth/types';
import { IUserIdentity } from 'relic-ui';

import { IActiveLogin } from 'src/types';
import {
  getActiveLogin,
  getFallbackUri,
  getIdTokenHint,
  getRedirectUri,
  isValidLoginB2C as isValidLogin,
  loginRequestB2c,
  logoutB2C as logout,
  MsalInstance,
  oAuthApiB2c,
  storeUserIdentityAndAccess,
  triggerUiNotification,
} from './utils';

const successRedirectTo = getRedirectUri;
const failureRedirectTo = getFallbackUri;
const authActionResponse: AuthActionResponse = {
  success: false,
};
const checkResponse: CheckResponse = {
  authenticated: false,
};
const onErrorResponse: OnErrorResponse = {
  logout: false,
};

/**
 * The `authProviderLobby` constant implements the `AuthProvider` interface and provides
 * authentication functionalities for Patients. They can login using their emaild address or phone.
 * There is a passwordless login option available for patients.
 */
export const authProviderLobby: AuthProvider = {
  login: async ({ id, id_token_hint, redirectTo }) => {
    try {
      if (!id && !id_token_hint) {
        throw new Error('Neither ID nor ID token hint provided');
      }

      let token = id_token_hint;

      if (!id_token_hint) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const type = emailRegex.test(id) ? 'email' : 'phone';
        token = await getIdTokenHint(type, id);
      }

      const state = Math.random().toString(36);
      localStorage.setItem('b2cAuthState', state);

      const requestData = {
        ...loginRequestB2c,
        state,
        extraQueryParameters: {
          id_token_hint: token,
        },
        ...(redirectTo && { redirectUri: redirectTo }),
      };

      console.log('Request Data:', requestData);
      await oAuthApiB2c(requestData);
      authActionResponse.success = true;
      authActionResponse.redirectTo = successRedirectTo();
      authActionResponse.error = undefined;
      return Promise.resolve(authActionResponse);
    } catch (error) {
      if (error instanceof ServerError) {
        authActionResponse.success = false;
      } else {
        const errorInstance = {
          ...error,
          name: 'Login Error',
          message: error.message,
        };
        authActionResponse.success = false;
        authActionResponse.error = errorInstance;
      }
      return Promise.resolve(authActionResponse);
    }
  },

  logout: async () => {
    await logout();
    authActionResponse.success = true;
    authActionResponse.redirectTo = failureRedirectTo();
    return Promise.resolve(authActionResponse);
  },

  check: async () => {
    const urlParams = new URLSearchParams(
      window.location.search || window.location.hash.substring(1),
    );
    try {
      if (urlParams.has('code')) {
        let state = urlParams.get('state') as string;
        // Azure Entra OAuth attaches its own state with the state passed by browser.
        if (state && state.includes('|')) {
          state = state.split('|')[1];
        }
        const b2cAuthState = localStorage.getItem('b2cAuthState');
        if (state === b2cAuthState) {
          // Handle Entra auth response
          const b2cClient = await MsalInstance.getClient('b2c');
          const authResponse = await b2cClient.handleRedirectPromise();
          if (authResponse) {
            const activeLogin = await MsalInstance.handleAuthResponse(
              authResponse,
              'b2c',
            );
            localStorage.setItem('activeLogin', JSON.stringify(activeLogin));
          }
        }
        const activeLogin: IActiveLogin = getActiveLogin();
        await storeUserIdentityAndAccess(activeLogin);
      }

      /* Now, check if the user is logged in */
      const activeLogin: IActiveLogin = getActiveLogin();
      if (
        activeLogin &&
        activeLogin?.accessToken &&
        isValidLogin(activeLogin)
      ) {
        checkResponse.authenticated = true;
        checkResponse.error = undefined;
        return Promise.resolve(checkResponse);
      } else {
        throw new Error('No active login found. Kindly re-login.');
      }
    } catch (error) {
      const uiError: OpenNotificationParams = {
        type: 'error',
        key: error.statusCode,
        message: error.name,
        description: error.message,
      };
      const errorStatus =
        error.status ?? error.statusCode ?? error.response?.status;
      if (
        errorStatus === '401' ||
        errorStatus === 401 ||
        errorStatus === '400' ||
        errorStatus === 400
      ) {
        await logout();
        checkResponse.logout = true;
        triggerUiNotification(uiError);
      } else {
        checkResponse.logout = false;
      }
      checkResponse.redirectTo = failureRedirectTo();
      checkResponse.error = error;
      return Promise.resolve(checkResponse);
    }
  },

  getPermissions: async () => null,

  getIdentity: async () => {
    const activeLogin: IActiveLogin = getActiveLogin();
    const userIdentity = JSON.parse(
      localStorage.getItem('userIdentity') as string,
    ) as IUserIdentity;
    if (activeLogin && userIdentity) {
      return {
        id: activeLogin.id,
        name: activeLogin.name,
        userIdentity: userIdentity,
        accessToken: activeLogin.accessToken,
        idToken: activeLogin.idToken,
        organizationId: activeLogin.organizationId,
      };
    }
    await logout();
    checkResponse.authenticated = false;
    checkResponse.redirectTo = failureRedirectTo();
    return Promise.resolve(checkResponse);
  },

  onError: async error => {
    try {
      const errorStatus =
        error.status ?? error.statusCode ?? error.response?.status;
      if (errorStatus === '401' || errorStatus === 401) {
        onErrorResponse.logout = true;
        onErrorResponse.redirectTo = failureRedirectTo();
        onErrorResponse.error = error;
      } else {
        const redirectTo = window.location.pathname + window.location.search;
        onErrorResponse.redirectTo = getRedirectUri(false, redirectTo);
        onErrorResponse.error = error;
        console.log(onErrorResponse);
      }
      return Promise.resolve(onErrorResponse);
    } catch (e) {
      onErrorResponse.logout = true;
      onErrorResponse.redirectTo =
        onErrorResponse.redirectTo ?? failureRedirectTo();
      onErrorResponse.error = onErrorResponse.error ?? e;
      return Promise.resolve(onErrorResponse);
    }
  },
};
