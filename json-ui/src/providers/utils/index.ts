import { loginRequestB2c, loginRequestEntra } from './auth-requests';
import {
  getActiveLogin,
  getFallbackUri,
  getIdTokenHint,
  getRedirectUri,
  jwtDecode,
  storeUserIdentityAndAccess,
  triggerUiNotification,
  whatsMyAccess,
  whoami,
} from './auth-utils';
import {
  isValidLogin as isValidLoginB2C,
  logout as logoutB2C,
  oAuthApiB2c,
} from './auth-utils-lobby';
import {
  isValidLogin,
  logout,
  oAuthApi,
  silentAuthRefresh,
} from './auth-utils-relic-ai';
import { convertToNodeServicesRequest } from './axios-utils';
import msalManager from './msal-manager';

// Axios utilities
export { convertToNodeServicesRequest };

// Core authentication utilities
export {
  getActiveLogin,
  silentAuthRefresh,
  storeUserIdentityAndAccess,
  triggerUiNotification,
};

// Relic-AI authentication utilities
export { isValidLogin, logout, oAuthApi };

// Lobby App authentication utilities
export {
  getFallbackUri,
  getIdTokenHint,
  getRedirectUri,
  isValidLoginB2C,
  jwtDecode,
  loginRequestB2c,
  loginRequestEntra,
  logoutB2C,
  msalManager as MsalInstance,
  oAuthApiB2c,
  whatsMyAccess,
  whatsMyAccess as whatsMyAccessB2C,
  whoami,
  whoami as whoamiB2C,
};
