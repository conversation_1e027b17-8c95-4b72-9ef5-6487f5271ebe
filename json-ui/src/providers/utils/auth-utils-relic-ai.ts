import { HttpError } from '@refinedev/core';
import axios from 'axios';
import { IActiveLogin, ILogin } from 'src/types';
import { getNodeServicesApi } from 'src/utils/app-config';
import { getActiveLogin } from './auth-utils';
import MsalInstance from './msal-manager';

const authAxiosInstance = axios.create();

authAxiosInstance.interceptors.response.use(
  response => response,
  error => {
    const customError: HttpError = {
      ...error,
      message:
        error.response?.data?.message ||
        error.response?.message ||
        error?.message ||
        'An unknown error occurred',
      statusCode: error.response?.status,
    };

    return Promise.reject(customError);
  },
);

/**
 * Check if the login is valid. Includes refresh token validity check for pcc.
 * @param {IActiveLogin} activeLogin - currently active login
 * @returns {boolean}
 */
export const isValidLogin = (activeLogin: IActiveLogin): boolean => {
  if (!activeLogin) {
    return false;
  }

  if (!(activeLogin.accessToken || activeLogin.idToken)) {
    return false;
  }

  if (!localStorage.getItem('userIdentity')) {
    return false;
  }

  if (activeLogin && activeLogin.provider === 'pcc') {
    if (activeLogin.refreshToken && activeLogin.refreshTokenExpiresIn) {
      const currentTime = Date.now();
      return activeLogin.refreshTokenExpiresIn > currentTime;
    } else {
      return false;
    }
  }
  return true;
};

export const oAuthApi = async (
  code: string,
  action: 'login' | 'reauth' | 'revokeAccess' | 'logout',
  login: ILogin | IActiveLogin,
): Promise<void> => {
  const tokenUrl =
    window.location.hostname === 'localhost'
      ? `${getNodeServicesApi()}/local/${login.provider}/auth/token`
      : `${getNodeServicesApi()}/api/${login.provider}/auth/token`;
  let revokeUrl = '';
  if (action === 'login') {
    if (!login.provider || !code) {
      const error: HttpError = {
        name: 'Unauthorized.',
        message: 'No provider or code provided.',
        statusCode: 401,
      };
      return Promise.reject(error);
    }
    const loginData = {
      code: code,
      grant_type: 'authorization_code',
      redirect_uri: window.location.origin + '/patients',
    };
    const response = await authAxiosInstance
      .post(tokenUrl, new URLSearchParams(loginData))
      .then(response => response.data);
    const activeLogin: IActiveLogin = {
      provider: login.provider,
      accessToken: response?.access_token,
      idToken: response?.id_token,
      expiresIn: Date.now() + Number(response.expires_in) * 1000,
    };
    if (response?.refresh_token) {
      activeLogin.refreshToken = response?.refresh_token;
    }
    if (response?.refresh_token_expires_in) {
      activeLogin.refreshTokenExpiresIn =
        Date.now() + Number(response.refresh_token_expires_in) * 1000;
    }
    if (response?.metadata?.orgUuid) {
      activeLogin.organizationId = response.metadata.orgUuid;
    }
    localStorage.setItem('activeLoginEHR', JSON.stringify(activeLogin));
  }
  if (action === 'reauth') {
    if (!('refreshToken' in login)) {
      const error: HttpError = {
        name: 'Unauthorized.',
        message: 'Active Login is required for Re Authorization.',
        statusCode: 401,
      };
      return Promise.reject(error);
    }
    const activeLogin = login as IActiveLogin;
    if (!activeLogin?.refreshToken) {
      const error: HttpError = {
        name: 'Unauthorized.',
        message: 'No refresh token provided.',
        statusCode: 401,
      };
      return Promise.reject(error);
    }
    const reauthData = {
      grant_type: 'refresh_token',
      refresh_token: activeLogin.refreshToken as string,
    };
    const response = await axios
      .post(tokenUrl, new URLSearchParams(reauthData))
      .then(response => response.data);
    activeLogin.accessToken = response.access_token;
    activeLogin.expiresIn = Date.now() + Number(response.expires_in) * 1000;
    activeLogin.refreshToken = response.refresh_token;
    if (response.refresh_token_expires_in) {
      activeLogin.refreshTokenExpiresIn =
        Date.now() + Number(response.refresh_token_expires_in) * 1000;
    }
    localStorage.setItem('activeLoginEHR', JSON.stringify(activeLogin));
  }
  if (action === 'revokeAccess') {
    if (!('accessToken' in login)) {
      const error: HttpError = {
        name: 'Unauthorized.',
        message: 'Active Login is required for revoking access.',
        statusCode: 401,
      };
      return Promise.reject(error);
    }
    const activeLogin = login as IActiveLogin;
    if (activeLogin.provider === 'pcc') {
      revokeUrl =
        window.location.hostname === 'localhost'
          ? `${getNodeServicesApi()}/local/pcc/auth/revoke`
          : `${getNodeServicesApi()}/api/pcc/auth/revoke`;
    }
    if (activeLogin?.provider === 'pcc' && activeLogin?.refreshToken) {
      const revokeRefreshToken = new URLSearchParams({
        token: activeLogin?.refreshToken as string,
        token_type_hint: 'refresh_token',
      });
      await authAxiosInstance.post(revokeUrl, revokeRefreshToken);
    }
    const revokeAccessToken =
      activeLogin?.provider === 'pcc'
        ? new URLSearchParams({
            token: activeLogin?.accessToken as string,
            token_type_hint: 'access_token',
          })
        : {};
    await authAxiosInstance.post(revokeUrl, revokeAccessToken, {
      headers: {
        Authorization: `Bearer ${activeLogin.accessToken}`,
        'Content-Type': 'application/json',
      },
    });
    localStorage.removeItem('activeLoginEHR');
  }
  if (action === 'logout') {
    const activeLogin = login as IActiveLogin;
    if (activeLogin?.provider !== 'medplum') {
      const error: HttpError = {
        name: 'Unauthorized.',
        message: 'Logout action is only available for medplum provider.',
        statusCode: 401,
      };
      return Promise.reject(error);
    }
    revokeUrl =
      window.location.hostname === 'localhost'
        ? `${getNodeServicesApi()}/local/medplum/auth/logout`
        : `${getNodeServicesApi()}/api/medplum/auth/logout`;
    const revokeData = {};
    await authAxiosInstance.post(revokeUrl, revokeData, {
      headers: {
        Authorization: `Bearer ${activeLogin.accessToken}`,
        'Content-Type': 'application/json',
      },
    });
    localStorage.removeItem('activeLoginEHR');
  }
};

/**
 * Refreshes the authentication token silently for the given active login.
 *
 * This function checks the provider of the active login and performs the appropriate
 * token refresh operation. If the provider is 'pcc', it reauthenticates using the PCC
 * refresh token. If the provider is 'medplum', it uses the Medplum client to refresh
 * the token if it has expired.
 *
 * @param {IActiveLogin} activeLogin - The active login object containing the provider and refresh token.
 * @returns {Promise<void>} A promise that resolves when the token refresh operation is complete.
 * @throws Will log an error message if the token refresh operation fails. No error is thrown.
 */
let refreshPromise: Promise<void> | null = null;
export async function silentAuthRefresh(
  activeLogin: IActiveLogin,
): Promise<void> {
  if (!refreshPromise) {
    refreshPromise = (async () => {
      try {
        if (activeLogin && activeLogin.refreshToken) {
          await oAuthApi(
            activeLogin.refreshToken as string,
            'reauth',
            activeLogin,
          );
        }
      } catch (error) {
        console.log('Error refreshing token', error);
      } finally {
        refreshPromise = null;
      }
    })();
  }
  await refreshPromise;
}

/**
 * Logs out the user by revoking access tokens and clearing local storage.
 *
 * This function performs the following steps:
 * 1. Retrieves the active login information.
 * 2. If the active login provider is 'pcc', it revokes the access token and removes the 'pccAuthState' from local storage.
 * 3. If the active login provider is 'medplum', it logs out the user and removes the 'medplumAuthState' from local storage.
 * 4. In case of any error during the process, it logs the error to the console.
 * 5. Finally, it clears several items from local storage, including 'activeLogin', 'logins', 'userIdentity', 'activeLoginEHR', and 'accessPolicy'.
 *
 * @returns {Promise<void>} A promise that resolves when the logout process is complete.
 */
export const logout = async (): Promise<void> => {
  try {
    const activeLogin = getActiveLogin();
    if (activeLogin && activeLogin.provider === 'entra') {
      await MsalInstance.logout('entra');
    } else if (activeLogin && activeLogin.provider === 'b2c') {
      await MsalInstance.logout('b2c');
    } else if (activeLogin && activeLogin.provider === 'pcc') {
      await oAuthApi(
        activeLogin.accessToken as string,
        'revokeAccess',
        activeLogin,
      );
    } else if (activeLogin && activeLogin.provider === 'medplum') {
      await oAuthApi(activeLogin.accessToken as string, 'logout', activeLogin);
    }
  } catch (e) {
    console.error('Could not clear active login', e);
  } finally {
    localStorage.removeItem('activeLogin');
    localStorage.removeItem('logins');
    localStorage.removeItem('userIdentity');
    localStorage.removeItem('activeLoginEHR');
    localStorage.removeItem('accessPolicy');
    localStorage.removeItem('medplumAuthState');
    localStorage.removeItem('pccAuthState');
    localStorage.removeItem('b2cAuthState');
    localStorage.removeItem('entraAuthState');
  }
};
