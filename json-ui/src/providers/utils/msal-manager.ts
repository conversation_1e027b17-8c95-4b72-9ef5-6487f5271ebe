import {
  AuthenticationResult,
  AuthError,
  Configuration,
  EventMessage,
  EventType,
  IPublicClientApplication,
  PublicClientApplication,
} from '@azure/msal-browser';
import { OpenNotificationParams } from '@refinedev/core';

import { IActiveLogin } from 'src/types';
import { triggerUiNotification } from './auth-utils';

const {
  VITE_B2C_CLIENT_ID,
  VITE_B2C_TENANT_ID,
  VITE_MICROSOFT_LOGIN_CLIENT_ID,
} = import.meta.env;

// B2C Configuration
const b2cPolicies = {
  authority: `https://${VITE_B2C_TENANT_ID}.b2clogin.com/${VITE_B2C_TENANT_ID}.onmicrosoft.com/B2C_1A_SIGNIN_RELIC`,
  authorityDomain: `${VITE_B2C_TENANT_ID}.b2clogin.com`,
};

const msalConfigB2C: Configuration = {
  auth: {
    clientId: VITE_B2C_CLIENT_ID as string,
    authority: b2cPolicies.authority,
    knownAuthorities: [b2cPolicies.authorityDomain],
    redirectUri: window.location.origin + '/sandbox',
    postLogoutRedirectUri: window.location.origin + '/lobby',
    navigateToLoginRequestUrl: false,
  },
  cache: {
    cacheLocation: 'localStorage',
    storeAuthStateInCookie: false,
  },
};

// Entra Configuration
const msalConfigEntra: Configuration = {
  auth: {
    clientId: VITE_MICROSOFT_LOGIN_CLIENT_ID as string,
    authority: 'https://login.microsoftonline.com/common',
    redirectUri: window.location.origin + '/patients',
    postLogoutRedirectUri: window.location.origin,
    navigateToLoginRequestUrl: false,
  },
  cache: {
    cacheLocation: 'localStorage',
    storeAuthStateInCookie: false,
  },
};

/**
 * Extracts a user-friendly error message from an MSAL Azure AD B2C error string.
 *
 * @param {string} errorString - The error message string from MSAL Azure AD B2C.
 * @returns {string} - A cleaned, user-friendly error message. Returns "Something Went Wrong" if no match is found.
 */
function getUserFriendlyErrorFromMSAL(errorString: string): string {
  // Regex to capture the main message by excluding the code, correlation ID, and timestamp
  const regex = /:\s*[^:]+:\s*(.+?)(?=\s*Correlation ID:|\s*Timestamp:|$)/; // Capture only the message after the code
  const match = errorString.match(regex); // Attempt to match the regex pattern
  if (match) {
    const message = match[1].trim(); // Get the message part and trim whitespace
    return message; // Return the cleaned message
  } else {
    return 'Login failed. Please check your credentials and try again.'; // Default message if no match found
  }
}

export type MsalInstanceType = 'b2c' | 'entra';

class MsalManager {
  private static instance: MsalManager;
  private b2cClient?: IPublicClientApplication;
  private entraClient?: IPublicClientApplication;

  /**
   * Private constructor to prevent direct instantiation.
   * Use MsalManager.getInstance() instead.
   */
  private constructor() {
    // Private constructor for singleton pattern
  }

  static getInstance(): MsalManager {
    if (!MsalManager.instance) {
      MsalManager.instance = new MsalManager();
    }
    return MsalManager.instance;
  }

  async getClient(type: MsalInstanceType): Promise<IPublicClientApplication> {
    if (type === 'b2c' && !this.b2cClient) {
      this.b2cClient = await this.initializeClient(msalConfigB2C);
    }
    if (type === 'entra' && !this.entraClient) {
      this.entraClient = await this.initializeClient(msalConfigEntra);
    }
    return type === 'b2c' ? this.b2cClient! : this.entraClient!;
  }

  private async initializeClient(
    config: Configuration,
  ): Promise<IPublicClientApplication> {
    const client =
      await PublicClientApplication.createPublicClientApplication(config);
    this.setupEventCallbacks(client);
    return client;
  }

  private setupEventCallbacks(client: IPublicClientApplication) {
    client.addEventCallback((message: EventMessage) => {
      if (
        message.eventType === EventType.LOGIN_FAILURE ||
        message.eventType === EventType.ACQUIRE_TOKEN_FAILURE ||
        message.eventType === EventType.SSO_SILENT_FAILURE ||
        message.eventType === EventType.LOGOUT_FAILURE
      ) {
        if (message.error instanceof AuthError) {
          const authError = message.error as AuthError;
          const uiError: OpenNotificationParams = {
            type: 'error',
            key: authError.errorCode,
            message: authError.name,
            description: getUserFriendlyErrorFromMSAL(authError.message),
          };
          console.error('MSAL Login Error:', authError);
          triggerUiNotification(uiError);
        }
      }

      if (message.eventType === EventType.LOGOUT_SUCCESS) {
        this.clearAuthState();
      }
    });
  }

  async handleAuthResponse(
    response: AuthenticationResult,
    type: MsalInstanceType,
  ): Promise<IActiveLogin> {
    const client = await this.getClient(type);
    client.setActiveAccount(response.account);

    const activeLogin: IActiveLogin = {
      accessToken: response.accessToken,
      idToken: response.idToken,
      expiresIn: (response.idTokenClaims as { exp: number }).exp * 1000,
      provider: type === 'b2c' ? 'b2c' : 'entra',
      b2cAccount: type === 'b2c' ? JSON.stringify(response.account) : undefined,
    };

    this.updateAuthState(type, activeLogin);
    return activeLogin;
  }

  private updateAuthState(type: MsalInstanceType, state: IActiveLogin) {
    const stateKey = type === 'b2c' ? 'b2cAuthState' : 'entraAuthState';
    localStorage.setItem(stateKey, JSON.stringify(state));
  }

  private clearAuthState() {
    localStorage.removeItem('activeLogin');
    localStorage.removeItem('logins');
    localStorage.removeItem('userIdentity');
    localStorage.removeItem('activeLoginEHR');
    localStorage.removeItem('accessPolicy');
    localStorage.removeItem('medplumAuthState');
    localStorage.removeItem('pccAuthState');
    localStorage.removeItem('b2cAuthState');
    localStorage.removeItem('entraAuthState');
    localStorage.removeItem('threadId');
  }

  async logout(type: MsalInstanceType): Promise<void> {
    const client = await this.getClient(type);
    const account = client.getActiveAccount();
    client.clearCache();
    if (account && type === 'b2c') {
      await client.logoutRedirect({
        account,
        postLogoutRedirectUri:
          type === 'b2c'
            ? msalConfigB2C.auth.postLogoutRedirectUri
            : msalConfigEntra.auth.postLogoutRedirectUri,
      });
    }
  }
}

export default MsalManager.getInstance();
