import { RedirectRequest } from '@azure/msal-browser';
import { HttpError } from '@refinedev/core';
import axios from 'axios';

import { IActiveLogin } from 'src/types';
import { getNodeServicesApi } from '../../utils/app-config';
import { getActiveLogin } from './auth-utils';
import MsalInstance from './msal-manager';

const authAxiosInstance = axios.create();

authAxiosInstance.interceptors.response.use(
  response => response,
  error => {
    const customError: HttpError = {
      ...error,
      message:
        error.response?.data?.message ||
        error.response?.message ||
        error?.message ||
        'An unknown error occurred',
      statusCode: error.response?.status,
    };

    return Promise.reject(customError);
  },
);

/**
 * Check if the login is valid. Includes refresh token validity check for pcc.
 * @param {IActiveLogin} activeLogin - currently active login
 * @returns {boolean}
 */
export const isValidLogin = (activeLogin: IActiveLogin): boolean => {
  if (!activeLogin) {
    return false;
  }

  if (!(activeLogin.accessToken || activeLogin.idToken)) {
    return false;
  }

  if (!localStorage.getItem('userIdentity')) {
    return false;
  }

  if (activeLogin && activeLogin.provider === 'b2c') {
    if (activeLogin.b2cAccount && activeLogin.expiresIn) {
      const currentTime = Date.now();
      return activeLogin.expiresIn > currentTime;
    } else {
      return false;
    }
  }
  return true;
};

/**
 * Initiates a login or token acquisition flow for Azure AD B2C via redirect.
 *
 * @param {RedirectRequest} b2cLoginRequest - The login request object containing
 * the parameters for the redirect request.
 * @returns {Promise<void>} A promise that resolves when the login or token
 * acquisition process is complete.
 */
export const oAuthApiB2c = async (
  b2cLoginRequest: RedirectRequest,
): Promise<void> => {
  const client = await MsalInstance.getClient('b2c');
  const tokenResponse = await client.handleRedirectPromise();
  const accounts = client.getAllAccounts();

  if (!tokenResponse || accounts?.length === 0) {
    await client.loginRedirect(b2cLoginRequest);
  } else {
    await client.acquireTokenRedirect({
      ...b2cLoginRequest,
      account: accounts && accounts[0],
    });
  }
};

/**
 * Logs out the user from the B2C Azure AD
 * @param {IActiveLogin} activeLogin The active login object
 * @returns {Promise<void>} A promise that resolves after the logout is complete
 */
export const logout = async (): Promise<void> => {
  try {
    const activeLogin = getActiveLogin();
    if (activeLogin && activeLogin.provider === 'b2c') {
      await MsalInstance.logout('b2c');
    }
  } catch (e) {
    console.error('Could not clear active login', e);
  }
};

export const checkPatientExists = async (email: string, phone: string) => {
  const response = await authAxiosInstance.get(
    `${getNodeServicesApi()}/api/demo/patients?email=${email}&phone=${phone}`,
  );
  return response.data;
};
