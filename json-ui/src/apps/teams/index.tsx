import { DEFAULT_COMPONENT_ICONS } from '@azure/communication-react';
import { initializeIcons, registerIcons } from '@fluentui/react';
import React from 'react';
import { createRoot } from 'react-dom/client';
import { BrowserRouter } from 'react-router-dom';

import { LicenseInfo } from '@mui/x-license';

import { setAppName } from 'src/utils/app-config';

import { MsalInstance } from 'src/providers/utils';

import App from './App';
import './i18n';

LicenseInfo.setLicenseKey(
  'b970abffbe3bf712e68edaf3dc5a9676Tz0xMTI2MjUsRT0xNzc4Mjg0Nzk5MDAwLFM9cHJvLExNPXN1YnNjcmlwdGlvbixQVj1RMy0yMDI0LEtWPTI=',
);

const appName = '/teams';
setAppName(appName);

// Initialize MSAL instances early
void MsalInstance.getClient('b2c');
void MsalInstance.getClient('entra');

const container = document.getElementById('root') as HTMLElement;
const root = createRoot(container);

initializeIcons();
registerIcons({ icons: DEFAULT_COMPONENT_ICONS });

root.render(
  <React.StrictMode>
    <React.Suspense fallback="loading">
      <BrowserRouter basename={appName}>
        <App />
      </BrowserRouter>
    </React.Suspense>
  </React.StrictMode>,
);
