import * as microsoftTeams from '@microsoft/teams-js';
import { triggerUiNotification } from 'src/providers/utils';
import { IActiveLogin } from 'src/types';

/**
 * Initializes the Microsoft Teams SDK if running inside Teams.
 */
export const initTeamsApp = async (): Promise<boolean> => {
  try {
    await microsoftTeams.app.initialize();
    console.log('✅ Microsoft Teams SDK initialized');
    return true;
  } catch (error) {
    console.error('❌ Failed to initialize Teams SDK', error);
    return false;
  }
};

export const isRunningInTeams = async () => {
  try {
    await microsoftTeams.app.initialize();
    const context = await microsoftTeams.app.getContext();
    return !!context;
  } catch (error) {
    console.error('Error checking if running in Teams:', error);
    return false;
  }
};

/**
 * Handles Microsoft Teams Single Sign-On (SSO) using Teams SDK and MSAL.
 * This method should be called only if the app is confirmed to be running inside Teams.
 */
export const handleTeamsSSO = async (): Promise<IActiveLogin | null> => {
  try {
    const teamsInitialized = await initTeamsApp();

    if (!teamsInitialized) throw new Error('Teams SDK init failed');

    return new Promise((resolve, reject) => {
      microsoftTeams.authentication.getAuthToken({
        successCallback: async (token: string) => {
          console.log('🔐 Received Teams SSO token', token);
        },
        failureCallback: (err: any) => {
          triggerUiNotification({
            type: 'error',
            key: 'teams-sso-init',
            message: 'Initializing Teams SSO...',
            description: err,
          });
          console.error('❌ Teams getAuthToken failed', err);
          reject(err);
        },
        resources: [import.meta.env.VITE_MICROSOFT_LOGIN_CLIENT_ID],
      });
    });
  } catch (err) {
    console.error('❌ handleTeamsSSO error', err);
    return null;
  }
};
