import * as React from 'react';
import { IUser } from 'relic-ui';
import { Controller } from 'react-hook-form';
import { FieldValues } from 'react-hook-form';
import { useAutocomplete } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import { useTranslate, IResourceComponentsProps } from '@refinedev/core';
import { useUpdate, useInvalidate, useGetIdentity } from '@refinedev/core';

import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Dialog from '@mui/material/Dialog';
import Switch from '@mui/material/Switch';
import TextField from '@mui/material/TextField';
import LinearProgress from '@mui/material/LinearProgress';
import FormHelperText from '@mui/material/FormHelperText';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import FormControlLabel from '@mui/material/FormControlLabel';

import { useRouter } from 'src/routes/hooks';

import PhoneInput from 'src/components/phone-input';
import { Edit } from 'src/components/refine-customs/edit';
import { LoginProviderDropdown } from 'src/components/dropdown/loginProviderDropdown';
import { OrganizationDropdown } from 'src/components/dropdown/organizationDropdown';

// ----------------------------------------------------------------------

export const StaffEdit: React.FC<IResourceComponentsProps> = () => {
  const router = useRouter();
  const translate = useTranslate();
  const invalidate = useInvalidate();

  const { data: currentUser } = useGetIdentity<IUser>();

  const [open, setOpen] = React.useState(true);

  const handleClose = () => {
    setOpen(false);
    router.push('/staff');
  };

  const {
    watch,
    register,
    control,
    setValue,
    saveButtonProps,
    handleSubmit,
    formState: { errors, isSubmitting },
    refineCore: { query, onFinish },
  } = useForm();

  const values = watch();

  const staffData = query?.data?.data;

  // Initialize useUpdate hook
  const { mutate: updatePractitioner, isLoading: isUpdating } = useUpdate();

  // Custom validation function for email
  const validateEmail = (value: string) => {
    if (values.provider === 'msgraph') {
      if (!value) {
        return 'This field is required';
      }
      const emailPattern = /\S+@\S+\.\S+/;
      return (
        emailPattern.test(value) || 'Entered value does not match email format'
      );
    }
    return true;
  };

  const onSubmit = async (values: FieldValues) => {
    // Ensure the form data is correctly structured
    const practitionerData = {
      id: staffData?.id,
      name: values.name,
      email: values.email,
      mobilePhone: values.mobilePhone,
      organizationId: values.organizationId,
      enabled: values.enabled,
      userPrincipalName: `${staffData?.id}@${staffData?.provider}`,
      provider: values.provider,
    };

    console.log('values getting submitted for update', practitionerData);
    // Use the update method for the "practitioner" resource
    updatePractitioner(
      {
        resource: 'practitioners',
        id: staffData?.id, // Ensure the ID is retrieved from staffData
        values: practitionerData,
      },
      {
        onSuccess: () => {
          invalidate({
            resource: 'staff',
            invalidates: ['list', 'many', 'detail'],
            id: staffData?.id,
          });
          handleClose();
        },
      },
    );
  };

  return (
    <Dialog
      fullWidth
      maxWidth="sm"
      open={open}
      onClose={handleClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <Edit
        onClose={handleClose}
        deleteButtonProps={{
          hidden: staffData?.provider !== 'msgraph',
          confirmTitle: translate('staff.titles.delete'),
          confirmContent: `Are you sure you want to delete ${staffData?.name}?`,
        }}
        saveButtonProps={{
          onClick: handleSubmit(onSubmit),
          disabled: isSubmitting || isUpdating || query?.isLoading,
        }}
        title={translate('staff.titles.edit')}
      >
        {query?.isLoading ? (
          <LinearProgress
            color="inherit"
            sx={{ my: 20, maxWidth: 320, mx: 'auto' }}
          />
        ) : (
          <Stack spacing={2.5} component="form" autoComplete="off">
            <TextField
              {...register('name', {
                required: 'This field is required',
              })}
              error={!!errors?.name}
              helperText={(errors as any)?.name?.message}
              fullWidth
              InputLabelProps={{ shrink: true }}
              type="text"
              disabled={staffData?.provider !== 'msgraph'}
              label={translate('staff.fields.name')}
              name="name"
            />

            <TextField
              {...register('email', {
                validate: validateEmail,
              })}
              error={!!errors?.email}
              helperText={(errors as any)?.email?.message}
              fullWidth
              InputLabelProps={{ shrink: true }}
              type="email"
              label={translate('staff.fields.email')}
              name="email"
            />

            <PhoneInput
              control={control}
              name="mobilePhone"
              label={translate('staff.fields.mobilePhone')}
              value={values?.mobilePhone ?? staffData?.mobilePhone}
            />

            <OrganizationDropdown
              control={control}
              name="organizationId"
              label={translate('staff.fields.organizationId')}
              error={!!(errors as any)?.organizationId}
              helperText={(errors as any)?.organizationId?.message}
              value={staffData?.organizationId}
              disabled
            />

            <LoginProviderDropdown
              control={control}
              name="provider"
              label={translate('staff.fields.provider')}
              error={!!(errors as any)?.provider}
              helperText={(errors as any)?.provider?.message}
              value={staffData?.provider}
              disabled={true}
            />
            <div>
              <FormControlLabel
                label={translate('staff.fields.status')}
                control={
                  <Switch
                    {...register('enabled')}
                    name="enabled"
                    checked={!!values?.enabled}
                  />
                }
              />
              {!!errors.enabled && (
                <FormHelperText error sx={{ px: 2 }}>
                  {(errors as any)?.enabled?.message}
                </FormHelperText>
              )}
            </div>
            <div>
              <FormControlLabel
                control={
                  <Switch
                    {...register('sendInvite')}
                    checked={!!values.sendInvite}
                    name="sendInvite"
                  />
                }
                label={translate('staff.fields.sendInvite')}
              />
              {!!errors.sendInvite && (
                <FormHelperText error sx={{ px: 2 }}>
                  {(errors as any)?.sendInvite?.message}
                </FormHelperText>
              )}
            </div>
          </Stack>
        )}
      </Edit>
    </Dialog>
  );
};
