import {
  useInvalidate,
  useLink,
  useLogin,
  useRouterContext,
  useRouterType,
} from '@refinedev/core';
import { useEffect } from 'react';

import { buttonClasses } from '@mui/material/Button';
import MuiLink from '@mui/material/Link';
import Typography from '@mui/material/Typography';

import { useSearchParams } from 'src/routes/hooks';

import { jwtDecode } from 'src/providers/utils';

import WebsiteHeader from 'src/components/header/websiteHeader';

import { HealthCheckWrapper } from 'src/components/login/health-check-wrapper';
import { AuthPage } from './swizzled';

// ----------------------------------------------------------------------

export const DemoB2CLogin: React.FC = () => {
  const searchParams = useSearchParams();

  const { mutate: login } = useLogin();

  const routerType = useRouterType();

  const Link = useLink();

  const { Link: LegacyLink } = useRouterContext();

  const ActiveLink = routerType === 'legacy' ? LegacyLink : Link;

  const idTokenHint = searchParams.get('id_token_hint');
  const decodedHeader = jwtDecode(idTokenHint as string);
  let tokenHint =
    (decodedHeader?.relicEmail as string) ||
    (decodedHeader?.relicPhone as string) ||
    '';

  const redirectTo = searchParams.get('to')
    ? `${window.location.origin}/demo${searchParams.get('to')}`
    : `${window.location.origin}/demo/check`;

  const identityHint = searchParams.get('identity_hint');
  if (identityHint) {
    tokenHint = identityHint.startsWith('+')
      ? identityHint
      : `+${identityHint}`;
  }

  const invalidate = useInvalidate();

  useEffect(() => {
    if (idTokenHint) {
      login({
        id_token_hint: idTokenHint as string,
        redirectTo: redirectTo,
      });
    }
  }, [idTokenHint, login, redirectTo]);

  useEffect(() => {
    //A better approach will be to obtain all resources using a refine hook and invalidate all of the lists.
    invalidate({
      resource: 'documents',
      invalidates: ['list', 'many'],
    });
    invalidate({
      resource: 'patients',
      invalidates: ['list', 'many'],
    });
    invalidate({
      resource: 'organizations',
      invalidates: ['list', 'many'],
    });
  }, [invalidate]);

  return (
    <>
      <HealthCheckWrapper>
        <WebsiteHeader />
        <AuthPage
          type="login"
          registerLink=""
          rememberMe={false}
          forgotPasswordLink={
            <MuiLink
              to={`${import.meta.env.VITE_SUPPORT_LINK}`}
              target="_blank"
              component={ActiveLink}
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                py: 1,
              }}
            >
              <Typography
                fontSize="14px"
                textOverflow="ellipsis"
                overflow="hidden"
              >
                Need help?
              </Typography>
            </MuiLink>
          }
          title=""
          formProps={{
            defaultValues: {
              id: tokenHint,
              redirectTo: redirectTo,
            },
            // disabled: !!tokenHint,
          }}
          wrapperProps={{
            sx: {
              position: 'relative',
              '&::before': {
                width: 1,
                height: 1,
                zIndex: -1,
                content: "''",
                opacity: 0.24,
                position: 'absolute',
                backgroundSize: 'cover',
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'center center',
                backgroundImage: 'url(/images/overlay_4.jpg)',
              },
            },
          }}
          contentProps={{
            title: 'Login',
            sx: {
              borderRadius: 2,
              boxShadow: theme => theme.customShadows.z1,
              [`& .${buttonClasses.root}`]: {
                mt: 0,
                fontSize: 15,
                height: 'auto',
                padding: '14px 20px',
              },
              '& .MuiTextField-root': {
                marginTop: 1,
              },
              '& .MuiBox-root': {
                marginTop: 2,
              },
              '& .MuiFormControlLabel-root': {
                marginLeft: -1,
              },
              '@media (max-width: 600px)': {
                [`& .${buttonClasses.root}`]: {
                  height: 48,
                },
              },
            },
          }}
          renderContent={(content: React.ReactNode, title: React.ReactNode) => (
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              {title}
              {content}
            </div>
          )}
        />
      </HealthCheckWrapper>
    </>
  );
};
