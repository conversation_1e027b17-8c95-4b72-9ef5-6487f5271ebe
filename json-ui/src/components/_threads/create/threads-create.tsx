import { useState } from 'react';
import { SaveButton } from '@refinedev/mui';
import { FieldValues } from 'react-hook-form';
import { useEffect, useCallback } from 'react';
import { useForm } from '@refinedev/react-hook-form';
import { useList, useCreate, useTranslate } from '@refinedev/core';
import {
  RelicAgent,
  RelicPatient,
  RelicOrganization,
  RelicChatParticipant,
} from 'relic-ui';

import { Send } from '@mui/icons-material';
import { Stack, Dialog, Button, TextField } from '@mui/material';

import { useRouter } from 'src/routes/hooks';

import { Create } from 'src/components/refine-customs/create';
import { ResidentDropdown } from 'src/components/dropdown/residentDropdown';
import InvitationDropdown from 'src/components/dropdown/InvitationDropdown';
import AiParticipantRoleDropdown from 'src/components/dropdown/aiRoleDropdown';
import { OrganizationDropdown } from 'src/components/dropdown/organizationDropdown';

type Props = {
  open: boolean;
  onClose: VoidFunction;
  currentOrganization?: RelicOrganization;
  currentPatient?: RelicPatient;
};

const CreateThread = ({
  open,
  onClose,
  currentOrganization,
  currentPatient,
}: Props) => {
  const translate = useTranslate();
  const router = useRouter();
  const [selectedAgent, setSelectedAgent] = useState<RelicAgent | null>(null);
  const { mutate, isLoading } = useCreate({
    mutationOptions: {
      onSuccess: ({ data }: any) => {
        router.push(
          `/threads/show/${data?.threadId}?threadId=${data?.threadId}&name=${data?.threadSubject?.title}`,
        );
      },
    },
  });
  const {
    watch,
    control,
    register,
    handleSubmit,
    saveButtonProps,
    setValue,
    formState: { errors },
    refineCore: { formLoading },
  } = useForm();

  const values = watch();

  const onSubmit = async (values: FieldValues) => {
    await handleThreadSaveOrInvite(values, selectedAgent);
  };

  const handleSendInvitation = async () => {
    await handleThreadSaveOrInvite(values, selectedAgent);
  };

  const handleThreadSaveOrInvite = async (
    formData: FieldValues,
    selectedAgent: RelicAgent | null,
  ) => {
    try {
      // Retrieve the selected agent from agentList based on the ID in formData
      // const selectedAgent = agentList?.find(
      //   (agent: RelicAgent) => agent.id === formData?.['aiParticipant']?.id,
      // );

      // Extract agent details if available
      const agentDisplayRole = selectedAgent?.role?.['display-role'];

      // Define participants array with both agent and patient id
      const participants: RelicChatParticipant[] = [
        {
          resourceId: selectedAgent?.id,
          resourceType: 'Practitioner',
          role: selectedAgent?.role,
          type: selectedAgent?.type as "Patient Agent" | "Staff Agent" | "System Agent" | undefined,
          displayName: selectedAgent?.name,
          id: {
            communicationUserId: selectedAgent?.communicationIdentities?.[0]
              ?.userId as string,
          },
        },
        {
          resourceId: formData?.patientId,
          resourceType: 'Patient',
          displayName: currentPatient?.name,
          id: {
            communicationUserId: currentPatient?.communicationIdentities?.[0]
              ?.userId as string,
          },
        },
      ];

      console.log('participantsList:', participants);

      const threadSubject = {
        organizationId: formData.organizationId,
        type: 'Default',
        title: formData.title,
        patientLanguage: currentPatient?.primaryLanguage,
      };

      // Determine conversation type based on agent's display role
      const conversationCategory =
        agentDisplayRole === 'Clinical assistant'
          ? 'Caregiver_Practitioner'
          : 'Default_Practitioner';

      // Build data object with necessary details for the mutation
      const mutationPayload = {
        subject: threadSubject,
        participants: participants,
        inviteVia: formData.invitation ? formData.invitation : 'none', // Conditional invitation method
        receiverEmail: formData.email,
        receiverPhone: formData.cellphone,
        type: conversationCategory,
      };

      // Execute the mutation with constructed payload (commented out for testing)
      mutate({
        resource: 'communication/chat/threads',
        values: mutationPayload,
      });

      console.log(mutationPayload);
    } catch (error) {
      // Provide an error message for any failure in processing
      console.error('Error in handleThreadSaveOrInvite:', error);
    }
  };

  const { data: patientData } = useList<RelicPatient>({
    resource: 'patients',
    queryOptions: {
      enabled: !currentPatient,
    },
  });

  const handlePatientChange = useCallback(
    (value: string) => {
      setValue('patientId', value);
      if (currentPatient && currentPatient.id === value) {
        setValue('cellphone', currentPatient?.mobilePhone);
        setValue('email', currentPatient?.email);
        return;
      }
      const selectedPatient =
        patientData?.data?.find(p => p.id === value) || null;
      setValue('cellphone', selectedPatient?.mobilePhone);
      setValue('email', selectedPatient?.email);
    },
    [patientData, currentPatient, setValue],
  );

  useEffect(() => {
    if (currentPatient) {
      handlePatientChange(currentPatient.id);
    }
  }, [currentPatient, handlePatientChange]);

  return (
    <Dialog
      fullWidth
      maxWidth="sm"
      open={open}
      onClose={onClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <Create
        isLoading={formLoading || isLoading}
        saveButtonProps={{
          ...saveButtonProps,
          onClick: handleSubmit(onSubmit),
        }}
        footerButtons={({ saveButtonProps }) => (
          <>
            {onClose && (
              <Button variant="outlined" onClick={onClose}>
                Cancel
              </Button>
            )}
            <Button variant="outlined" onClick={handleSendInvitation}>
              <Send fontSize="small" sx={{ mr: 1 }} />
              Send Invitation
            </Button>
            <SaveButton {...saveButtonProps} />
          </>
        )}
        title={translate('threads.titles.create')}
        onClose={onClose}
      >
        <Stack component="form" spacing={2.5} autoComplete="off">
          <TextField
            {...register('title', {
              required: 'This field is required',
            })}
            error={!!(errors as any)?.title}
            helperText={(errors as any)?.title?.message}
            margin="normal"
            required
            fullWidth
            InputLabelProps={{ shrink: true }}
            type="text"
            label={translate('threads.fields.title')}
            name="title"
          />
          <OrganizationDropdown
            control={control}
            name="organizationId"
            label={translate('threads.fields.organization')}
            value={currentOrganization?.id ?? null}
            error={!!(errors as any)?.organizationId}
            helperText={(errors as any)?.organizationId?.message}
            disabled={!!currentOrganization}
          />
          <ResidentDropdown
            control={control}
            name="patientId"
            label={translate('threads.fields.resident')}
            value={currentPatient ? (currentPatient.id as string) : ''}
            error={!!(errors as any)?.patientId}
            helperText={(errors as any)?.patientId?.message}
            onChange={handlePatientChange}
            disabled={!!currentPatient}
          />
          <AiParticipantRoleDropdown
            control={control}
            name="aiParticipant"
            label={translate('threads.fields.ai-participant')}
            error={!!(errors as any)?.aiParticipant}
            helperText={(errors as any)?.aiParticipant?.message}
            organizationId={
              currentOrganization
                ? currentOrganization.id
                : values.organizationId
            }
            currentPatientId={values.patientId ?? currentPatient?.id}
            value={values.aiParticipant ?? null}
            setSelectedAgent={setSelectedAgent}
          />
          <InvitationDropdown
            control={control}
            name="invitation"
            label={translate('threads.fields.invitation')}
            value={values.invitation ?? 'email'}
            error={!!(errors as any)?.invitation}
            helperText={(errors as any)?.invitation?.message}
          />
          {(values.invitation === 'cellphone' ||
            values.invitation === 'both') && (
            <TextField
              {...register('cellphone', {
                required: 'This field is required',
              })}
              error={!!(errors as any)?.cellphone}
              helperText={(errors as any)?.cellphone?.message}
              margin="normal"
              fullWidth
              InputLabelProps={{ shrink: true }}
              type="text"
              label={translate('threads.fields.cellphone')}
              name="cellphone"
            />
          )}
          {(values.invitation === 'email' || values.invitation === 'both') && (
            <TextField
              {...register('email', {
                required: 'This field is required',
              })}
              error={!!(errors as any)?.email}
              helperText={(errors as any)?.email?.message}
              margin="normal"
              fullWidth
              InputLabelProps={{ shrink: true }}
              type="text"
              label={translate('threads.fields.email')}
              name="email"
            />
          )}
        </Stack>
      </Create>
    </Dialog>
  );
};

export default CreateThread;
