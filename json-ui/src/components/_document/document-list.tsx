import { List, DateField, RefreshButton } from '@refinedev/mui';
import React, { useMemo, useState, useEffect, useCallback } from 'react';
import {
  RelicPatient,
  RelicDocument,
  availableLanguages,
  RelicCommunicationLanguage,
  IUserIdentity,
} from 'relic-ui';
import {
  useMany,
  useOnError,
  CrudFilter,
  useTranslate,
  useInvalidate,
  DeleteOneResponse,
} from '@refinedev/core';

import DeleteIcon from '@mui/icons-material/Delete';
import { Skeleton, Typography, useMediaQuery } from '@mui/material';
import {
  GridColDef,
  DataGridPro,
  gridClasses,
  GridGroupNode,
  useGridApiRef,
  GridCellParams,
  DataGridProProps,
} from '@mui/x-data-grid-pro';

import { useSearch } from 'src/hooks/use-search';

import { RowActionsPopover } from 'src/components/list';
import { NewButton } from 'src/components/refine-customs/new-btn';
import { DataGridToolbar } from 'src/components/list/data-grid-toolbar';

import EditDocument from './document-edit';
import TranslateDocument from './document-translate';

type SetFilterBehavior = 'merge' | 'replace';

interface DocumentListProps extends DataGridProProps {
  patient?: RelicPatient;
  currentIdentity?: IUserIdentity;
  refetchDocuments?: VoidFunction;
  setFilters: ((filters: CrudFilter[], behavior?: SetFilterBehavior) => void) &
    ((setter: (prevFilters: CrudFilter[]) => CrudFilter[]) => void);
}

export default function ListDocuments({
  patient,
  currentIdentity,
  setFilters,
  refetchDocuments,
  ...dataGridProps
}: DocumentListProps) {
  const translate = useTranslate();

  const invalidate = useInvalidate();

  const { globalSearch } = useSearch();

  const { mutate: onError } = useOnError();

  const apiRef = useGridApiRef();

  const isSmallScreen = useMediaQuery('(max-width:600px)');

  const [editDocumentOpen, setEditDocumentOpen] = useState(false);

  const [translateDocumentOpen, setTranslateDocumentOpen] = useState(false);

  const [refetchNeeded, setRefetchNeeded] = useState(false);

  const [selectedDocument, setSelectedDocument] =
    useState<RelicDocument | null>(null);

  const [expandedRowIds, setExpandedRowIds] = useState<Set<string>>(new Set());

  const handleRefresh = async () => {
    // await invalidate({
    //   resource: 'documents',
    //   invalidates: ['list'],
    // });
    // apiRef.current?.forceUpdate();
    refetchDocuments?.();
  };

  const handleOpenTranslateDocument = (document: RelicDocument) => {
    setSelectedDocument(document);
    setTranslateDocumentOpen(true);
    if (document.sourceDocumentId !== undefined) {
      setExpandedRowIds(prev =>
        new Set(prev).add(document.sourceDocumentId as string),
      );
    }
    if (!document.url) {
      setRefetchNeeded(true);
    }
  };

  const handleCloseTranslateDocument = () => {
    refetchNeeded && refetchDocuments?.();
    setSelectedDocument(null);
    setTranslateDocumentOpen(false);
  };

  const handleOpenEditDocument = (document: RelicDocument) => {
    setSelectedDocument(document);
    setEditDocumentOpen(true);
    if (document.sourceDocumentId !== undefined) {
      setExpandedRowIds(prev =>
        new Set(prev).add(document.sourceDocumentId as string),
      );
    }
  };

  const handleCloseEditDocument = () => {
    setEditDocumentOpen(false);
  };

  const handleRetry = useCallback((row: RelicDocument) => {
    handleOpenTranslateDocument(row);
  }, []);

  const handleDelete = (value: DeleteOneResponse) => {
    const deletedDocument: RelicDocument = value as RelicDocument;
    setExpandedRowIds(prev => {
      const newExpanded = new Set(prev);
      if (deletedDocument?.sourceDocumentId) {
        newExpanded.add(deletedDocument.sourceDocumentId);
      }
      return newExpanded;
    });
  };

  function expandGroup(node: GridGroupNode) {
    return expandedRowIds.has(node.id as string);
  }

  // Search Filter
  useEffect(() => {
    try {
      if (globalSearch.length > 0) {
        setFilters([
          {
            field: 'search',
            value: globalSearch.length > 0 ? globalSearch : '',
            operator: 'contains',
          },
        ]);
      } else {
        setFilters([]);
      }
    } catch (error) {
      onError(error);
    }
  }, [globalSearch]);

  // Obtain org details for the documents
  const { data: relatedOrganizations, isSuccess: areOrgsLoaded } = useMany({
    resource: 'organizations',
    ids: dataGridProps?.rows?.map((item: any) => item?.organizationId) ?? [],
    queryOptions: {
      enabled: dataGridProps?.loading === false,
    },
  });

  // Cell Click Handler
  const handleOnCellClick = React.useCallback((params: GridCellParams) => {
    if (params.field !== 'actions') {
      if (params.rowNode.type === 'group') {
        handleOpenTranslateDocument(params.row as RelicDocument);
      } else {
        handleRetry(params.row as RelicDocument);
      }
    }
  }, []);

  // Toolbar for Document Listing Page
  const [viewButtonEl, setViewButtonEl] =
    React.useState<HTMLButtonElement | null>(null);

  const Toolbar = React.useCallback(
    () => (
      <DataGridToolbar
        title={translate('Documents')}
        exportButton={false}
        actions={
          <>
            <RefreshButton onClick={handleRefresh} />
            <NewButton
              href={'/documents/create'}
              label={translate('buttons.create')}
            />{' '}
          </>
        }
      />
    ),
    [translate],
  );

  const deleteButtonProps = useCallback(
    (row: RelicDocument) => {
      const isLeafNode =
        row.sourceDocumentId && row.sourceDocumentId !== row.id;
      const leafNodes = dataGridProps?.rows?.filter(
        leafRow => leafRow.sourceDocumentId === row.id,
      ).length;
      const leafNodesCount = leafNodes ? leafNodes - 1 : 0;

      const getLanguageDisplay = (code: string) => {
        const language = availableLanguages.find(
          (lang: RelicCommunicationLanguage) => lang.code === code,
        );
        return language ? language.display : code;
      };

      const confirmContent = isLeafNode
        ? `This document is "${getLanguageDisplay(row.language)}" translation of ${
            row.filename
          }. Are you sure you want to delete it?`
        : leafNodesCount && leafNodesCount > 0
          ? `This file has ${leafNodesCount} translated files which will also be deleted as a result. Are you sure you want to delete it?`
          : `Are you sure you want to delete ${row.filename}?`;

      return {
        confirmTitle: translate('documents.titles.delete'),
        confirmContent,
        successNotification: {
          message: translate(`Successfully Deleted ${row.filename}`, {}),
          type: 'success' as const,
        },
        buttonProps: {
          variant: 'outlined',
          color: 'error',
          startIcon: <DeleteIcon />,
        },
        dialogProps: {
          PaperProps: {
            sx: {
              padding: 2,
              borderRadius: 1,
            },
          },
        },
        resource: 'documents',
        recordItemId: row.id,
      };
    },
    [dataGridProps?.rows, translate],
  );

  // Columns to be displayed
  const columns = React.useMemo<GridColDef[]>(
    () => [
      {
        field: 'language',
        headerName: translate('documents.fields.language'),
        flex: 1,
        hide: isSmallScreen,
        renderCell: function render({ value }) {
          return (
            availableLanguages.find(
              (lang: RelicCommunicationLanguage) => lang.code === value,
            )?.display || 'English-US'
          );
        },
      },
      {
        field: 'organizationId',
        headerName: translate('documents.fields.organizationName'),
        flex: 1,
        minWidth: 200,
        hide: true,
        renderCell: function render({ value }) {
          return areOrgsLoaded ? (
            relatedOrganizations?.data?.find(item => item.id === value)?.name ||
              '-'
          ) : (
            <Skeleton
              variant="rectangular"
              sx={{ width: 0.5, height: 16, borderRadius: 0.5 }}
            />
          );
        },
      },
      {
        field: 'status',
        headerName: 'Status',
        flex: 1,
        minWidth: 200,
        renderCell: function render({ row, api, value }) {
          const rowNode = api.getRowNode(row.id);
          const isLeaf = rowNode && rowNode?.depth > 0;

          if (!isLeaf) {
            return '--';
          }
          let color;
          let text;

          switch (value) {
            case 'pending':
              color = '#FFA500'; // Orange for Pending Translation
              text = 'Pending Translation';
              break;
            case 'failed':
              color = '#FF4C4C'; // Soft Red for Failed Translation
              text = 'Failed Translation';
              break;
            case 'done':
              color = '#4CAF50'; // Green for Completed Translation
              text = 'Completed Translation';
              break;
            case 'inprogress':
              color = '#1E90FF'; // Blue for In Progress
              text = 'In Progress';
              break;
            default:
              color = '#A9A9A9'; // Light Gray for Unknown Status
              text = value?.toUpperCase() || 'UNKNOWN STATUS';
          }

          return (
            <Typography variant="body2" sx={{ color: color }}>
              {text}
            </Typography>
          );
        },
      },
      {
        field: 'createDate',
        headerName: translate('documents.fields.dateCreated'),
        flex: 1,
        renderCell: function render({ value }) {
          return (
            <DateField
              sx={{ display: 'inline' }}
              value={value}
              format="MMMM DD YYYY, h:mm a"
            />
          );
        },
        hide: isSmallScreen,
      },
      {
        field: 'actions',
        headerName: translate('table.actions'),
        sortable: false,
        renderCell: function render({ row, api }) {
          const rowNode = api.getRowNode(row.id);
          const isLeaf = rowNode && rowNode?.depth > 0;
          return (
            <RowActionsPopover
              rowId={row.id}
              translateButtonProps={
                isLeaf
                  ? undefined
                  : {
                      onConfirm: () => handleOpenTranslateDocument(row),
                    }
              }
              retryButtonProps={
                isLeaf && row.status === 'failed'
                  ? {
                      onConfirm: () => handleRetry(row),
                    }
                  : undefined
              }
              deleteButtonProps={
                row.status !== 'pending'
                  ? {
                      ...deleteButtonProps(row),
                      onSuccess: handleDelete,
                    }
                  : undefined
              }
              editButtonProps={
                rowNode && rowNode?.depth === 0
                  ? {
                      onClick: () => handleOpenEditDocument(row),
                      children: translate('documents.titles.edit'),
                    }
                  : undefined
              }
            />
          );
        },
        align: 'center',
        headerAlign: 'center',
        minWidth: 80,
      },
    ],
    [
      translate,
      isSmallScreen,
      areOrgsLoaded,
      relatedOrganizations?.data,
      deleteButtonProps,
      handleRetry,
    ],
  );

  // Filter top-level documents
  const topLevelDocuments = useMemo(
    () => dataGridProps.rows?.filter(row => row.sourceDocumentId === row.id),
    [dataGridProps.rows],
  );

  const displayDocuments = useMemo(() => {
    if (!topLevelDocuments) return [];

    // Create a document map to group top-level and leaf documents
    const documentMap = new Map<string, RelicDocument[]>();

    // Add top-level documents to the map
    topLevelDocuments.forEach(row => {
      documentMap.set(row.id, [row]);
    });

    // Add leaf nodes to their respective parent documents
    dataGridProps.rows?.forEach(row => {
      if (row.sourceDocumentId && row.sourceDocumentId !== row.id) {
        const parentDocs = documentMap.get(row.sourceDocumentId);
        if (parentDocs) {
          parentDocs.push(row);
        }
      }
    });

    // Flatten the map values into a single array of documents
    return Array.from(documentMap.values()).flat();
  }, [dataGridProps.rows, topLevelDocuments]);

  const displayColumns = useMemo(() => {
    // Filter or select columns based on screen size
    if (isSmallScreen) {
      return columns.filter(
        column =>
          column.field === 'documentName' ||
          column.field === 'status' ||
          column.field === 'actions',
      );
    }
    if ((patient && patient.id) || (currentIdentity && currentIdentity.id)) {
      return columns.filter(
        column =>
          column.field === 'documentName' ||
          column.field === 'language' ||
          column.field === 'status' ||
          column.field === 'createDate' ||
          column.field === 'actions',
      );
    }
    return columns;
  }, [columns, currentIdentity, isSmallScreen, patient]);

  // Render the grid for Documents Page
  function renderGrid() {
    return (
      <>
        <DataGridPro
          {...dataGridProps}
          apiRef={apiRef}
          rows={displayDocuments}
          filterMode="client"
          isGroupExpandedByDefault={expandGroup}
          sx={{
            '& .MuiDataGrid-cell': {
              display: 'flex',
              alignSelf: 'center', // Center aligns the items vertically
              height: '100%', // Ensure the cell takes full height for vertical centering,
              border: 'none',
            },
          }}
          getRowId={row => row.id}
          getTreeDataPath={row => {
            // Check if the document is not in English or does not have a sourceDocumentId
            if (
              row.language &&
              (row.language.toUpperCase() === 'EN' ||
                row.language.toUpperCase() === 'EN-US')
            ) {
              return [row.id.toString()]; // Root level for 'EN' or 'EN-US'
            } else if (row.sourceDocumentId) {
              return [row.sourceDocumentId.toString(), row.id.toString()]; // Nested for other languages
            }
            // English documents or documents without a sourceDocumentId appear at the top level
            return [row.id.toString()];
          }}
          groupingColDef={{
            headerName: 'Document Name',
            minWidth: 300,
            valueGetter: (params, row) => row.filename,
          }}
          treeData
          columns={displayColumns}
          onCellClick={handleOnCellClick}
          autoHeight
          disableColumnMenu
          localeText={{ toolbarColumns: 'View' }}
          slots={
            (patient && patient.id) || (currentIdentity && currentIdentity.id)
              ? undefined
              : {
                  toolbar: Toolbar,
                }
          }
          slotProps={
            (patient && patient.id) || (currentIdentity && currentIdentity.id)
              ? undefined
              : {
                  panel: {
                    anchorEl: viewButtonEl,
                  },
                  toolbar: {
                    onClick: (e: React.MouseEvent) => {
                      e.stopPropagation();
                      setViewButtonEl(null);
                    },
                  },
                }
          }
        />
      </>
    );
  }

  // Render the grid for Patients Tab
  function renderGridTransHeader() {
    return (
      <List
        headerProps={{
          sx: {
            display: 'none',
          },
        }}
        contentProps={{
          sx: {
            p: '0 !important',
          },
        }}
        breadcrumb={null}
        wrapperProps={{
          sx: {
            boxShadow: 'none',
            background: 'transparent',
            [`& .${gridClasses.root}`]: {
              [`& .${gridClasses.cell}`]: {
                py: 1,
              },
              [`& .${gridClasses.columnHeader}`]: {
                bgcolor: 'transparent',
              },
              [`& .${gridClasses.columnHeaders}`]: {
                bgcolor: 'transparent',
                borderBottomStyle: 'dashed',
              },
            },
          },
        }}
      >
        {renderGrid()}
      </List>
    );
  }

  return (
    <>
      {(patient && patient.id) || (currentIdentity && currentIdentity.id)
        ? renderGridTransHeader()
        : renderGrid()}
      {editDocumentOpen && selectedDocument && (
        <EditDocument
          open={editDocumentOpen}
          onClose={handleCloseEditDocument}
          documentDetails={selectedDocument}
        />
      )}
      {translateDocumentOpen && selectedDocument && (
        <TranslateDocument
          open={translateDocumentOpen}
          onClose={handleCloseTranslateDocument}
          selectedDocument={selectedDocument}
          sourceDocument={dataGridProps?.rows?.find(
            row => row.id === selectedDocument.sourceDocumentId,
          )}
          patient={patient}
          leafNodes={
            dataGridProps &&
            dataGridProps.rows?.filter(
              row => row.sourceDocumentId === selectedDocument.id,
            )
          }
        />
      )}
    </>
  );
}
