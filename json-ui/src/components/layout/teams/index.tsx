import { useResourceParams } from '@refinedev/core';
import type { RefineThemedLayoutV2Props } from '@refinedev/mui';
import { ThemedLayoutContextProvider } from '@refinedev/mui';
import React from 'react';

import { useMediaQuery, useTheme } from '@mui/material';
import Box from '@mui/material/Box';

import { ModernHeaderV2 } from './header';
import { ModernSiderV2 } from './sider';

export const TeamsAppLayout: React.FC<RefineThemedLayoutV2Props> = ({
  Sider,
  Header,
  Title,
  Footer,
  OffLayoutArea,
  children,
  initialSiderCollapsed,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'));

  const SiderToRender = Sider ?? ModernSiderV2;
  const HeaderToRender = Header ?? ModernHeaderV2;
  const { resource, action } = useResourceParams();

  return (
    <ThemedLayoutContextProvider initialSiderCollapsed={initialSiderCollapsed}>
      <Box sx={{ display: 'flex', minHeight: '100vh' }}>
        {/* Header always fixed at top */}
        <HeaderToRender Title={Title} />

        {/* Mobile Sidebar - rendered as drawer */}
        {isMobile && <SiderToRender Title={Title} />}

        {/* Main Content Area */}
        <Box
          component="main"
          sx={{
            p: { xs: 1, md: 2, lg: 3 },
            flexGrow: 1,
            height: 'calc(100vh - 64px)',
            bgcolor: theme => theme.palette.background.default,
          }}
        >
          {children}
          {Footer && <Footer />}
        </Box>

        {OffLayoutArea && <OffLayoutArea />}
      </Box>
    </ThemedLayoutContextProvider>
  );
};
