import { useGetIdentity, useGo, useMenu } from '@refinedev/core';
import type { RefineThemedLayoutV2SiderProps } from '@refinedev/mui';
import { useThemedLayoutContext } from '@refinedev/mui';
import React from 'react';

import {
  Box,
  Collapse,
  Drawer,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  SvgIcon,
  Typography,
  useMediaQuery,
  useTheme,
} from '@mui/material';

import { ExpandLess, ExpandMore, Home } from '@mui/icons-material';

import { OrganizationIcon } from '../../icon/OrganizationIcon';

const drawerWidth = 280;

export const ModernSiderV2: React.FC<RefineThemedLayoutV2SiderProps> = ({
  meta,
}) => {
  const { data: user } = useGetIdentity();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'));
  const go = useGo();

  // Get dynamic menu items
  const { menuItems } = useMenu({ meta });

  const { mobileSiderOpen, setMobileSiderOpen } = useThemedLayoutContext();

  // Mobile sidebar collapse states - using dynamic menu keys
  const [openSections, setOpenSections] = React.useState<{
    [key: string]: boolean;
  }>({});

  // Filter parent menu items (those with children)
  const parentMenuItems = menuItems.filter(
    item => item.children && item.children.length > 0,
  );

  const handleSectionToggle = (key: string) => {
    setOpenSections(prev => ({ ...prev, [key]: !prev[key] }));
  };

  // Mobile sidebar content
  const sidebarContent = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Sidebar Header */}
      <Box sx={{ p: 1, borderBottom: '1px solid', borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <SvgIcon sx={{ mr: 2, color: 'primary.main', fontSize: 28 }}>
            <OrganizationIcon />
          </SvgIcon>
          <Typography variant="h6" fontWeight={700}>
            Relic AI Portal
          </Typography>
        </Box>
      </Box>
      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        <List sx={{ px: 2, py: 1 }}>
          {/* Dashboard Home */}
          <ListItemButton
            sx={{ borderRadius: 2, mb: 1 }}
            onClick={() => go({ to: '/' })}
          >
            <ListItemIcon>
              <Home />
            </ListItemIcon>
            <ListItemText
              primary="Dashboard Home"
              primaryTypographyProps={{
                fontSize: '14px', // Match existing sider font size
                noWrap: true,
              }}
            />
          </ListItemButton>

          {/* Dynamic Parent Menu Items */}
          {parentMenuItems.map(parentItem => (
            <React.Fragment key={parentItem.key}>
              <ListItemButton
                onClick={() => handleSectionToggle(parentItem.key)}
                sx={{ borderRadius: 2, mb: 1 }}
              >
                <ListItemIcon>{parentItem.icon}</ListItemIcon>
                <ListItemText
                  primary={parentItem.label}
                  primaryTypographyProps={{
                    fontSize: '14px', // Match existing sider font size
                    noWrap: true,
                  }}
                />
                {openSections[parentItem.key] ? <ExpandLess /> : <ExpandMore />}
              </ListItemButton>
              <Collapse
                in={openSections[parentItem.key]}
                timeout="auto"
                unmountOnExit
              >
                <List component="div" disablePadding>
                  {parentItem.children.map(child => (
                    <ListItemButton
                      key={child.key}
                      sx={{ pl: 4, borderRadius: 2, mx: 1, mb: 0.5 }}
                      onClick={() => {
                        if (child.route) {
                          go({ to: child.route });
                        }
                        setMobileSiderOpen(false); // Close mobile drawer after navigation
                      }}
                    >
                      <ListItemIcon sx={{ minWidth: 36 }}>
                        {child.icon}
                      </ListItemIcon>
                      <ListItemText
                        primary={child.label}
                        primaryTypographyProps={{
                          fontSize: '14px', // Match existing sider font size
                          noWrap: true,
                        }}
                      />
                    </ListItemButton>
                  ))}
                </List>
              </Collapse>
            </React.Fragment>
          ))}
        </List>
      </Box>
    </Box>
  );

  // Only render mobile drawer on mobile devices
  if (!isMobile) {
    return null;
  }

  return (
    <Drawer
      variant="temporary"
      open={mobileSiderOpen}
      onClose={() => setMobileSiderOpen(false)}
      ModalProps={{ keepMounted: true }}
      sx={{
        '& .MuiDrawer-paper': {
          boxSizing: 'border-box',
          width: drawerWidth,
        },
      }}
    >
      {sidebarContent}
    </Drawer>
  );
};
