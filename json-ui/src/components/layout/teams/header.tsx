import {
  pickNotDeprecated,
  useActiveAuthProvider,
  useGetIdentity,
  useGo,
  useLogout,
  useMenu,
  useTranslate,
  useWarnAboutChange,
} from '@refinedev/core';
import type { RefineThemedLayoutV2HeaderProps } from '@refinedev/mui';
import PopupState, { bindMenu, bindTrigger } from 'material-ui-popup-state';
import React from 'react';

import LogoutIcon from '@mui/icons-material/Logout';
import {
  AppBar,
  Avatar,
  Box,
  Button,
  ListItemIcon,
  ListItemText,
  Menu,
  MenuItem,
  Stack,
  SvgIcon,
  Toolbar,
  Typography,
  useMediaQuery,
  useTheme,
} from '@mui/material';

import { OrganizationIcon } from '../../icon/OrganizationIcon';
import { HamburgerMenu } from '../hamburgerMenu';

export const ModernHeaderV2: React.FC<
  RefineThemedLayoutV2HeaderProps & { Title?: React.ComponentType<any> }
> = ({ isSticky, sticky, Title }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'));
  const authProvider = useActiveAuthProvider();
  const go = useGo();
  const translate = useTranslate();
  const { data: user } = useGetIdentity({
    v3LegacyAuthProviderCompatible: Boolean(authProvider?.isLegacy),
  });

  const { mutate: mutateLogout } = useLogout({
    v3LegacyAuthProviderCompatible: Boolean(authProvider?.isLegacy),
  });
  // Get dynamic menu items from useMenu
  const { menuItems } = useMenu();

  const prefferedSticky = pickNotDeprecated(sticky, isSticky) ?? true;

  // Filter parent menu items (those with children)
  const parentMenuItems = menuItems.filter(
    item => item.children && item.children.length > 0,
  );

  const { warnWhen, setWarnWhen } = useWarnAboutChange();
  const handleLogout = () => {
    if (warnWhen) {
      const confirm = window.confirm(
        translate(
          'warnWhenUnsavedChanges',
          'Are you sure you want to leave? You have unsaved changes.',
        ),
      );

      if (confirm) {
        setWarnWhen(false);
        mutateLogout();
      }
    } else {
      mutateLogout();
    }
  };

  // Desktop dropdown component using PopupState
  const DesktopDropdown = ({ menuItem }: { menuItem: any }) => (
    <PopupState variant="popover" popupId={`menu-${menuItem.key}`}>
      {popupState => (
        <React.Fragment>
          <Button
            {...bindTrigger(popupState)}
            startIcon={menuItem.icon}
            sx={{
              color: 'inherit',
              textTransform: 'none',
              fontWeight: 500,
              px: 2,
              py: 1,
              borderRadius: 2,
              fontSize: '14px',
              '&:hover': { bgcolor: 'rgba(0,0,0,0.04)' },
            }}
          >
            {menuItem.label}
          </Button>
          <Menu
            {...bindMenu(popupState)}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'left',
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'left',
            }}
            sx={{
              '& .MuiPaper-root': {
                mt: 1,
                minWidth: 250,
                boxShadow: theme.shadows[8],
              },
            }}
          >
            {menuItem.children.map((child: any) => (
              <MenuItem
                key={child.key}
                onClick={() => {
                  if (child.route) {
                    go({ to: child.route });
                  }
                  popupState.close();
                }}
              >
                <ListItemIcon sx={{ minWidth: 36 }}>{child.icon}</ListItemIcon>
                <ListItemText
                  primary={child.label}
                  primaryTypographyProps={{
                    fontSize: '14px',
                  }}
                />
              </MenuItem>
            ))}
          </Menu>
        </React.Fragment>
      )}
    </PopupState>
  );

  return (
    <AppBar
      position={prefferedSticky ? 'fixed' : 'relative'}
      elevation={0}
      sx={{
        bgcolor: 'background.paper',
        color: 'text.primary',
        borderBottom: '1px solid',
        borderColor: 'divider',
        zIndex: theme.zIndex.drawer + 1,
      }}
    >
      <Toolbar sx={{ px: { xs: 2, sm: 3 } }}>
        {/* Mobile Menu Button */}
        {isMobile && <HamburgerMenu />}

        {/* Logo and Brand */}
        <Box
          sx={{ display: 'flex', alignItems: 'center', mr: { xs: 2, lg: 4 } }}
        >
          <SvgIcon sx={{ mr: 1.5, color: 'primary.main', fontSize: 28 }}>
            <OrganizationIcon />
          </SvgIcon>
          <Typography
            variant="h6"
            component="div"
            fontWeight={700}
            sx={{ display: { xs: 'none', sm: 'block' } }}
          >
            Relic AI Portal
          </Typography>
        </Box>

        {/* Desktop Navigation */}
        {!isMobile && (
          <Box
            sx={{ display: 'flex', alignItems: 'center', flexGrow: 1, gap: 1 }}
          >
            {parentMenuItems.map(menuItem => (
              <DesktopDropdown key={menuItem.key} menuItem={menuItem} />
            ))}
          </Box>
        )}

        <Box sx={{ flexGrow: 1 }} />

        {/* Right Side Actions */}
        <Stack direction="row" spacing={1} alignItems="center">
          {/* User Menu */}
          <PopupState variant="popover" popupId="user-menu">
            {popupState => (
              <React.Fragment>
                <Box
                  {...bindTrigger(popupState)}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    cursor: 'pointer',
                    padding: '4px 8px',
                    borderRadius: '8px',
                    '&:hover': {
                      backgroundColor: 'rgba(0, 0, 0, 0.04)',
                    },
                  }}
                >
                  {user?.name && (
                    <Typography
                      variant="subtitle2"
                      data-testid="header-user-name"
                    >
                      {user?.name}
                    </Typography>
                  )}
                  {user?.avatar && (
                    <Avatar src={user?.avatar} alt={user?.name} />
                  )}
                </Box>
                <Menu
                  {...bindMenu(popupState)}
                  anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right',
                  }}
                  transformOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                  }}
                >
                  <MenuItem
                    onClick={() => {
                      popupState.close();
                      mutateLogout();
                    }}
                  >
                    <ListItemIcon>
                      <LogoutIcon />
                    </ListItemIcon>
                    <ListItemText>Sign Out</ListItemText>
                  </MenuItem>
                </Menu>
              </React.Fragment>
            )}
          </PopupState>
        </Stack>
      </Toolbar>
    </AppBar>
  );
};
