import { useResourceParams } from '@refinedev/core';
import type { RefineThemedLayoutV2Props } from '@refinedev/mui';
import { ThemedLayoutContextProvider } from '@refinedev/mui';
import React from 'react';

import { useMediaQuery, useTheme } from '@mui/material';
import Box from '@mui/material/Box';

import { ModernHeaderV2 } from './header';
import { ModernSiderV2 } from './sider';

export const ModernTeamsLayoutV2: React.FC<RefineThemedLayoutV2Props> = ({
  Sider,
  Header,
  Title,
  Footer,
  OffLayoutArea,
  children,
  initialSiderCollapsed,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'));

  const SiderToRender = Sider ?? ModernSiderV2;
  const HeaderToRender = Header ?? ModernHeaderV2;
  const { resource, action } = useResourceParams();

  return (
    <ThemedLayoutContextProvider initialSiderCollapsed={initialSiderCollapsed}>
      <Box sx={{ display: 'flex', minHeight: '100vh' }}>
        {/* Header always fixed at top */}
        <HeaderToRender Title={Title} />

        {/* Mobile Sidebar - rendered as drawer */}
        {isMobile && <SiderToRender Title={Title} />}

        {/* Main Content Area */}
        <Box
          component="main"
          sx={{
            flexGrow: 1,
            bgcolor: 'grey.50',
            minHeight: '100vh',
            pt: { xs: 8, sm: 8 }, // Account for fixed header
          }}
        >
          {children}
          {Footer && <Footer />}
        </Box>

        {OffLayoutArea && <OffLayoutArea />}
      </Box>
    </ThemedLayoutContextProvider>
  );
};
