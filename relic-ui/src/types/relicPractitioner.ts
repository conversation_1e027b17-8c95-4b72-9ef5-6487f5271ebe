import { Type, Static } from '@sinclair/typebox';
import { CommunicationIdentitySchema, CommunicationIdentity } from './communication'; // import CommunicationIdentity
import { IRoleSchema } from './portal';
import { IdentityProviderSchema } from './relicBase';

// RelicPractitioner schema and type
export const RelicPractitionerSchema = Type.Object({
  id: Type.String(),
  name: Type.String(),
  resourceType: Type.Literal('Practitioner'),
  enabled: Type.Boolean(),
  organizationId: Type.String(),
  communicationIdentities: Type.Optional(Type.Array(CommunicationIdentitySchema)), 
  role: IRoleSchema,
  email: Type.Optional(Type.Union([Type.String(), Type.Null()])), // Because in MongoDB, it is already initialized as null.
  mobilePhone: Type.Optional(Type.String()),
  provider: Type.Optional(IdentityProviderSchema), // Replace Type.Any() with IdentityProviderSchema if available
  position: Type.Optional(Type.String()),
});

// Use CommunicationIdentity[] for the type
export type RelicPractitioner = Omit<Static<typeof RelicPractitionerSchema>, 'communicationIdentities'> & {
  communicationIdentities?: CommunicationIdentity[];
};

// PractitionerFilters schema and type
export const PractitionerFiltersSchema = Type.Object({
  id: Type.Optional(Type.Object({ $in: Type.Array(Type.String()) })),
  organizationId: Type.Optional(Type.String()),
  active: Type.Optional(Type.Boolean()),
  name: Type.Optional(Type.String()),
  email: Type.Optional(Type.String()),
  mobilePhone: Type.Optional(Type.String()),
});
export type PractitionerFilters = Static<typeof PractitionerFiltersSchema>;
