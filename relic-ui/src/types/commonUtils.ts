import { Thread } from './communication';

export const constructThreadTitle = (thread: Thread): string => {
    let participant = thread.participants.find(p => p.resourceType === 'Patient') ||
        thread.participants.find(p => p.resourceType === 'Practitioner' && p.type === null) ||
        thread.participants.find(p => p.resourceType === 'Practitioner' && p.type !== 'System Agent') ||
        thread.participants.find(p => p.resourceType === 'Practitioner' && p.type === 'System Agent');
    
    return `${participant?.displayName || participant?.resourceType} - ${thread.threadSubject.patientLanguage?.display || thread.threadSubject.patientLanguage?.code || 'English'}`;
};
