import type { ChatMessage as OriginalChatMessage } from '@azure/communication-chat';
import type { JsonString } from './json';
import type { WidgetData } from './widget';

/**
 * Extends the OriginalChatMessage from @azure/communication-chat to include metadata specific to Relic Care use cases.
 * The original 'metadata' property is omitted in favor of incorporating custom metadata tailored to Relic Care's requirements.
 */
export interface ChatMessage extends Omit<OriginalChatMessage, 'metadata'> {
  metadata?: ChatMetadata;
}

export type ChatMetadata = {
  type: 'chat' | 'widget' | 'call' | 'summary' | 'debug' | string; // add new message types as needed
  messageIntl?: JsonString<MessageIntl>; // JSON encoded string of MessageIntl object
  widgetData?: JsonString<WidgetData>; // JSON encoded string of WidgetData object
  callConnectionId?: string;
  [key: string]: string | undefined;
};

export type MessageIntl = {
  content: string;
  locale: string;
};

export type TranslateResponse = {
  languageCode: string;
  translation?: string;
};