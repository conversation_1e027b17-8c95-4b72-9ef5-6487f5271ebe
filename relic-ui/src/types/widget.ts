export type WidgetRequestWithMessageId = {
  requestMessageId: string;
  widgetData: WidgetRequest;
};

export type WidgetRequest = WidgetRequestBase<any, any> & { type: string; widget: string }; // eslint-disable-line @typescript-eslint/no-explicit-any
export type WidgetResponse = WidgetResponseBase<any, any> & { type: string; widget: string }; // eslint-disable-line @typescript-eslint/no-explicit-any

export type WidgetRequestBase<T, U> = {
  responseMessageId: string | null;
  action: string; // e.g. 'agentSwitch'
  actionParams: U; // e.g. U is AgentSwitchParams
  requestedById: string;
  requestOptions: T[];
  response: T | null;
};

export type WidgetResponseBase<T, U> = {
  requestMessageId: string;
  action: string;
  actionParams: U;
  requestedById: string;
  requestOptions: T[];
  response: T;
  responseMessage: string;
};

export interface Suggestion {
  type: string;
  label: string;
  altText: string[];
  value: boolean | number | string;
  intl?: {
    locale: string;
    label: string;
    altText: string[];
  };
}

export type AnnouncementWidgetData = {
  widget: 'AnnouncementWidget';
};

export type CallNotificationWidget = {
  widget: 'CallNotificationWidget';
  callConnectionId: string;
};

export interface Citation {
  id: string;
  title: string;
  content: string;
  filepath: string;
  url: string;
}

export type CitationWidgetData = {
  widget: 'CitationWidget';
  citations: Citation[];
};

export type WidgetData =
  | WidgetRequest
  | WidgetResponse
  | AnnouncementWidgetData
  | CallNotificationWidget
  | CitationWidgetData;
