{"type": "object", "properties": {"name": {"type": "string"}, "identifier": {"type": "string"}, "type": {"type": "object", "properties": {"definition": {"type": "string"}, "code": {"type": "string"}, "display": {"type": "string"}}}, "redirectUri": {"type": "string"}, "description": {"type": "string"}, "location": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "resourceType": {"type": "string", "const": "Location"}, "status": {"type": "string"}, "name": {"type": "string"}, "phone": {"type": "string"}, "fax": {"type": "string"}}, "required": ["id", "resourceType", "status", "name", "phone"]}}, "address": {"type": "object", "properties": {"use": {"type": "string", "enum": ["home", "work", "temp", "old", "billing"]}, "type": {"type": "string", "enum": ["postal", "physical", "both"]}, "line": {"type": "array", "items": {"type": "string"}}, "city": {"type": "string"}, "district": {"type": "string"}, "state": {"type": "string"}, "postalCode": {"type": "string"}, "country": {"type": "string"}}, "required": ["use", "type", "line", "city", "district", "state", "postalCode", "country"]}, "contact": {"type": "object", "properties": {"phone": {"type": "string", "minLength": 10}, "email": {"type": "string", "format": "email"}, "website": {"type": "string", "format": "uri"}, "fax": {"type": "string"}}, "required": ["phone", "email"]}, "defaultChatEndpoint": {"type": "object", "properties": {"url": {"type": "string"}, "provider": {"type": "string"}}, "required": ["url", "provider"]}, "defaultLanguage": {"type": "object", "properties": {"system": {"type": "string"}, "code": {"type": "string"}, "display": {"type": "string"}}, "required": ["system", "code", "display"]}}, "required": ["name"]}