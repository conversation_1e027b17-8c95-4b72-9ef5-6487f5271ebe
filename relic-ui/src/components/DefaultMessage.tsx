import React from 'react';
import { useThreadContext } from './RelicThreadProvider';

type DefaultMessageProps = {
  defaultOnRender: (props: any) => JSX.Element; // eslint-disable-line @typescript-eslint/no-explicit-any
  messageProps: any; // eslint-disable-line @typescript-eslint/no-explicit-any
};

const DefaultMessage: React.FC<DefaultMessageProps> = ({ defaultOnRender, messageProps }) => {
  const { displayInEnglish } = useThreadContext();
  if (messageProps.message.metadata?.messageIntl && !displayInEnglish) {
    const messageIntl = JSON.parse(messageProps.message.metadata.messageIntl);
    const newMessageProps = {
      ...messageProps,
      message: {
        ...messageProps.message,
        content: messageIntl.content,
      },
    };
    return <div>{defaultOnRender(newMessageProps)}</div>;
  } else {
    return <div>{defaultOnRender(messageProps)}</div>;
  }
};

export default DefaultMessage;
