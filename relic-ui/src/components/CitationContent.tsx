import React from 'react';
import CitationToolTip from '../components/CitationToolTip';
import { Box, Typography } from '@mui/material';
import { Citation } from '../types';
import { truncateText, wrapCitationsInSentences } from '../utils/citationContentParser';
import MarkdownRender from './MarkdownRender';
import { CitationStyle } from '../types';

interface CitationContentProps {
  content: string;
  citations: Citation[];
  citationStyles: CitationStyle;
}

const CitationContent: React.FC<CitationContentProps> = ({ content, citations, citationStyles }) => {

  const replaceableComponent = {
    span: (props: any) => {
      const type = props['data-type'];
      if (type === 'tooltip') {
        const ids = props['data-ids'];
        const citationIds = ids ? ids.split(',') : [];
        return (
          <CitationToolTip
            citations={citations}
            citationIds={citationIds}
            styles={citationStyles}
          >
            {props.children}
          </CitationToolTip>
        );
      } else if (type === 'reference') {
        return (
          <>
            <Box
              display="flex"
              gap={0.5}
              mt={3}
              alignItems="flex-start"
              sx={{ backgroundColor: 'chatBubbleBackgroundColor' }}
              width="100%"
              component={'span'}
            >
              <Typography
                component={'span'}
                sx={{
                  display: {
                    xs: 'none',
                    sm: 'inline',
                  },
                  fontSize: '14px',
                  mr: 0.5,
                  whiteSpace: 'nowrap',
                }}
              >
                Reference(s):
              </Typography>

              <Box display="flex" flexWrap="wrap" alignItems="center" component={'span'}>
                {citations.map((citation, index) => {
                  const citationIndex = index + 1;
                  const citationId = [citation.id];
                  return (
                    <CitationToolTip
                      key={`reference-${citation.id}-${citationIndex}`}
                      citations={citations}
                      citationIds={citationId}
                      styles={citationStyles}
                      chunk={true}
                    >
                      <Box
                        component="span"
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          marginY: 0.2,
                          marginRight: 0.5,
                          cursor: 'pointer',
                          border: '1px solid gray',
                          flexWrap: 'nowrap',
                        }}
                      >
                        <Box
                          component={'span'}
                          sx={{
                            padding: '2px 4px',
                            border: `solid 1px ${citationStyles?.reference?.borderColor}`,
                            backgroundColor: citationStyles?.reference?.backgroundColor,
                            fontWeight: 400,
                            fontSize: '12px',
                            display: 'inline-block',
                            minWidth: '20px',
                            textAlign: 'center',
                          }}
                        >
                          {citationIndex}
                        </Box>
                        <Typography
                          component={'span'}
                          sx={{
                            padding: '2px 4px',
                            border: `solid 1px ${citationStyles?.reference?.borderColor}`,
                            fontWeight: 400,
                            fontSize: '12px',
                            display: 'inline-block',
                            lineHeight: 'normal',
                          }}
                        >
                          {truncateText(citation.title, 50)}
                        </Typography>
                      </Box>
                    </CitationToolTip>
                  );
                })}
              </Box>
            </Box>
          </>
        );
      }
      return <>{props.children}</>;
    },
  };

  return (
    <MarkdownRender content={wrapCitationsInSentences(content, citations)} components={replaceableComponent} />
  );
};

export default CitationContent;
