import React, { useMemo } from 'react';
import ReactMarkDown from 'react-markdown';
import { Components } from 'react-markdown/lib';
import rehypeRaw from 'rehype-raw';
import rehypeReact from 'rehype-react';
import remarkGfm from 'remark-gfm';

interface MarkdownRenderProps {
  content: string;
  components?: Partial<Components>;
}

const MarkdownRender: React.FC<MarkdownRenderProps> = ({ content, components = {} }) => {
  const memoizedComponents = useMemo(() => components, [components]);
  return (
    <ReactMarkDown
      children={content}
      components={memoizedComponents}
      // Plugins to process raw HTML, sanitize it, and integrate React components
      rehypePlugins={[rehypeRaw, [rehypeReact, { components: memoizedComponents }]]}
      // Plugin for GitHub Flavored Markdown
      remarkPlugins={[remarkGfm]}
    />
  );
};

export default MarkdownRender;
