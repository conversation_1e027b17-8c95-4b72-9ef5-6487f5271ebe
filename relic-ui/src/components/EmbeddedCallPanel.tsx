import { useRef, useState, useEffect } from 'react';
import { CommunicationIdentifier, CommunicationUserIdentifier, AzureCommunicationTokenCredential } from '@azure/communication-common';
import {
  CallAdapter,
  CallAdapterState,
  CallComposite,
  AvatarPersonaData,  
  StartCallIdentifier,
  CommonCallAdapterOptions,
  toFlatCommunicationIdentifier,
  createAzureCommunicationCallAdapter,
  AzureCommunicationOutboundCallAdapterArgs,
} from '@azure/communication-react';
import { StartCallOptions } from '@azure/communication-calling';
import { Spinner, Stack } from '@fluentui/react';
import Box from '@mui/material/Box';
import { useUserContext } from './RelicUserProvider';
import { useThreadContext } from './RelicThreadProvider';
import { RelicChatParticipant } from './../types';
import { CommunicationIdentity } from './../types';

export const EmbeddedCallPanel = () => {
  const { thread, getOnCallParticipantIdentity } = useThreadContext();
  const { myIdentity, aiAssistant } = useUserContext();
  const [callerIdentity, setCallerIdentity] = useState<CommunicationIdentity | null>(null);
  const [targetCallees, setTargetCallees] = useState<StartCallIdentifier[]>([]);
  const [adapterArgs, setAdapterArgs] = useState<AzureCommunicationOutboundCallAdapterArgs>();
  const [adapter, setAdapter] = useState<CallAdapter>();
  // Todo: Fix this duplication. callPanelState is duplicated in windows.sessionStorage as well. 
  const [callPanelState, setCallPanelState] = useState<'new' | 'inCall'>('new');

  
  useEffect(() => {
    const fetchSpeakerIdentity = async () => {
      const onCallIdentity = await getOnCallParticipantIdentity();
      setCallerIdentity(onCallIdentity);
    };
    fetchSpeakerIdentity();
  }, []);

  useEffect(() => {
    if (callerIdentity && thread?.participants && aiAssistant?.id && !adapterArgs) {
      const caller: CommunicationIdentifier = { communicationUserId: callerIdentity?.userId as string };
      const callerToken: string = callerIdentity?.secret.token as string;
      const callerCredential = new AzureCommunicationTokenCredential(callerToken);
      const callerDisplayName = callerIdentity?.displayName as string;
      const callee: StartCallIdentifier = thread?.participants?.find(
        (p: RelicChatParticipant) => p.resourceId === aiAssistant?.id,
      )?.id as StartCallIdentifier;
      const adapterOptions: CommonCallAdapterOptions = {
        callingSounds: {
          callEnded: { url: '/sounds/callEnded.mp3' },
          callRinging: { url: '/sounds/callRinging.mp3' },
          callBusy: { url: '/sounds/callBusy.mp3' },
        },
      };
      const callAdapterArgs: AzureCommunicationOutboundCallAdapterArgs = {
        userId: caller,
        displayName: callerDisplayName,
        credential: callerCredential,
        targetCallees: [callee as StartCallIdentifier],
        options: adapterOptions,
      };
      if (!targetCallees.includes(callee as StartCallIdentifier)) {
        targetCallees.push(callee as StartCallIdentifier);
      }
      setTargetCallees(targetCallees);
      setAdapterArgs(callAdapterArgs);
    }
  }, [callerIdentity, thread?.participants, aiAssistant?.id]);

  useEffect(() => {
    if (adapterArgs && targetCallees) {
      const setAdapterInState = async () => {
        try {
          setAdapter(
            await createAzureCommunicationCallAdapter({
              userId: adapterArgs.userId as CommunicationUserIdentifier,
              displayName: adapterArgs.displayName as string,
              credential: adapterArgs.credential,
              targetCallees: adapterArgs.targetCallees as StartCallIdentifier[],
              options: adapterArgs.options,
            }),
          );
        } catch (e) {
          console.error('Error creating adapter', e);
        }
      };
      if (callPanelState === 'new') {
        setAdapterInState();
      }  
    }
  }, [adapterArgs, targetCallees]);

  useEffect(() => {
    const endCallEvent = new MessageEvent('message', {
      data: 'end-call',
      origin: window.location.origin,
    });  
    const handleWindowMessage = (event: MessageEvent) => {
      if (event.origin !== window.location.origin) {
        return;
      }
      // Start the call when the parent window asks to start the call and a call is not already running.
      if (event.data && event.data === 'start-call' && callPanelState === 'new' && !adapter?.getState().call) {
        setCallPanelState('inCall');
        if (adapter && targetCallees && targetCallees.length > 0) {
          const callOptions: StartCallOptions = {
            customContext: {
              userToUser: thread?.threadId,
            },
            audioOptions: { muted: false },
          };
          adapter?.startCall(targetCallees, callOptions);
        }
      }
      // End the call when the parent window asks to end the call. This is done to allow parent window to hangup the call.
      if (event.data && event.data === 'end-call' && callPanelState === 'inCall') {
        setCallPanelState('new');
        if (adapter) {
          adapter?.leaveCall();
          setAdapter(undefined);
          adapter.dispose();
        }
        if (window.opener) {
          window.opener.postMessage('request-end-call', window.opener.origin);
        }
        window.close();
      }
    };
    const handleWindowClose = (event: any) => {
      if (event.origin !== window.location.origin) {
        return;
      }
      event.preventDefault();
      window.dispatchEvent(endCallEvent);
    };
    const callEndedListener = () => {
      window.dispatchEvent(endCallEvent);
    };
    window.addEventListener('message', handleWindowMessage);
    window.addEventListener('beforeunload', handleWindowClose);
    if (adapter) {
      adapter.on('callEnded', callEndedListener);
      // Auto Start Call after adapter readiness - Ask parent window to auto start the call since adapter is now ready.
      if (window.opener && callPanelState === 'new' ) {
        window.opener.postMessage('request-start-call', window.opener.origin);
      }
    }
    return () => {
      window.removeEventListener('message', handleWindowMessage);
      window.removeEventListener('beforeunload', handleWindowClose);
      if (adapter) {
        adapter.off('callEnded', callEndedListener);
      }
    };
  }, [adapter, callPanelState]);

  const onFetchAvatarPersonaData = async (userId: string): Promise<AvatarPersonaData> => {
    let avatarName: string = 'Unknown Participant';
    let avatarInitials: string = 'UP';
    const myName: string = myIdentity?.communicationIdentities[0].displayName as string;
    const myId: string = myIdentity?.communicationIdentities[0].userId as string;
    const aiAssistantName: string = aiAssistant ? aiAssistant?.name : avatarName;
    const aiAssistantId: string = aiAssistant?.communicationIdentities
      ? (aiAssistant?.communicationIdentities[0].userId as string)
      : '';
    if (userId === aiAssistantId) {
      //For AI participant, use actual name
      avatarName = aiAssistantName || avatarName;
      avatarInitials = avatarName.split(' ').map((n) => n[0]).join('');
    } else {
      //For other participant, use my name. As of now, this participant is always patient if the thread contains patient.
      avatarName = myName || avatarName;
      avatarInitials = avatarName.split(' ').map((n) => n[0]).join('');
    }
    return Promise.resolve({
      text: avatarName,
      imageInitials: avatarInitials,
    });
  };

  return (
    <>
      {callPanelState === 'new' && (
        <Stack verticalAlign="center" styles={{ root: { height: '100vh', width: '100vw' } }}>
          <Spinner label={'Preparing Browser...'} ariaLive="assertive" labelPosition="top" />
        </Stack>
      )}
      {callPanelState === 'inCall' && adapter && (
        <CallComposite
          adapter={adapter}
          formFactor="desktop"
          onFetchAvatarPersonaData={onFetchAvatarPersonaData}
          options={{
            callControls: {
              cameraButton: false,
              screenShareButton: false,
              moreButton: false,
              peopleButton: false,
              raiseHandButton: false,
              displayType: 'compact'
            },
            localVideoTile: { position: 'grid' },
          }}
        ></CallComposite>
      )}
    </>
  );
};
