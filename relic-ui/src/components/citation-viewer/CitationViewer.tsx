import React, { useState, useEffect } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import IconButton from '@mui/material/IconButton';
import CircularProgress from '@mui/material/CircularProgress';
import DialogContent from '@mui/material/DialogContent';
import Box from '@mui/material/Box';
import Alert from '@mui/material/Alert';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import CloseIcon from '@mui/icons-material/Close';
import axios from 'axios';
import { useUserContext } from '../RelicUserProvider';
interface CitationViewerProps {
    open: boolean;
    onClose: () => void;
    citationUrl: string;
}

const CitationViewer: React.FC<CitationViewerProps> = ({ open, onClose, citationUrl }) => {
    const [fileUrl, setFileUrl] = useState<string | null>(null);
    const [fileType, setFileType] = useState<'pdf' | 'docx' | 'doc' | 'txt' | 'url' | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [txtContent, setTxtContent] = useState<string | null>(null);
    const { accessToken, idToken, serviceUri, myIdentity } = useUserContext();
    const [hasOpened, setHasOpened] = useState(false);

    useEffect(() => {
        if (open && citationUrl) {
            const validateCitationUrl = async () => {
                setLoading(true);
                setError(null);
                setHasOpened(false); // Reset the flag when a new URL is loaded

                try {
                    const isAzureBlobUrl = citationUrl.includes('.blob.core.windows.net');

                    if (isAzureBlobUrl) {
                        // Determine file type based on file extension
                        if (citationUrl.endsWith('.pdf')) {
                            setFileType('pdf');
                        } else if (citationUrl.endsWith('.docx') || citationUrl.endsWith('.doc')) {
                            setFileType('docx');
                        } else if (citationUrl.endsWith('.txt')) {
                            setFileType('txt');
                        } else {
                            setFileType(null); // Unsupported file type
                        }

                        // Fetch SAS token for Azure Blob URL
                        const response = await axios.get(`${serviceUri}/tokens/azure-sas`, {
                            params: { url: citationUrl },
                            headers: {
                                'Content-Type': 'application/json',
                                'x-access-token': accessToken,
                                'x-id-token': idToken ?? '',
                                'x-organization-id': myIdentity?.portalIdentity?.organizationId || '',
                            },
                        });

                        const citationSasToken = response.data.token;
                        const securedUrl = `${citationUrl}?${citationSasToken}`;
                        setFileUrl(securedUrl);

                        // Fetch content if file is .txt
                        if (citationUrl.endsWith('.txt')) {
                            const textResponse = await axios.get(securedUrl);
                            setTxtContent(textResponse.data);
                        }
                    } else {
                        // Non-Azure URLs
                        setFileType('url');
                        setFileUrl(citationUrl);
                    }
                } catch (err) {
                    setError('Unable to fetch the document. Please try again.');
                } finally {
                    setLoading(false);
                }
            };

            validateCitationUrl();
        }
    }, [open, citationUrl, accessToken, idToken, myIdentity]);

    useEffect(() => {
        if (fileType && fileType !== 'txt' && fileUrl && !hasOpened) {
            // Open all non-txt file types (pdf, docx, and external URLs) in a new tab and close the modal
            const externalUrl = fileType === 'docx' || fileType === 'doc'
                ? `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl)}`
                : fileUrl;

            window.open(externalUrl, '_blank', 'noopener,noreferrer');
            setHasOpened(true); // Set the flag to true to prevent re-opening
            onClose();
        }
    }, [fileType, fileUrl, onClose, hasOpened]);

    return (
        <Dialog open={open} onClose={onClose} fullScreen aria-labelledby="citation-source-viewer">
            <DialogTitle sx={{ position: 'relative', display: 'flex', justifyContent: 'space-between' }}>
                <span>Citation Source</span>
                <IconButton
                    edge="end"
                    color="inherit"
                    onClick={onClose}
                    aria-label="close"
                    sx={{ position: 'absolute', right: 8, top: 8 }}
                >
                    <CloseIcon />
                </IconButton>
            </DialogTitle>
            <DialogContent>
                <Box sx={{ height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                    {loading ? (
                        <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center">
                            <CircularProgress size={50} sx={{ mb: 2 }} />
                            <Typography variant="body1" sx={{ color: 'text.secondary' }}>
                                Loading your document, please wait...
                            </Typography>
                        </Box>
                    ) : error ? (
                        <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center">
                            <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>
                            <Button onClick={onClose} variant="contained" color="primary">
                                Close
                            </Button>
                        </Box>
                    ) : (
                        <Box
                            sx={{
                                height: '100%',
                                width: '100%',
                                overflowY: 'auto',
                                whiteSpace: 'pre-wrap',
                                fontFamily: 'monospace',
                                padding: 2,
                                backgroundColor: '#f9f9f9',
                                borderRadius: 1,
                                boxShadow: 1,
                            }}
                        >
                            {txtContent}
                        </Box>
                    )}
                </Box>
            </DialogContent>
        </Dialog>
    );
};

export default CitationViewer;
