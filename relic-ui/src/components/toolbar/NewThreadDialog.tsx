import { <PERSON>, Dialog, DialogContent, <PERSON><PERSON>Title, <PERSON><PERSON>ield, <PERSON>ack, DialogA<PERSON>, Button } from '@mui/material';
import { FieldValues, useForm } from 'react-hook-form';
import InvitationDropdown from './InvitationDropdown';
import { useUserContext } from '../RelicUserProvider';
import { useThreadContext } from '../RelicThreadProvider';
import SendIcon from '@mui/icons-material/Send';
import SaveIcon from '@mui/icons-material/Save';
import { RelicChatParticipant, RelicPatient } from '../../types';
import React, { useEffect } from 'react';
import { constructThreadTitle } from '../../types';

interface NewThreadDialogProps {
  isNewThreadDialogOpen: boolean;
  handleNewThreadDialogClose: VoidFunction;
}

const NewThreadDialog: React.FC<NewThreadDialogProps> = ({ isNewThreadDialogOpen, handleNewThreadDialogClose }) => {
  const {
    watch,
    control,
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm();

  const values = watch();

  const { thread, reinitializeThread } = useThreadContext();

  const { accessToken, idToken, serviceUri, myIdentity } = useUserContext();

  const onSubmit = async () => {
    await handleThreadSaveOrInvite(values, false);
  };

  const handleSendInvitation = async () => {
    await handleThreadSaveOrInvite(values, true);
  };

  const fetchPatient = React.useCallback(async (patientParticipant: RelicChatParticipant) => {
    try {
      const url = `${serviceUri}/patients/${patientParticipant.resourceId}`;

      const headers = {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        'x-access-token': accessToken,
        'x-id-token': idToken || '',
        'x-organization-id': myIdentity?.portalIdentity?.organizationId || '',
        'x-hostname': window.location.hostname,
      };

      const response = await fetch(url, { method: 'GET', headers });

      if (!response.ok) {
        throw new Error(`Failed to fetch patient: ${response.statusText}`);
      }
      const patientDetails: RelicPatient = await response.json();

      setValue('cellphone', patientDetails?.mobilePhone);
      setValue('email', patientDetails?.email);
    } catch (error) {
      console.error('Error fetching organizations:', error);
    }
  }, [accessToken, idToken, myIdentity?.portalIdentity?.organizationId]);

  const patientParticipant = thread?.participants?.find(
    (participant: RelicChatParticipant) => participant.resourceType === 'Patient',
  );

  useEffect(() => {
    if (patientParticipant) fetchPatient(patientParticipant);
  }, [thread?.participants, fetchPatient]);

  const handleThreadSaveOrInvite = async (formData: FieldValues, isInvite: boolean) => {
    try {
      const threadSubject = {
        ...thread?.threadSubject,
        title: formData.title,
        patientLanguage: thread?.threadSubject?.patientLanguage,
      };

      const mutationPayload = {
        subject: threadSubject,
        participants: thread?.participants,
        inviteVia: isInvite ? formData.invitation || 'none' : 'none',
        receiverEmail: formData.email,
        receiverPhone: formData.cellphone,
      };

      const url = `${serviceUri}/communication/chat/threads`;

      const headers = {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        'x-access-token': accessToken,
        'x-id-token': idToken || '',
        'x-organization-id': myIdentity?.portalIdentity?.organizationId || '',
        'x-hostname': window.location.hostname,
      };

      const response = await fetch(url, { method: 'POST', headers, body: JSON.stringify(mutationPayload) });

      if (!response.ok) {
        throw new Error(`Failed to fetch: ${response.statusText}`);
      }
      const data = await response.json();
      handleNewThreadDialogClose();
      const redirectUrl = `/threads/show/${data?.threadId}?threadId=${data?.threadId}&name=${data?.threadSubject?.title}`;
      const queryParams = new URLSearchParams(window.location.search);
      if (queryParams?.get('threadId')) {
        // TODO: Redirect to the new thread after creation or invitation need to check for react router
        window.location.replace(redirectUrl);
      } else {
        reinitializeThread(); // Re-render RelicThreadProvider without refreshing the page
      }
    } catch (error) {
      // Provide an error message for any failure in processing
      console.error('Error in handleThreadSaveOrInvite:', error);
    }
  };

  const defaultTitle = thread ? constructThreadTitle(thread) : '';

  return (
    <Dialog fullWidth maxWidth="sm" open={isNewThreadDialogOpen} onClose={handleNewThreadDialogClose}>
      <DialogTitle>New Conversation</DialogTitle>
      <DialogContent dividers>
        <Box sx={{ display: 'flex', flexDirection: 'column', mb: 2, mt: 1, gap: 2 }}>
          <Stack component="form" spacing={2.5} autoComplete="off">
            <TextField
              {...register('title', {
                required: 'This field is required',
              })}
              error={!!(errors as any)?.title}
              helperText={(errors as any)?.title?.message}
              margin="normal"
              required
              fullWidth
              InputLabelProps={{ shrink: true }}
              type="text"
              label={'Title'}
              name="title"
              placeholder={defaultTitle}
            />
            {patientParticipant && (
              <InvitationDropdown
                control={control}
                name="invitation"
                label={'Invite via'}
                value={values.invitation ?? 'none'}
                error={!!(errors as any)?.invitation}
                helperText={(errors as any)?.invitation?.message}
              />
            )}
            {(values.invitation === 'cellphone' || values.invitation === 'both') && (
              <TextField
                {...register('cellphone', {
                  required: 'This field is required',
                })}
                error={!!(errors as any)?.cellphone}
                helperText={(errors as any)?.cellphone?.message}
                margin="normal"
                fullWidth
                InputLabelProps={{ shrink: true }}
                type="text"
                label={'Cellphone'}
                name="cellphone"
              />
            )}
            {(values.invitation === 'email' || values.invitation === 'both') && (
              <TextField
                {...register('email', {
                  required: 'This field is required',
                })}
                error={!!(errors as any)?.email}
                helperText={(errors as any)?.email?.message}
                margin="normal"
                fullWidth
                InputLabelProps={{ shrink: true }}
                type="text"
                label={'Email'}
                name="email"
              />
            )}
          </Stack>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleNewThreadDialogClose} variant="outlined">
          Cancel
        </Button>
        <Button onClick={handleSubmit(handleSendInvitation)} variant="outlined">
          <SendIcon fontSize="small" sx={{ mr: 1 }} />
          Send Invitation
        </Button>
        <Button onClick={handleSubmit(onSubmit)} variant="contained">
          <SaveIcon fontSize="small" sx={{ mr: 1 }} />
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default NewThreadDialog;
