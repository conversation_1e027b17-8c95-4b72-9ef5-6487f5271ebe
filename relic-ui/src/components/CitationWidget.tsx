/* eslint-disable @typescript-eslint/no-explicit-any */
import DefaultMessage from './DefaultMessage';
import CitationContent from './CitationContent';
import { CitationWidgetData } from '../types';

type CitationWidgetProps = {
  defaultOnRender: (props: any) => JSX.Element;
  messageProps: any;
  widgetData: CitationWidgetData;
};

const CitationWidget: React.FC<CitationWidgetProps> = ({ defaultOnRender, messageProps, widgetData }) => {
  const messageContent = CitationContent({
    content: messageProps.message.content,
    citations: widgetData.citations,
    citationStyles: messageProps?.styles?.citationStyles,
  });
  const newMessageProps = { ...messageProps, message: { ...messageProps.message, content: messageContent } };
  return (
    <>
      <DefaultMessage defaultOnRender={defaultOnRender} messageProps={newMessageProps} />
    </>
  );
};

export default CitationWidget;
