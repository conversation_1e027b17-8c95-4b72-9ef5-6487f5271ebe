import path from 'path';
import react from '@vitejs/plugin-react';
import { defineConfig, loadEnv } from 'vite';
import tsConfigPaths from 'vite-tsconfig-paths';
import dts from 'vite-plugin-dts';
import * as packageJson from './package.json';

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');

  return {
    define: {
      __APP_ENV__: JSON.stringify(env.APP_ENV),
    },
    plugins: [
      react(),
      tsConfigPaths(),
      dts({
        entryRoot: 'src',
        outDir: 'dist',
        tsconfigPath: path.resolve(__dirname, 'tsconfig.json'),
      }),
    ],
    resolve: {
      alias: {
        '~': path.resolve(__dirname, 'node_modules'),
        src: path.resolve(__dirname, 'src'),
      },
    },
    build: {
      lib: {
        entry: {
          index: path.resolve(__dirname, 'src/index.ts'),
          schema: path.resolve(__dirname, 'src/types/index.ts'),
        },
        name: 'relic-ui',
        fileName: (format, entryName) => {
          if (entryName === 'index') {
            return `relic-ui.${format}.js`;
          }
          // For schema entry, we use a different naming convention
          if (entryName === 'schema' && format === 'cjs') {            
            return `relic-ui.${entryName}.${format}`;
          }
          return `relic-ui.${entryName}.${format}.js`;
        },
        formats: ['es', 'cjs'],
      },
      commonjsOptions:{
        strictRequires: "auto"
      },
      rollupOptions: {
        external: [...Object.keys(packageJson.dependencies || {})],
        output: {
          globals: {
            react: 'React',
            'react-dom': 'ReactDOM',
            '@emotion/styled': 'EmStyled',
            '@emotion/react': 'EmReact',
            '@azure/communication-common': 'AzureCommunicationCommon',
            '@azure/communication-react': 'AzureCommunicationReact',
            '@mui/material': 'MuiMaterial',
            'fuse.js': 'Fuse',
            '@fluentui/react-components': 'FluentUIReact',
            '@rjsf/mui': 'RJSFMui',
            '@rjsf/validator-ajv8': 'RJSFValidatorAjv8',
            'react-markdown': 'ReactMarkdown',
            'rehype-raw': 'RehypeRaw',
            'rehype-react': 'RehypeReact',
            'remark-gfm': 'RemarkGfm',
          },
        },
        maxParallelFileOps: 100,
        treeshake: true,
      },
    },
  };
});
