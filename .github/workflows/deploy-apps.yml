name: Build & Deploy Dev Apps

env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG }}
  VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}

on:
  push:
    branches:
      - 'main'

jobs:
  json-ui:
    # Relic Facility Portal deployment at https://relic-facility-portal.vercel.app/
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - name: "Checkout from Gith<PERSON>"
        uses: actions/checkout@v4

      - name: "Install node.js"
        uses: actions/setup-node@v2
        with:
          node-version: "22.0.0"

      - name: "Install pnpm"
        uses: pnpm/action-setup@v4
        with:
          version: "10.10.0"

      - name: "Install Vercel CLI"
        run: pnpm install -g vercel@latest

      - name: Remove relic-ui .env.production file
        uses: JesseTG/rm@v1.0.3
        with:
          path: ./relic-ui/.env.production

      - name: "Build relic ui"
        run: |
          pnpm --filter relic-ui... install --frozen-lockfile
          pnpm run build
        working-directory: relic-ui

      - name: "Install json-ui dependencies"
        run: pnpm --filter json-ui... install --frozen-lockfile
        working-directory: json-ui

      - name: "Build & deploy Relic JSON UI"
        run: |
          export NODE_OPTIONS='--max-old-space-size=8192'
          export VERCEL_PROJECT_ID=${{ secrets.VERCEL_RELIC_FACILITY_PORTAL }}
          vercel pull --yes --environment=production --token=$VERCEL_TOKEN
          vercel build --prod --token=$VERCEL_TOKEN
          vercel deploy --prebuilt --prod --token=$VERCEL_TOKEN
        working-directory: json-ui

  test-json-ui:
      needs: json-ui
      timeout-minutes: 60
      if: github.ref == 'refs/heads/main'
      runs-on: ubuntu-22.04
      steps:
      - name: "Checkout from Github"
        uses: actions/checkout@v4

      - name: "Install node.js"
        uses: actions/setup-node@v4
        with:
          node-version: "20.0.0"

      - name: "Install pnpm"
        uses: pnpm/action-setup@v4
        with:
          version: "10.10.0"

      - name: "Install Tests dependencies"
        run: |
          pnpm install --ignore-workspace --frozen-lockfile
        working-directory: tests

      - name: "Install Playwright Browsers"
        run: |
          pnpm exec playwright install --with-deps
        working-directory: tests

      - name: "Run Playwright tests"
        run: |
          pnpm exec playwright test
        working-directory: tests
        continue-on-error: true
        id: playwright-tests

      - name: "Upload HTML report to Azure"
        shell: bash
        run: |
          REPORT_DIR='run-${{ github.run_id }}-${{ github.run_attempt }}'
          azcopy cp --recursive "./playwright-report/*" "https://relicstorage.blob.core.windows.net/\$web/$REPORT_DIR"
          echo "Detailed Report::https://relicstorage.z13.web.core.windows.net/$REPORT_DIR/index.html" >> $GITHUB_STEP_SUMMARY
        env:
          AZCOPY_AUTO_LOGIN_TYPE: SPN
          AZCOPY_SPA_APPLICATION_ID: ${{ secrets.AZURE_CLIENT_ID_GITHUB_ACTIONS }}
          AZCOPY_SPA_CLIENT_SECRET: ${{ secrets.AZURE_CLIENT_SECRET_GITHUB_ACTIONS }}
          AZCOPY_TENANT_ID: ${{ secrets.AZURE_TENANT_ID }}
        working-directory: tests
        
      - name: Generate CTRF report
        run: pnpm exec github-actions-ctrf ctrf/ctrf-report.json
        working-directory: tests

      - uses: actions/upload-artifact@v4
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 30

      - name: Check test result and fail workflow if necessary
        run: |
          if [ ${{ steps.playwright-tests.outcome }} == 'failure' ]; then
            echo "Playwright tests failed"
            exit 1
          fi

  json-server:
    # Relic JSON Server deployment at https://relic-json-server.vercel.app/
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - name: "Checkout from Github"
        uses: actions/checkout@v4

      - name: "Install node.js"
        uses: actions/setup-node@v2
        with:
          node-version: "20.0.0"

      - name: "Install Vercel CLI"
        run: npm install -g vercel@latest

      - name: "Build & deploy json-server"
        run: |
          export VERCEL_PROJECT_ID=${{ secrets.VERCEL_JSON_SERVER }}
          vercel pull --yes --environment=production --token=$VERCEL_TOKEN
          vercel build --prod --token=$VERCEL_TOKEN
          vercel deploy --prebuilt --prod --token=$VERCEL_TOKEN
        working-directory: json-server
