name: Build & Deploy Dev Services

on:
  push:
    branches:
      - 'main'

jobs:
  apify-actor:
    if: github.ref == 'refs/heads/some-random-branch-that-does-not-exist'
    runs-on: ubuntu-latest
    steps:
      # checkout the repo
      - name: 'Checkout from Github'
        uses: actions/checkout@main

      - name: 'Deploy Actor to Apify'
        uses: distributhor/workflow-webhook@v3
        with:
          webhook_url: https://api.apify.com/v2/acts/M4nchkJlaBsRI9jpF/builds?token=${{ secrets.APIFY_BUILD_TOKEN }}&version=0.0&tag=latest&waitForFinish=60
          webhook_secret: ${{ secrets.APIFY_BUILD_TOKEN }}

  node-services:
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
    # checkout the repo
    - name: 'Checkout from Gith<PERSON>'
      uses: actions/checkout@main
      
    - name: 'Login via Azure CLI'
      uses: azure/login@v2
      with:
        creds: '{"clientId":"${{ secrets.AZURE_CLIENT_ID }}","clientSecret":"${{ secrets.AZURE_CLIENT_SECRET }}","subscriptionId":"${{ secrets.AZURE_DEV_SUBSCRIPTION_ID }}","tenantId":"${{ secrets.AZURE_TENANT_ID }}"}'

    # - name: 'Delete node-services repository'
    #   uses: azure/login@v2
    #   with:
    #     creds: '{"clientId":"${{ secrets.AZURE_CLIENT_ID }}","clientSecret":"${{ secrets.AZURE_CLIENT_SECRET }}","subscriptionId":"${{ secrets.AZURE_DEV_SUBSCRIPTION_ID }}","tenantId":"${{ secrets.AZURE_TENANT_ID }}"}'
    # - run: |
    #       az acr repository delete --name aicounsellor --resource-group ai-counsellor --repository node-services --yes

    
    - name: 'Build and push image'
      uses: docker/login-action@v3
      with:
        registry: ${{ secrets.REGISTRY_LOGIN_SERVER }}
        username: ${{ secrets.AZURE_CLIENT_ID }}
        password: ${{ secrets.AZURE_CLIENT_SECRET }}
    - run: |
        docker build -t ${{ secrets.REGISTRY_LOGIN_SERVER }}/node-services:$GITHUB_SHA -f app/node-services .
        docker push ${{ secrets.REGISTRY_LOGIN_SERVER }}/node-services:$GITHUB_SHA

    - name: 'Update Container App to use latest image'
      uses: Azure/cli@v1.0.7
      with:
        inlineScript: |
          az containerapp update -n node-services -g ai-counsellor --image aicounsellor.azurecr.io/node-services:$GITHUB_SHA

  agent-messenger:
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - name: 'Checkout from Github'
        uses: actions/checkout@main

      - name: 'Login via Azure CLI'
        uses: azure/login@v2
        with:
          creds: '{"clientId":"${{ secrets.AZURE_CLIENT_ID }}","clientSecret":"${{ secrets.AZURE_CLIENT_SECRET }}","subscriptionId":"${{ secrets.AZURE_DEV_SUBSCRIPTION_ID }}","tenantId":"${{ secrets.AZURE_TENANT_ID }}"}'

      # - name: 'Delete agent-messenger repository'
      #   uses: azure/login@v2
      #   with:
      #     creds: '{"clientId":"${{ secrets.AZURE_CLIENT_ID }}","clientSecret":"${{ secrets.AZURE_CLIENT_SECRET }}","subscriptionId":"${{ secrets.AZURE_DEV_SUBSCRIPTION_ID }}","tenantId":"${{ secrets.AZURE_TENANT_ID }}"}'
      # - run: |
      #     az acr repository delete --name aicounsellor --resource-group ai-counsellor --repository agent-messenger --yes
      
      - name: 'Build and push image'
        uses: docker/login-action@v3
        with:
          registry: ${{ secrets.REGISTRY_LOGIN_SERVER }}
          username: ${{ secrets.AZURE_CLIENT_ID }}
          password: ${{ secrets.AZURE_CLIENT_SECRET }}

      - run: |
          docker build -t ${{ secrets.REGISTRY_LOGIN_SERVER }}/agent-messenger:$GITHUB_SHA -f app/agent-messenger .
          docker push ${{ secrets.REGISTRY_LOGIN_SERVER }}/agent-messenger:$GITHUB_SHA

      - name: 'Update Container App to use latest image'
        uses: Azure/cli@v1.0.7
        with:
          inlineScript: |
            az containerapp update -n agent-messenger -g ai-counsellor --image aicounsellor.azurecr.io/agent-messenger:$GITHUB_SHA
            
