import config from 'config';
import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import { Collection } from 'mongodb';
import { RelicPractitioner } from "relic-ui";
import { httpErrors } from "@fastify/sensible";
import { RelicAgent, RelicDocument } from "relic-ui";
import { RelicOrganization, Thread, TrainingModule, TrainingContent, Training } from "relic-ui";
import { RelicCommunicationLanguage, RelicPatient } from "relic-ui";

export class OrganizationService {
  private organizationsCollection: Collection<RelicOrganization>;
  private languagesCollection: Collection<RelicCommunicationLanguage>;
  private practitionersCollection: Collection<RelicPractitioner>;
  private agentsCollection: Collection<RelicAgent>;
  private documentsCollection: Collection<RelicDocument>;
  private patientsCollection: Collection<RelicPatient>;
  private threadsCollection: Collection<Thread>;
  private trainingModulesCollection: Collection<TrainingModule>;
  private trainingContentsCollection: Collection<TrainingContent>;
  private trainingCollection: Collection<Training>;
  private fastify: FastifyInstance;

  constructor(fastify: FastifyInstance, options: FastifyPluginOptions) {
    this.organizationsCollection = fastify.mongo.reliccare.db.collection<RelicOrganization>('organizations');
    this.languagesCollection = fastify.mongo.reliccare.db.collection<RelicCommunicationLanguage>('languages');
    this.patientsCollection = fastify.mongo.reliccare.db.collection<RelicPatient>('patients');
    this.practitionersCollection = fastify.mongo.reliccare.db.collection<RelicPractitioner>('practitioners');
    this.agentsCollection = fastify.mongo.reliccare.db.collection<RelicAgent>('agents');
    this.documentsCollection = fastify.mongo.reliccare.db.collection<RelicDocument>('documents');
    this.threadsCollection = fastify.mongo.reliccare.db.collection<Thread>('threads');
    this.trainingModulesCollection = fastify.mongo.reliccare.db.collection<TrainingModule>('trainingModules');
    this.trainingContentsCollection = fastify.mongo.reliccare.db.collection<TrainingContent>('trainingContents');
    this.trainingCollection = fastify.mongo.reliccare.db.collection<Training>('training');
    this.fastify = fastify;
  }

  /**
   * Retrieves the RelicCare organization from the database.
   * @returns {Promise<RelicOrganization>} The RelicCare organization.
   */
  public async getRelicCareOrganization(): Promise<RelicOrganization> {
    const relicCareOrgId = config.get('RELICCARE.ORG_ID');
    const organization = await this.organizationsCollection.findOne({ id: relicCareOrgId }, { projection: { _id: 0 } });
    if (!organization) {
      throw new Error('Relic Care Organization not found.');
    }
    return organization;
  }

  public async getOrganizations(filters: any): Promise<RelicOrganization[] | []> {
    const { filter, _count, _offset, sortOptions } = filters;
    return await this.organizationsCollection
      .find(filter, { projection: { _id: 0 }, limit: _count, skip: _offset })
      .sort(sortOptions)
      .toArray();
  }

  public async getOrganizationCount(filters: any): Promise<number> {
    const { filter } = filters;
    const count = await this.organizationsCollection.find(filter).count();
    return count;
  }

  /**
   * Retrieves an organization by its ID.
   * @param {string} organizationId - The ID of the organization.
   * @returns {Promise<RelicOrganization>} The organization matching the ID.
   */
  public async getOrganization(organizationId: string): Promise<RelicOrganization> {
    if (!organizationId) {
      throw httpErrors.badRequest('Organization id cannot be blank.');
    }
    const filter = [
      { id: organizationId },
      { website: organizationId },
      { 'pointClickCare.id': organizationId },
      { 'externalIdentifier.id': organizationId },
    ];
    const organization: RelicOrganization = await this.organizationsCollection.findOne(
      { $or: filter },
      { projection: { _id: 0 } },
    );
    if (!organization) {
      throw httpErrors.unauthorized(
        `Your organization (id: ${organizationId}) is not registered. Please contact your administrator.`,
      );
    }
    if (!organization.supportedLanguages) {
      organization.supportedLanguages = await this.languagesCollection.find({}).toArray();
    }

    const chatEndpointIndex = organization.endpoints?.findIndex((e) => e.service.includes('chat'));
    const voipEndpointIndex = organization.endpoints?.findIndex((e) => e.service.includes('voip'));

    if (chatEndpointIndex === -1 || voipEndpointIndex === -1) {
      throw httpErrors.internalServerError("Both 'chat' and 'voip' endpoints must be configured for the organization.");
    }

    return organization;
  }

  public async createOrganization(
    relicOrganization: RelicOrganization,
  ): Promise<RelicOrganization> {
    if (!relicOrganization) {
      throw httpErrors.badRequest('Invalid request body. Missing organization details.');
    }
    if (relicOrganization.phone) {
      if (!this.fastify.validatePhoneNumber(relicOrganization.phone)) {
        throw new Error("Invalid phone number.");
      } else {
        relicOrganization.phone = this.fastify.formatPhoneNumber(relicOrganization.phone);
      }
    }
    if (relicOrganization.fax) {
      if (!this.fastify.validatePhoneNumber(relicOrganization.fax)) {
        throw new Error("Invalid fax number.");
      } else {
        relicOrganization.fax = this.fastify.formatPhoneNumber(relicOrganization.fax);
      }
    }

    const existingOrganization = await this.organizationsCollection.findOne(
      {
        $or: [
          { name: relicOrganization.name },
          { website: relicOrganization.website }
        ]
      },
      { projection: { _id: 0 } },
    );
    if (existingOrganization) {
      throw httpErrors.conflict(`Organization with same name or website already exists.`);
    }

    //Remove identifiers from template organization.
    const templateOrganization = await this.getRelicCareOrganization();
    delete templateOrganization.id;
    delete templateOrganization.name;
    delete templateOrganization.type;
    delete templateOrganization.fhirStore;
    delete templateOrganization.website;
    delete templateOrganization.pointClickCare;
    delete templateOrganization.fax;
    delete templateOrganization.phone;
    delete templateOrganization.externalIdentifier;
    relicOrganization = { ...templateOrganization, ...relicOrganization };

    relicOrganization.id = crypto.randomUUID();
    await this.organizationsCollection.insertOne(relicOrganization);
    return relicOrganization;
  }

  public async updateOrganization(
    relicOrganization: RelicOrganization
  ): Promise<RelicOrganization> {
    const [updatedOrganization] = await Promise.all([
      this.organizationsCollection.findOneAndUpdate(
        { id: relicOrganization.id },
        { $set: relicOrganization },
        { projection: { _id: 0 }, returnDocument: 'after' },
      ),
    ]);
    return updatedOrganization;
  }

  public async deleteOrganization(organizationId: string): Promise<void> {
    const filter = [
      { id: organizationId },
      { 'pointClickCare.id': organizationId },
      { 'externalIdentifier.id': organizationId },
    ];
    const organization = await this.organizationsCollection.findOne({ $or: filter }, { projection: { _id: 0 } });
    if (!organization) {
      throw httpErrors.notFound(`Organization with id ${organizationId} not found.`);
    }
    const promises = [];
    promises.push(this.agentsCollection.deleteMany({ organizationId: organization.id }));
    promises.push(this.practitionersCollection.deleteMany({ organizationId: organization.id }));
    promises.push(this.documentsCollection.deleteMany({ organizationId: organization.id }));
    promises.push(this.patientsCollection.deleteMany({ organizationId: organization.id }));
    promises.push(this.organizationsCollection.deleteOne({ id: organization.id }));
    promises.push(this.trainingModulesCollection.deleteMany({ organizationId: organization.id }));
    promises.push(this.trainingContentsCollection.deleteMany({ organizationId: organization.id }));
    promises.push(this.threadsCollection.deleteMany({ 'threadSubject.organizationId': organization.id }));
    promises.push(this.trainingCollection.deleteMany({ organizationId: organization.id }));
    await Promise.allSettled(promises).then((results) => {
      const rejections = results.filter((result) => result.status === 'rejected');
      if (rejections.length > 0) {
        throw httpErrors.internalServerError(
          `Failed to delete organization. ${rejections.map((r) => r.reason).join(', ')}`,
        );
      }
    });
  }
}
