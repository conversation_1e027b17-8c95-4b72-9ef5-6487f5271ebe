import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import { Collection } from 'mongodb';
import { formatHumanName, MedplumClient, ProfileResource } from '@medplum/core';
import {
  IPortalIdentity,
  RelicCommunicationLanguage,
  RelicCondition,
  RelicLink,
  RelicPatient,
} from 'relic-ui';
import { EhrServiceInterface } from './interface';
import { requestContext } from '@fastify/request-context';
import { RelicOrganization } from 'relic-ui';
import { Bundle, Condition, Encounter, Observation, Patient, RelatedPerson, BundleEntry } from '@medplum/fhirtypes';
import { RelicCarePlan, RelicFocus } from '@/types/relicCarePlan';
import { IRole, IUser, IUserIdentity } from 'relic-ui';
import { RelicPractitioner } from 'relic-ui';
import { Location } from 'relic-ui';
import { SignPayloadType } from '@fastify/jwt';

export class MedplumService implements EhrServiceInterface {
  private fastify: FastifyInstance;
  private client: MedplumClient;
  private practitionersCollection: Collection<RelicPractitioner>;

  constructor(fastify: FastifyInstance, options: FastifyPluginOptions) {
    this.fastify = fastify;
    // To be fixed. We need to have a well defined interface for provider services.
    if (fastify && fastify.mongo && fastify.mongo.reliccare && fastify.mongo.reliccare.db) {
      this.practitionersCollection = fastify.mongo.reliccare.db.collection<RelicPractitioner>('practitioners');
    }
    this.client = new MedplumClient();
  }
  /**
   * convert request query to medplum search filter and pagination format
   * @param req
   */
  private convertPatientSearchQuery(query: any): any {
    const organizationId: string =
      query.organizationId || requestContext.get('organizationId') || requestContext.get('organization')?.id;
    let { active, _sort, _order, _start, _end, _search } = query;
    _sort = (_sort as string)?.toLowerCase();

    const filter = {
      active,
      _count: _end - _start,
      _sort: `${_order == 'asc' ? '' : '-'}${_sort}`,
      _offset: _start,
      organization: undefined,
    };
    let orgFilter: string = undefined;
    if (organizationId) {
      orgFilter = `Organization/${organizationId}`;
    } else {
      const me: IUserIdentity = requestContext.get('whoami' as never);
      if (me?.role.name !== 'admin') {
        orgFilter = `Organization/${me.portalIdentity.organizationId}`;
      }
    }
    if (orgFilter) filter.organization = orgFilter;
    else delete filter.organization;
    if (_search != null && _search != '') {
      filter['_filter'] = `(name co "${_search}" or email co "${_search}" or telecom co "${_search}")`;
    }
    return filter;
  }

  // private convertConditionSearchQuery(query: any) {
  //     throw new Error('Method not implemented.')
  // }
  // private convertToRelicCondition(input: Condition): RelicCondition {
  //     throw new Error('Method not implemented.')
  // }
  // private convertToRelicConditionArray(input: Condition[]): RelicCondition[] {
  //     throw new Error('Method not implemented.')
  // }
  // private convertFacilitySearchQuery(query: any) {
  //     throw new Error('Method not implemented.')
  // }
  // private convertCarePlanSearchQuery(query: any) {
  //     throw new Error('Method not implemented.')
  // }
  // private convertFocusesSearchQuery(query: any) {
  //     throw new Error('Method not implemented.')
  // }
  // private async getFocusById(focusId: any, query: any): Promise<RelicFocus> {
  //     throw new Error('Method not implemented.')
  // }

  /**
   * search list of patients
   * @param req
   * @param rep
   * @returns
   */
  async searchPatients(query: any): Promise<{ total: number; data: RelicPatient[] }> {
    const filter: any = this.convertPatientSearchQuery(query);
    this.client.setAccessToken(requestContext.get('accessToken' as never) as string);
    const patientBundle: Bundle = await this.client.search('Patient', filter);
    const total: number = (await this.client.search('Patient', { ...filter, _summary: 'count' })).total;
    const allRelicPatients: RelicPatient[] = [];
    const allPatients: any[] = patientBundle?.entry || [];
    await Promise.all(
      allPatients.map(async (patientResource: BundleEntry, index) => {
        const patient = patientResource.resource as Patient;
        const relicPatient: RelicPatient = null;
        allRelicPatients[index] = this.fastify.toRelicPatient(patient, relicPatient)
      }),
    );
    return { total, data: allRelicPatients };
  }

  async getPatientById(id: string, options?: any): Promise<RelicPatient> {
    this.client.setAccessToken(requestContext.get('accessToken' as never) as string);
    const patientResponse: Bundle = await this.client.readPatientEverything(id);
    let fhirPatient: Patient = null;
    let encounters: Encounter[] = [];
    let conditions: Condition[] = [];
    let relatedPersons: RelatedPerson[] = [];
    let observations: Observation[] = [];
    let organization: RelicOrganization = null;

    if (patientResponse.entry) {
      for (const entry of patientResponse.entry) {
        const resource = entry.resource;
        if (resource.resourceType === 'Patient') {
          fhirPatient = resource as Patient;
        }
        if (resource.resourceType === 'Encounter') {
          encounters.push(resource as Encounter);
        }
        if (resource.resourceType === 'Condition') {
          conditions.push(resource as Condition);
        }
        if (resource.resourceType === 'RelatedPerson') {
          relatedPersons.push(resource as RelatedPerson);
        }
        if (resource.resourceType === 'Observation') {
          observations.push(resource as Observation);
        }
      }
    } else {
      this.fastify.httpErrors.notFound('Patient not found');
    }
    if (fhirPatient.managingOrganization) {
      const organizationId: string = fhirPatient.managingOrganization.reference.split('/')[1];
      organization = await this.fastify.orgService.getOrganization(organizationId);
    } else {
      throw this.fastify.httpErrors.preconditionFailed('Patient not associated with any organization or facility.');
    }
    if (!organization) {
      throw this.fastify.httpErrors.preconditionFailed(
        `Patient Organization - ${fhirPatient.managingOrganization.reference.split('/')[1]} is not on-boarded.`,
      );
    }
    let relicPatient: RelicPatient = await this.fastify.upsertRelicPatient(fhirPatient);
    return relicPatient;
  }

  async getPatientConditions(patientId: string, query: any): Promise<{ total: number; data: RelicCondition[] }> {
    return null;
  }

  async getPatientCarePlans(patientId: string, query: any): Promise<{ total: number; data: RelicCarePlan[] }> {
    throw new Error('Method not implemented.');
  }
  async getCarePlanFocuses(focusIds: number[], query: any): Promise<{ total: number; data: RelicFocus[] }> {
    throw new Error('Method not implemented.');
  }

  /**
   * Retrieves the organization associated with a Medplum practitioner.
   * @param {string} practitionerRef - The reference to the practitioner (e.g., 'Practitioner/123').
   * @param {MedplumClient} medplumClient - The Medplum client to fetch FHIR resources.
   * @returns {Promise<RelicOrganization>} The organization associated with the practitioner.
   */
  private async getMedplumPractitionerOrganization(practitionerRef: string, medplumClient: MedplumClient): Promise<RelicOrganization> {
    if (!practitionerRef) {
      throw this.fastify.httpErrors.badRequest("practitionerRef is required.");
    }
    const practitionerRole = await medplumClient.searchOne('PractitionerRole', `practitioner=${practitionerRef}`);
    if (!practitionerRole || !practitionerRole.organization?.reference) {
      throw this.fastify.httpErrors.badRequest(`Your account is not associated with any organization. Please contact your administrator.`);
    }
    const orgRef = practitionerRole.organization.reference;
    const orgId = orgRef.split('/')[1];
    const organization = await this.fastify.orgService.getOrganization(orgId);
    if (!organization) {
      throw this.fastify.httpErrors.badRequest(`Your organization (id: ${orgId}) is not registered. Please contact your administrator.`);
    }
    return organization;
  }

  async searchFacilities(orgIds: string[], query: any): Promise<{ total: number; data: Location[] }> {
    throw new Error('Method not implemented.');
  }
  public async getFacilityById(id: string): Promise<Location> {
    throw new Error('Method not implemented.');
  }

  async whoAmI(tokenPayload: SignPayloadType): Promise<IUser> {
    if (!tokenPayload) {
      throw this.fastify.httpErrors.unauthorized('Missing or invalid login. Please login again.');
    }

    // Medplum specific process to obtain profile and set medplum client.
    const { payload: decodedJwtToken } = tokenPayload as { header: object; payload: object };
    const { profile } = decodedJwtToken as { profile: string };
    const accessToken = requestContext.get('accessToken');
    this.client.setAccessToken(accessToken);
    
    // For Medplum, we set the org to RelicCare Organization initially.
    let myRelicOrg = await this.fastify.orgService.getRelicCareOrganization();

    let myMedplumProfile: ProfileResource = null;
    let myRelicProfile: RelicPractitioner = null;
    let role: IRole = null;
    const id = profile.split('/')[1];
    let language: RelicCommunicationLanguage;
    if (profile.includes('Practitioner')) {
      myRelicOrg = await this.getMedplumPractitionerOrganization(profile, this.client);
      myMedplumProfile = await this.client.readResource('Practitioner', id);
      myRelicProfile = await this.practitionersCollection.findOne({
        id: myMedplumProfile.id,
      });
      if (!myRelicProfile) {
        throw this.fastify.httpErrors.unauthorized('User not found');
      }
      if (!myRelicProfile.enabled) {
        throw this.fastify.httpErrors.unauthorized(
          'Your account has been disabled. Please contact your administrator.',
        );
      }
      const myLanguage = myMedplumProfile.communication?.[0].coding?.[0];
      language = myLanguage
        ? {
            system: myLanguage.system,
            code: myLanguage.code,
            display: myLanguage.display,
            preferred: true,
          }
        : myRelicOrg?.fhirStore?.defaultLanguage;
      role =
        myRelicProfile.role || this.client.isProjectAdmin() || this.client.isSuperAdmin()
          ? { name: 'admin' }
          : { name: 'member' };
    }
    if (profile.includes('ClientApplication')) {
      const systemAgent = await this.fastify.getSystemAgent();
      myMedplumProfile = await this.client.readResource('Practitioner', systemAgent.id);
      myRelicProfile = await this.practitionersCollection.findOne({
        id: myMedplumProfile.id,
      });
      if (!myRelicProfile.enabled) {
        throw this.fastify.httpErrors.unauthorized(
          'Your account has been disabled. Please contact your administrator.',
        );
      }
      language = myRelicOrg?.fhirStore?.defaultLanguage;
      role =
        myRelicProfile.role || this.client.isProjectAdmin() || this.client.isSuperAdmin()
          ? { name: 'admin' }
          : { name: 'member' };
    }
    let email: string = myMedplumProfile?.telecom?.find((t) => t.system === 'email' && t.use === 'work')?.value || '';
    email = email ?? (myMedplumProfile?.telecom?.find((t) => t.system === 'email' && t.use === 'home')?.value || '');
    email = email ?? (myMedplumProfile?.telecom?.find((t) => t.system === 'email' && t.use === 'mobile')?.value || '');
    let mobilePhone: string =
      myMedplumProfile?.telecom?.find((t) => t.system === 'phone' && t.use === 'mobile')?.value || '';
    mobilePhone =
      mobilePhone ?? (myMedplumProfile?.telecom?.find((t) => t.system === 'phone' && t.use === 'work')?.value || '');
    mobilePhone =
      mobilePhone ?? (myMedplumProfile?.telecom?.find((t) => t.system === 'phone' && t.use === 'home')?.value || '');
    const userName: string = formatHumanName(myMedplumProfile?.name[0]);

    const myPortalIdentity: IPortalIdentity = {
      email: email,
      header: true,
      sider: true,
      name: userName,
      mobilePhone: mobilePhone,
      companyName: myRelicOrg.name,
      companyId: 'deprecated',
      clientId: 'deprecated',
      organizationId: myRelicOrg.id,
      preferredLanguage: language,
    };
    const acsIdentity = await this.fastify.acs.refreshIdentity(
      myRelicProfile,
      myRelicProfile.communicationIdentities?.[0],
    );
    const myUserIdentity: IUserIdentity = {
      id: myRelicProfile.id,
      resourceType: myRelicProfile.resourceType,
      email: myPortalIdentity.email,
      portalIdentity: myPortalIdentity,
      role: role,
      communicationIdentities: [acsIdentity],
      provider: 'medplum',
    };

    const me: IUser = {
      id: myUserIdentity.id,
      name: myUserIdentity.portalIdentity.name || myUserIdentity.portalIdentity.email,
      avatar: '',
      organizationId: myUserIdentity.portalIdentity.organizationId,
      userIdentity: myUserIdentity,
      decodedJwtToken: tokenPayload as object,
    };
    return me;
  }
}

/**
 * Transforms an array of Condition objects into an array of RelicCondition objects.
 * Each Condition object in the input array is converted into a corresponding RelicCondition object.
 *
 * @param {Condition[]} conditions - An array of Condition objects.
 * @returns {RelicCondition[]} An array of transformed RelicCondition objects.
 */
// function transformCondition(conditions: Condition[]): RelicCondition[] {
//   return conditions.map((condition) => conditionToRelicCondition(condition));
// }

/**
 * Transforms an array of RelatedPerson objects into an array of RelicLink objects.
 * Each RelatedPerson object in the input array is converted into a corresponding RelicLink object
 *
 * @param {RelatedPerson[]} link - An array of RelatedPerson objects.
 * @returns {RelicLink[]} An array of transformed RelicLink objects.
 */
// function transformRelatedPerson(link: RelatedPerson[]): RelicLink[] | [] {
//   let relatedPerson: RelicLink[] | [];
//   if (link[0] != null) {
//     relatedPerson = link.map((link) => relatedPersonToRelicRelatedPerson(link));
//   } else {
//     relatedPerson = [];
//   }
//   return relatedPerson;
// }

/**
 * Converts a FHIR RelatedPerson object to a RelatedPerson object.
 *
 *
 * @param {RelatedPerson} relatedPerson - The FHIR RelatedPerson object to be converted.
 * @returns {RelatedPerson} - The converted RelatedPerson object.
 *
 */
export function relatedPersonToRelicRelatedPerson(relatedPerson: RelatedPerson): RelicLink | null {
  if (!relatedPerson) return null;
  const link: RelicLink = {
    id: relatedPerson.id,
    resourceType: relatedPerson.resourceType || '',
    relationship: {
      system: relatedPerson.relationship?.[0]?.coding?.[0]?.system || '',
      code: relatedPerson.relationship?.[0]?.coding?.[0]?.code || '',
      display: relatedPerson.relationship?.[0]?.coding?.[0]?.display || '',
    },
    use: relatedPerson.name?.[0]?.use || '',
    name: relatedPerson.name ? relatedPerson.name?.[0]?.given?.[0] + ' ' + relatedPerson.name[0].family : '',
    email: '',
    mobilePhone: '',
    homePhone: '',
    gender: relatedPerson.gender,
    birthDate: relatedPerson.birthDate || '',
  };

  if (relatedPerson.telecom) {
    relatedPerson.telecom.forEach((t) => {
      if (t.system === 'email') {
        link.email = t.value;
      } else if (t.system === 'phone') {
        if (t.use === 'mobile') {
          link.mobilePhone = t.value;
        } else if (t.use === 'home') {
          link.homePhone = t.value;
        }
      }
    });
  }

  return link;
}
