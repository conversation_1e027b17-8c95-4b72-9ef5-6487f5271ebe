{"$schema": "http://json-schema.org/draft-07/schema#", "definitions": {"CommunicationIdentity": {"type": "object", "properties": {"userId": {"type": "string", "title": "userId"}, "service": {"type": "array", "items": {"type": "string"}, "title": "service"}, "endpoint": {"type": "string", "title": "endpoint"}, "secret": {"type": "object", "properties": {"token": {"type": "string", "title": "token"}, "expiresOn": {"type": "string", "format": "date-time", "title": "expiresOn"}}, "required": ["expiresOn", "token"], "title": "secret"}, "threads": {"type": "array", "items": {"$ref": "#/definitions/Thread"}, "title": "threads"}}, "required": ["endpoint", "secret", "service", "threads", "userId"]}, "Secret": {"type": "object", "properties": {"token": {"type": "string", "title": "token"}, "expiresOn": {"type": "string", "format": "date-time", "title": "expiresOn"}}, "required": ["expiresOn", "token"]}, "Thread": {"type": "object", "properties": {"id": {"type": "string", "title": "id"}, "endpoint": {"type": "string", "title": "endpoint"}, "threadId": {"type": "string", "title": "threadId"}, "threadTopic": {"$ref": "#/definitions/ThreadTopic"}, "threadSubject": {"ref": "#/definitions/ThreadSubject"}, "participants": {"type": "array", "items": {"$ref": "#/definitions/ChatParticipant"}, "title": "participants"}, "status": {"enum": ["active", "closed"], "type": "string", "title": "status"}}, "required": ["endpoint", "threadId", "threadSubject", "threadTopic"]}, "ThreadSubject": {"type": "object", "properties": {"organizationId": {"type": "string", "title": "organizationId"}, "type": {"type": "string", "title": "type"}, "questionnaireId": {"type": "string", "title": "questionnaireId"}}, "required": ["organizationId", "type"]}, "Topic": {"type": "object", "properties": {"currentAgentAcsId": {"type": "string", "title": "currentAgentAcsId"}, "threadOwner": {"type": "object", "properties": {"resourceType": {"type": "string", "title": "resourceType"}, "id": {"type": "string", "title": "id"}}, "required": ["id", "resourceType"], "title": "threadOwner"}, "organizationId": {"type": "string", "title": "organizationId"}, "interactiveWidget": {"$ref": "#/definitions/WidgetRequestWithMessageId"}}, "required": ["currentAgentAcsId", "organizationId", "threadOwner"]}, "ThreadTopic": {"anyOf": [{"$ref": "#/definitions/Topic"}, {"type": "string"}]}, "ThreadOwner": {"type": "object", "properties": {"resourceType": {"type": "string", "title": "resourceType"}, "id": {"type": "string", "title": "id"}}, "required": ["id", "resourceType"]}, "WidgetRequestWithMessageId": {"type": "object", "properties": {"requestMessageId": {"type": "string", "title": "requestMessageId"}, "widgetData": {"title": "widgetData"}}, "required": ["requestMessageId", "widgetData"]}, "ChatParticipant": {"type": "object", "properties": {"id": {"type": "object", "properties": {"communicationUserId": {"type": "string", "title": "communicationUserId"}}, "required": ["communicationUserId"], "title": "id"}, "displayName": {"type": "string", "title": "displayName"}}, "required": ["id"]}}}