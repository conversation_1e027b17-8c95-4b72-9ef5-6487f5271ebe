import { ChatParticipant } from '@azure/communication-chat';
import { CommunicationIdentifier } from '@azure/communication-common';
import { WidgetRequestWithMessageId } from 'relic-ui';
import { RelicCommunicationLanguage } from 'relic-ui';
import { Role } from 'relic-ui';

export type CommunicationIdentity = {
  userId: string;
  displayName?: string;
  service: string[];
  endpoint: string;
  secret: Secret;
  threads: string[];
};

export type CommunicationConfig = {
    service: string[]
    endpoint: string
    provider: string
    accessKey: string
}

export type Secret = {
  token: string;
  expiresOn: Date;
};

export type Thread = {
  endpoint: string;
  threadId: string;
  threadTopic: ThreadTopic;
  threadSubject: ThreadSubject;
  participants: RelicChatParticipant[];
  status: 'active' | 'closed';
  inviteVia: 'email' | 'cellphone' | 'both' | 'none';
  createdBy?: {
    id: string;
    resourceType: string;
  };
  createDate: Date;
  updatedBy?: {
    id: string;
    resourceType: string;
  };
  updateDate?: Date;
};

export type ThreadSubject = {
  organizationId: string;
  questionnaireId?: string;
  threadOwner?: ThreadOwner;
  targetPhoneNumber?: string;
  patientLanguage?: RelicCommunicationLanguage;
  title?: string;
};

export type Topic = {
  currentAgentAcsId: string;
  interactiveWidget?: WidgetRequestWithMessageId;
  error?: Error;
  threadMessage?: string;
};

export type ThreadTopic = string | Topic;

export type ThreadOwner = {
  resourceType: string;
  id: string;
};

export type RelicChatParticipant = ChatParticipant & {
  resourceType?: 'Patient' | 'Practitioner';
  resourceId?: string; //refers to FHIR or RelicCare id
  role?: Role; //AI agents are also Practitioners, if this contains value, then this is an Agent.
  type?: 'Patient Agent' | 'Staff Agent' | 'System Agent'; //AI agents are also Practitioners, if this contains value, then this is an Agent.
  chatLanguage?: RelicCommunicationLanguage;
  mobilePhone?: string;
  speechId?: CommunicationIdentifier;
};

export type AgentFor = {
  id: string;
  resourceType: string;
};

export type ThreadContext = {
  agentPerspective: string;
  patientSummary: string;
  agentSummary: string;
};

export type ThreadsWithCount = {
  threads: Thread[];
  count: number;
};

export interface ThreadQueryParams {
  patientId?: string; // To get thread with a specific patient
  assistantRole?: string; // To get thread with a specific AI assistant role (e.g. 'compliance')
  participants?: {
    // For explicit participant specification
    id: string;
    type: string;
  }[];
};

type FlowState = {
  myResourceName: string; //Input 
  myResourceType: string; //Input
  myMessage?: string; //Output. Input is sent as a chat message.
  myLastMessage?: string; //Input + Output
  patientLanguage?: string; //Input + Output
  turns?: number; //Internal
  error?: Error; //Internal
  ended?: boolean; //Input + Output
};

export type InterpreterFlowState = FlowState & {
  otherResourceName?: string; //Input + Output
  otherResourceType?: string; //Input + Output
  otherLastMessage?: string; //Input
  speakerFaqs?: [{ //Input
    question: string;
    answer: string;
  }];
  messagesForSwitch?: [{ //Output
    content: string;
    forResourceType: string;
    forResourceName: string;
  }];
};
