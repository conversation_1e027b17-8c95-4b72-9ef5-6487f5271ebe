import path from 'path';
import { readFileSync, readdirSync } from 'fs';
import { FastifyInstance, FastifyPluginAsync, FastifyPluginOptions } from 'fastify';
import { LoginState, MedplumClient } from '@medplum/core';
import { requestContext } from '@fastify/request-context';
import { IUserIdentity, AccessPolicy } from 'relic-ui';
import { RelicOrganization } from 'relic-ui';
import practitionerPlugin from './practitionerPlugin';
import { AcsCommunicationService } from '../../services/communication/communication.acs';

declare module 'fastify' {
  interface FastifyInstance {
    attachServerMedplumAccessToken(req: any): Promise<void>;
    createUserIdentity(req: any): Promise<IUserIdentity>;
    acs: AcsCommunicationService;
  }
}

const userPlugin: FastifyPluginAsync = async (fastify: FastifyInstance, options: FastifyPluginOptions) => {
  const accessPolicyDir = `${__dirname}/../../utils/accessPolicy`;
  const accessPolicies: Map<string, AccessPolicy> = new Map();
  readdirSync(accessPolicyDir).forEach((policyFile) => {
    const policy = JSON.parse(readFileSync(path.join(accessPolicyDir, policyFile)).toString()) as AccessPolicy;
    accessPolicies.set(policy.name as string, policy);
  });

  async function getUserType(accessToken: string): Promise<string> {
    const accessTokenDecoded: any = requestContext.get('decodedJwtToken' as never);
    if (accessToken && accessTokenDecoded?.payload?.iss.includes('PointClickCare')) {
      return 'StaffMember';
    }
    if (accessToken && accessTokenDecoded?.payload?.iss.includes('sts.windows.net')) {
      return 'StaffMember';
    }
    if (accessToken && accessTokenDecoded?.payload?.iss.includes('patientauth.b2clogin.com')) {
      return 'Patient';
    }
    if (accessToken && accessTokenDecoded?.payload?.iss.includes('api.medplum.com')) {
      const medplumClient = new MedplumClient();
      await medplumClient.setActiveLogin({ accessToken } as LoginState);
      const me = await medplumClient.getProfileAsync();
      const myResourceType = me?.resourceType;
      if (myResourceType == 'Practitioner') {
        return medplumClient.isSuperAdmin() || medplumClient.isProjectAdmin() ? 'StaffAdmin' : 'StaffMember';
      } else if ((myResourceType as string) == 'ClientApplication') {
        return 'AIStaff';
      } else {
        return myResourceType;
      }
    }
  }

  fastify.decorate('getMyAccessPolicy', async function (): Promise<AccessPolicy> {
    if (!requestContext.get('accessToken')) {
      throw fastify.httpErrors.unauthorized('Missing access token. Access token is required for this operation.');
    }
    const userType = await getUserType(requestContext.get('accessToken'));
    return accessPolicies.get(userType) as AccessPolicy;
  });

  // acs decorator for AcsCommunicationService
  fastify.decorate('acs', new AcsCommunicationService(fastify, options));
  fastify.log.info('acs decorator successful.');

  // Used by demo api handlers only. Should eventually be removed since we support provider based routes now.
  fastify.decorate('attachServerMedplumAccessToken', async function (req: any) {
    const medplumClient = new MedplumClient();
    const relicOrganization = await fastify.orgService.getRelicCareOrganization();
    const clientSecret = fastify.getCredentials(
      (relicOrganization as RelicOrganization).fhirStore.clientApplication.id,
    );
    if (clientSecret) {
      await medplumClient.startClientLogin(
        (relicOrganization as RelicOrganization).fhirStore.clientApplication.id,
        clientSecret.secret,
      );
    } else {
      throw req.server.httpErrors.internalServerError(
        'Medplum credentials not found for the organization. Please contact your administrator.',
      );
    }
    const accessToken = medplumClient.getAccessToken();
    //reset x-access-token to a Medplum AccessToken
    requestContext.set('accessToken' as never, accessToken as never);
    req.headers['x-access-token'] = accessToken;
  });

  fastify.register(practitionerPlugin, options);
};

export default userPlugin;
