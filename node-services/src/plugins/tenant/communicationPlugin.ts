import { FastifyInstance, FastifyPluginAsync, FastifyPluginOptions } from 'fastify'
import { RelicPatient } from 'relic-ui'
import { RelicPractitioner } from 'relic-ui'
import {
    CommunicationIdentity,
    RelicChatParticipant,
    Thread,
    Topic,
    ThreadsWithCount,
} from 'relic-ui'
import {
    CreateChatThreadRequest,
    CreateChatThreadOptions,
    ChatParticipant,
    ChatClient,
} from '@azure/communication-chat'
import { UnknownIdentifier, AzureCommunicationTokenCredential, PhoneNumberIdentifier, CommunicationIdentifier } from '@azure/communication-common'
import { RelicOrganization } from 'relic-ui'
import { RelicAgent } from 'relic-ui'
import { requestContext } from '@fastify/request-context'
import { IUserIdentity } from 'relic-ui'
import { constructThreadTitle } from '../../types/commonUtils'
import documentsPlugin from './documentsPlugin'

/* internal types and constants */
type GetCommunicationIdentitiesByIdFunction = (id: string) => Promise<CommunicationIdentity>

type ICommunicationPlugin = {
    getCommunicationIdentityById: GetCommunicationIdentitiesByIdFunction
}

declare module 'fastify' {
    interface FastifyInstance {
        // Used only in routes.
        getCommunicationIdentityById: GetCommunicationIdentitiesByIdFunction
        // Used only in routes.
        getUserThreadById: (threadId: string) => Promise<Thread>
        // Used only in routes.
        getUserThreads: (filters: any) => Promise<ThreadsWithCount>
        // Used only in routes.
        createThreadV2: (thread: Thread) => Promise<Thread>
        // Used only in routes.
        updateUserThread: (threadId: string, thread: Thread) => Promise<Thread>
    }
}
/**
 * Plugin API to work with Relic Care Communication Services.
 * Communication Services are provided by Azure Communication Services.
 * This plugin is a convenience wrapper for Azure Communication Services service methods.
 */

const communicationPlugin: FastifyPluginAsync = async (fastify: FastifyInstance, options: FastifyPluginOptions) => {
    const threadsCollection = fastify.mongo.reliccare.db.collection<Thread>("threads");

    const practitionerCommunicationIdentityInstance: ICommunicationPlugin = {
        getCommunicationIdentityById: async function (id: string): Promise<CommunicationIdentity> {
            return await fastify.getPractitionerCommunicationIdentity(id)
        },
    }

    const patientCommunicationIdentityInstance: ICommunicationPlugin = {
        getCommunicationIdentityById: async function (id: string): Promise<CommunicationIdentity> {
            return await fastify.getPatientCommunicationIdentity(id)
        },
    }

    const agentCommunicationIdentityInstance: ICommunicationPlugin = {
        getCommunicationIdentityById: async function (id: string): Promise<CommunicationIdentity> {
            return ((await (fastify as any).getRelicAgent(id, {})) as CommunicationIdentity[])?.[0]
        },
    }

    const getCommunicationIdentityInstance = function (resourceType: string): ICommunicationPlugin {
        if (resourceType === 'Practitioner') {
            return practitionerCommunicationIdentityInstance
        }
        if (resourceType === 'Patient') {
            return patientCommunicationIdentityInstance
        }
        if (resourceType === 'ClientApplication') {
            return agentCommunicationIdentityInstance
        }
        throw Error(`${resourceType} not supported`)
    }

    const createTopic = function (ownerId: string, ownerType: string, organizationId: string, agentId: string): Topic {
        const topic: Topic = {
            currentAgentAcsId: agentId
        }
        return topic
    }

    /**
     * Validates a list of chat participants by checking their existence and retrieving their resource details.
     * Chat participants can be defined by their acs id, relic care id, phone or email id.
     * 
     * @param {RelicChatParticipant[]} [participants] - An optional array of participants to validate.
     * @returns {Promise<RelicChatParticipant[]>} A promise that resolves to an array of validated participants.
     * @throws {HttpError} Throws a bad request error if any invalid participants are found.
     */
    const validateParticipants = async function (participants?: RelicChatParticipant[]): Promise<RelicChatParticipant[]> {
        const validatedParticipants: RelicChatParticipant[] = [];
        if (!participants || participants.length === 0) {
            return validatedParticipants;
        }
        if (!participants.every((p) => p.resourceType)) {
            throw fastify.httpErrors.badRequest('Participant resourceType missing.');
        }
        let validatedParticipant: RelicChatParticipant;
        let relicPatient: RelicPatient;
        let relicPractitioner: RelicPractitioner;
        let relicAgent: RelicAgent;
        let communicationIdentifier: CommunicationIdentifier;
        await Promise.all(participants.map(async (participant) => {
            const participantId = participant?.resourceId ?? (participant?.id as UnknownIdentifier).id;
            if (participant.resourceType === 'Patient') {
                relicPatient = await fastify.relicPatients().findOne({
                    $or: [
                    { "id": participantId },
                    { "communicationIdentities.userId": participantId },
                    { "email": participantId },
                    { "mobilePhone": participantId },
                    ]
                });
                if (!relicPatient) {
                    throw fastify.httpErrors.badRequest('Invalid patient participant found.');
                }
                if (relicPatient.mobilePhone === participantId) {
                    communicationIdentifier = { phoneNumber: participantId };
                } else {
                    communicationIdentifier = { communicationUserId: relicPatient.communicationIdentities[0].userId };
                }
                validatedParticipant = {
                    resourceId: relicPatient.id,
                    resourceType: 'Patient',
                    displayName: participant.displayName,
                    id: communicationIdentifier,
                    chatLanguage: participant.chatLanguage,
                    mobilePhone: participant.mobilePhone,
                };
            }
            if (participant.resourceType === 'Practitioner') {
                try {
                    // First check if this practitioner is an agent
                    relicAgent = await (fastify as any).getRelicAgent(participantId);
                    if (relicAgent.mobilePhone === participantId) {
                        communicationIdentifier = { phoneNumber: participantId };
                    } else {
                        communicationIdentifier = { communicationUserId: relicAgent.communicationIdentities[0].userId };
                    }
                    validatedParticipant = {
                        resourceId: relicAgent.id,
                        resourceType: 'Practitioner',
                        displayName: relicAgent.name,
                        id: communicationIdentifier,
                        role: relicAgent.role,
                        type: relicAgent.type,
                        chatLanguage: {code: "en-US", display: "English (United States)"},
                        mobilePhone: relicAgent.mobilePhone
                    };
                } catch (error) {
                    // If not an agent, then check if this practitioner is a relic practitioner
                    relicPractitioner = await fastify.getPractitioner(participantId)
                    if (!relicPractitioner) {
                        throw fastify.httpErrors.badRequest('Invalid practitioner participant found.');
                    }    
                    if (relicPractitioner.mobilePhone === participantId) {
                        communicationIdentifier = { phoneNumber: participantId }
                    } else {
                        communicationIdentifier = { communicationUserId: relicPractitioner.communicationIdentities[0].userId }
                    }
                    validatedParticipant = {
                        resourceId: relicPractitioner.id,
                        resourceType: 'Practitioner',
                        displayName: relicPractitioner.name,
                        id: communicationIdentifier,
                        chatLanguage: {code: "en-US", display: "English (United States)"},
                        mobilePhone: relicPractitioner.mobilePhone
                    }
                }
            }
            validatedParticipants.push(validatedParticipant);
        }));
        if (participants.length !== validatedParticipants.length) {
            throw fastify.httpErrors.badRequest('Invalid participant(s) found.');
        };
        return validatedParticipants;
    }

    const getExistingThread = async function (participants: ChatParticipant[], organizationId: string): Promise<Thread> {
        return await threadsCollection.findOne({
            status: 'active',
            'threadSubject.organizationId': organizationId,
            'participants.id.communicationUserId': { $all: participants.map((p: any) => p.id) }
        })
    }

    fastify.decorate('getCommunicationIdentityById', async function (id: string): Promise<CommunicationIdentity> {
        const whoami: IUserIdentity = fastify.requestContext.get('whoami')
        if (!whoami) {
            throw fastify.httpErrors.unauthorized('User identity not found in request context.');
        }
        const resourceType = whoami.resourceType;
        const instance: ICommunicationPlugin = getCommunicationIdentityInstance(resourceType)
        return await instance.getCommunicationIdentityById(id)
    })


    /**
     * Gets the thread by threadId. If mustIncludeAcsId is provided, it will also check if the user is part of the thread.
     * This prevents unauthorized access to threads where a non-participant tries to access the thread.
     * @param {string} threadId - Id of the thread
     * @returns {Promise<Thread>} Thread object
     **/
    fastify.decorate('getUserThreadById', async function (threadId: string): Promise<Thread> {
        const filter = { threadId: threadId }
        const whoami: IUserIdentity = fastify.requestContext.get('whoami')
        if (!whoami.role || whoami.role.name !== 'admin') {
            //if role is missing (patient access) or role is not admin
            filter['participants.resourceId'] = whoami.id
        }
        const thread: Thread = await threadsCollection.findOne(filter);
        if (!thread) {
            throw fastify.httpErrors.notFound(`Thread id - ${threadId} not found or unauthorized.`);
        }
        // Populate default title if missing, null, or blank
        if (!thread.threadSubject.title) {
            thread.threadSubject.title = constructThreadTitle(thread);
        }
        //Need to get the thread from ACS and populate topic & participants from ACS thread.
        //return await getCombinedThread(thread);
        //Returning relicThread directly as it has everything needed by relic-ui.
        return thread
    })

    fastify.decorate('getUserThreads', async function (filters: any): Promise<ThreadsWithCount> {
        const whoami: IUserIdentity = fastify.requestContext.get('whoami');
        const org = requestContext.get('organization' as never) as RelicOrganization;
        const pipeline = [];
        const validatedParticipants: RelicChatParticipant[] = await validateParticipants(filters.relicChatParticipants);
        //If a phone number is participating, do not return existing threads. Phone calls need to always create new thread.
        if (validatedParticipants.some(participant => (participant.id as PhoneNumberIdentifier).phoneNumber)) {
            return { threads: [], count: 0 };
        }
        //If there are two human participants, then thread is always patient-practitioner
        if (validatedParticipants.filter(p => !p?.type).length === 2) {
            filters.type = 'Caregiver';
        }
        const participantFilter = {}
        //Pipeline filter 1 - Match threads where they are participating for non admins
        if (whoami.role.name != 'admin') {
            pipeline.push({
                $match: {
                    $and: [
                        { 'participants.resourceId': whoami.id },
                        {
                            'participants.id.communicationUserId': whoami.communicationIdentities?.find(
                                (v) => v.endpoint === org.endpoints?.find((v) => v.service.includes('chat'))?.endpoint
                            )?.userId
                        }
                    ]
                }
            })
        }
        //Pipeline filter 2 - Filter as per queried status (if any)
        if (filters.status) {
            pipeline.push({ $match: { status: filters.status } })
        }
        //Pipeline filter 3 - Filter as per queried questionnaireId (if any)
        if (filters.questionnaireId) {
            pipeline.push({ $match: { 'threadSubject.questionnaireId': filters.questionnaireId } })
        }
        //Pipeline filter 4 - Filter as per queried threadId (if any)
        if (filters.threadId) {
            pipeline.push({ $match: { threadId: filters.threadId } })
        }
        //Pipeline filter 5 - Filter as per queried thread type (if any) + add counterpart info for caregiver threads
        if (filters.type && filters.type == 'Caregiver') {
            //For caregiver threads, obtain counterpart info
            let counterpartcollection = 'patients'
            if (whoami.resourceType == 'Patient') {
                counterpartcollection = 'practitioners'
            }
            pipeline.push({
                $lookup: {
                    from: counterpartcollection,
                    localField: 'participants.id.communicationUserId',
                    foreignField: 'communicationIdentities.userId',
                    as: 'counterpartInfo'
                }
            })
            pipeline.push({
                $unwind: {
                    path: '$counterpartInfo',
                    preserveNullAndEmptyArrays: false
                }
            })
        }
        //Pipeline filter 6 - Filter as per validatedParticipants (translated from filters.relicChatParticipants) if any
        if (validatedParticipants.length > 0) {
            participantFilter['participants.resourceId'] = { $all: validatedParticipants.map((p) => p.resourceId) }
            pipeline.push({ $match: participantFilter })
        }
        //Pipeline filter 7 -  Filter as per queried organizationId (if any)
        if (filters.organizationId) {
            pipeline.push({ $match: { 'threadSubject.organizationId': filters.organizationId } })
        }
        //Pipeline last step - Sorting and Ordering
        if (filters.sort && filters.order) {
            pipeline.push({ $sort: { [filters.sort]: (filters.order == 'asc') ? 1 : -1 } });
        } else {
            pipeline.push({ $sort: { ['updateDate']: -1 } });
        }

        const pipelineCount = pipeline.slice()
        pipelineCount.push({ $count: 'count' })
        pipeline.push({ $group: { _id: '$threadId', data: { $first: '$$ROOT' } } })
        pipeline.push({ $replaceRoot: { newRoot: '$data' } })
        pipeline.push({ $skip: filters.offset ?? 0 })
        pipeline.push({ $limit: filters.limit ?? 25 })
        const $facets = []
        $facets.push({
            $facet: {
                threads: [...pipeline],
                count: [...pipelineCount]
            }
        })
        const result: ThreadsWithCount[] = await threadsCollection
            .aggregate([...$facets])
            .toArray() as ThreadsWithCount[];
        // Populate default title if missing, null, or blank
        result[0].threads.forEach(thread => {
            if (!thread.threadSubject.title) {
                thread.threadSubject.title = constructThreadTitle(thread);
            }
        });
        return result[0];
    })

    /**
     * Create a new thread using the organization's AI Assistant.
     *
     * @param thread - The thread to be created. Must contains a threadSubject and a participants array.
     *  - The threadSubject must be present and valid containing { organizationId, type }. ThreadSubject is patched for {threadOwner and Language} if missing.
     *  - Chat participants must be present and valid. Participants are patched for { displayName, acs id and role} if these are missing.
     *  - For default thread type
     *      - Configured Patient or Practitioner Assistant is added to the participants if missing.
     *      - Thread topic is created with default Patient or Practitioner Assistant. 
     *  - The default AI Assistant is added to the participants if missing.
     * @returns - The newly created thread.
     * @throws - BadRequestError if the thread does not contain mandatory fields or if a bad field is found.
     */
    fastify.decorate('createThreadV2', async function (thread: Thread): Promise<Thread> {
        let organization: RelicOrganization = requestContext.get('organization' as never) as RelicOrganization;
        const me: IUserIdentity = fastify.requestContext.get('whoami');
        const aiAsst: RelicAgent = await (fastify as any).getSystemAgent();
        const aiAsstCommIdentity: CommunicationIdentity = aiAsst?.communicationIdentities?.[0];
        const aiAsstChatClient = new ChatClient(
            aiAsstCommIdentity.endpoint,
            new AzureCommunicationTokenCredential(aiAsstCommIdentity.secret.token)
        );
        //validate threadSubject and patch
        if (thread.threadSubject) {
            if (!thread.threadSubject.organizationId) {
                throw fastify.httpErrors.badRequest('Mising thread organization.');
            }
            if (thread.threadSubject.threadOwner && thread.threadSubject.threadOwner.id !== me.id) {
                throw fastify.httpErrors.badRequest('Thread owner mismatch with logged in user.');
            } else {
                thread.threadSubject.threadOwner = { id: me.id, resourceType: me.resourceType };
            }
        }
        //validate organization and patch
        if (thread.threadSubject.organizationId !== organization.id) {
            organization = await fastify.orgService.getOrganization(thread.threadSubject.organizationId);
        }
        if (!organization) {    
            throw fastify.httpErrors.badRequest('Organization not found.');
        }
        //validate participants, add AI assistant if missing, patch displayName(as well as id and role) if missing, construct topic
        if (thread.participants && thread.participants.length > 0) {
            thread.participants = await validateParticipants(thread.participants);
            const participantAgent = thread.participants.find((p) => p.resourceType === 'Practitioner' && p.type && p.type !== 'System Agent')
            const participantAgentAcsId = participantAgent && (participantAgent.id as { communicationUserId: string }).communicationUserId
            const topic: Topic = createTopic(
                thread.threadSubject.threadOwner && thread.threadSubject.threadOwner.id,
                thread.threadSubject.threadOwner && thread.threadSubject.threadOwner.resourceType,
                thread.threadSubject.organizationId,
                participantAgent && participantAgentAcsId,
            )
            thread.threadTopic = topic;
        } else {
            throw fastify.httpErrors.badRequest('Participants missing.');
        }
        //validate patientLanguage and patch
        if (!thread.threadSubject.patientLanguage) {
            const patientParticipant = thread.participants.find((p) => p.resourceType === 'Patient');
            if (patientParticipant && patientParticipant.chatLanguage) {
                thread.threadSubject.patientLanguage = patientParticipant.chatLanguage;
            } else {
                thread.threadSubject.patientLanguage = organization.fhirStore.defaultLanguage;
            }
        }
        //Patch target phone number for the thread.
        if (!thread.threadSubject.targetPhoneNumber) {
            const patientParticipant = thread.participants.find((p) => p.resourceType === 'Patient');
            const practitionerParticipant = thread.participants.find((p) => p.resourceType === 'Practitioner' && !p.role && !p.type);
            if (patientParticipant && patientParticipant?.mobilePhone) {
                thread.threadSubject.targetPhoneNumber = patientParticipant.mobilePhone;
            } else {
                thread.threadSubject.targetPhoneNumber = practitionerParticipant?.mobilePhone;
            }
        }
        //if ai assistant is missing, add it.
        if (!thread.participants.find((p) => p.resourceId === aiAsst.id)) {
            thread.participants.push({
                id: { communicationUserId: aiAsst.communicationIdentities[0].userId },
                displayName: aiAsst.name,
                resourceId: aiAsst.id,
                resourceType: aiAsst.resourceType,
                role: aiAsst.role,
                type: aiAsst.type,
            });
        }
        //check if thread already exists
        const existingThread = await getExistingThread(thread.participants, thread.threadSubject.organizationId);
        if (existingThread) {
            return existingThread;
        }
        //create thread
        const request: CreateChatThreadRequest = { topic: JSON.stringify(thread.threadTopic) };
        const options: CreateChatThreadOptions = { participants: thread.participants };
        const acsThread = await aiAsstChatClient.createChatThread(request, options);
        thread.endpoint = aiAsstCommIdentity.endpoint;
        thread.threadId = acsThread.chatThread.id;
        thread.createDate = thread.createDate ?? new Date();
        thread.updateDate = thread.createDate ?? new Date();
        thread.createdBy = thread.createdBy ?? { id: me.id, resourceType: me.resourceType };
        thread.updatedBy = thread.updatedBy ?? { id: me.id, resourceType: me.resourceType };
        await threadsCollection.insertOne(thread);
        return thread
    })

    fastify.decorate('updateUserThread', async function (threadId: string, updatedThread: Thread): Promise<Thread> {
        const update: Thread = await fastify.getUserThreadById(threadId) //get db and acs data

        if (updatedThread.status) {
            update['status'] = updatedThread.status
        }
        if (updatedThread.threadSubject?.patientLanguage) {
            update.threadSubject.patientLanguage = updatedThread.threadSubject.patientLanguage;
        }
        if (updatedThread.threadSubject?.targetPhoneNumber) {
            update.threadSubject.targetPhoneNumber = updatedThread.threadSubject.targetPhoneNumber;
        }
        const result = await threadsCollection
            .findOneAndUpdate({ threadId: threadId }, { $set: update }, { returnDocument: 'after', includeResultMetadata: true })

        // if (updatedThread.status === 'closed') {
        //     const filter = { 'communicationIdentities.threads': threadId }
        //     const update = { $pull: { 'communicationIdentities.$.threads': threadId } }
        //     const opts = { returnDocument: 'after', includeResultMetadata: true }
        //     //remove from resource (patient/practitioner) thread list
        //     let deleteResult = await encapsulatedInstance.relicPatients().findOneAndUpdate(filter, update, opts)
        //     if (!(deleteResult.lastErrorObject?.updatedExisting as boolean)) {
        //         await encapsulatedInstance.relicPractitioners().findOneAndUpdate(filter, update, opts)
        //     }
        // }
        return result.value as Thread
    })

    fastify.register(documentsPlugin, options);

}

export default communicationPlugin;