import { FastifyInstance, FastifyPluginAsync, FastifyPluginOptions } from 'fastify';
import config from 'config';
import { requestContext } from '@fastify/request-context';
import { IUserIdentity } from 'relic-ui';
import { BlobServiceClient, ContainerClient } from '@azure/storage-blob';
import { DocumentUsers, RelicDocument, PccMetaData } from '@/types/document';
import { PccPatientCarePlan, PccCarePlanFocus, PccCarePlanFocusResponse } from '@/types/pcc';
import path from 'path';
import crypto, { randomUUID } from 'crypto';
import { existsSync, mkdirSync, readdirSync, rmSync } from 'fs';
import { TranslateDocumentOptions, TranslateDocumentResult, generateTranslatedDocument } from '../../utils/pdf/fileTranslator';
import * as tar from 'tar';
import { homedir } from 'node:os';
import { resolve } from 'node:path';
import { generateTranslatedCarePlanJson } from '../../utils/pdf/textTranslator';
import { httpErrors } from '@fastify/sensible';
import { RelicMessageQueue } from '../../services/relic.queue';
import { PccService } from "../../services/ehr.pcc";
import routeProviderPlugin from './routeProviderPlugin';

declare module 'fastify' {
    interface FastifyInstance {
        // called by router
        searchDocuments(searchOptions: any): Promise<{ data: RelicDocument[], total: number }>;
        // called by router
        getDocumentById(id: string): Promise<RelicDocument>;
        // called by router
        saveDocument(resource: RelicDocument): Promise<RelicDocument>;
        // called by router
        deleteDocument(id: string): Promise<RelicDocument>;
        // called by router
        updateDocument(id: string, doc: RelicDocument): Promise<RelicDocument>;
        // called by router
        createTranslatedDocument(id: string, options: any): Promise<RelicDocument>;
        // called by route hook
        processCarePlans(patientId: string, query: any, searchOptions: any): Promise<PccPatientCarePlan[]>;
        // called by route hook
        getCarePlanDetails(carePlanDocument: RelicDocument): Promise<RelicDocument>;
    }
}

const documentsPlugin: FastifyPluginAsync = async (fastify: FastifyInstance, options: FastifyPluginOptions) => {
    const ROOT_DOWNLOADS_FOLDER = resolve(homedir(), 'downloads');
    const ARCHIVE_TYPE = 'tar';
    const AZ_STORAGE_TEXTBLOCKS_PREFIX = 'documents_textblocks';
    const translateJobName = "translate";

    const pccService = new PccService(fastify, options);

    const translatorWorkQueue = RelicMessageQueue.CreateQueueForWorker('DOCUMENT_TRANSLATOR');
    translatorWorkQueue.attachWorkerToQueue(async (job) => {
        const { documentId } = job.data;
        if (!documentId) {
            throw fastify.httpErrors.badRequest('Document ID is required.');
        }
        await translateDocument(documentId);
    });

    const relicDocuments = fastify.mongo.reliccare.db.collection<RelicDocument>('documents');

    fastify.decorate("searchDocuments", async function (searchOptions: any): Promise<{ data: RelicDocument[], total: number }> {
        const { limit, skip, patientId, sort, order, search, resourceType, carePlanId } = searchOptions;
        let filter = { ...searchOptions };
        delete filter['limit'];
        delete filter['skip'];
        delete filter['patientId'];
        delete filter['sort'];
        delete filter['order'];
        delete filter['search'];
        delete filter['resourceType'];
        delete filter['carePlanId'];
        if (search) {
            filter["filename"] = { "$regex": search, "$options": "i" };
        }
        if (resourceType) {
            filter["access.resourceType"] = resourceType;
        }
        if (patientId) {
            if (carePlanId) {
                filter["access.id"] = { "$all": [patientId, carePlanId] };
            } else {
                filter["access.id"] = patientId;
            }
            //temporary fix for https://reliccare.atlassian.net/browse/CB1-613. Search should auto clear on tab-click.
            delete filter["filename"];
        } else {
            filter["access.resourceType"] = { "$nin": ["Patient", "CarePlan"] }; //assume that request came from Facility > Documents
        }
        const finalFilter = { ...filter, deleted: false, $expr: { $eq: ["$id", "$sourceDocumentId"] } };
        let matchFilter: any = [{ "$match": finalFilter }]; //obtain original files first
        const sortAggregate = [];
        let sortArrayBy = {
            "isUploaded": -1, //original file first
        }
        if (sort && order) {
            let sortOption = {};
            sortOption[sort] = (order == 'asc') ? 1 : -1;
            sortAggregate.push({ "$sort": sortOption });
            sortArrayBy[sort] = sortOption[sort];
        }
        const options = { "collation": { "locale": 'en_US', "strength": 2 } };
        const relicDocumentsAggregate = getRelicDocumentsAggregate(sortArrayBy, filter["access.id"] ?? '');
        const data = await relicDocuments.aggregate([
                ...matchFilter,
                ...sortAggregate,
                { "$skip": skip },
                { "$limit": limit },
                ...relicDocumentsAggregate,
            ], options).toArray() as RelicDocument[];
        const totalCount = await relicDocuments.countDocuments({ ...finalFilter }, options);
        return { data, total: totalCount };

    });

    fastify.decorate("getDocumentById", async function (id: string): Promise<RelicDocument> {
        const profile: IUserIdentity = requestContext.get('whoami' as never) as IUserIdentity;
        let filters: any = {};
        if (profile.role.name !== 'admin') {
            filters.organizationId = profile.portalIdentity.organizationId;
        }
        if (profile.resourceType === 'Patient') {
            filters["access.id"] = profile.id;
        }
        filters["$or"] = [{ "id": id }, { "access.id": id, "access.resourceType": "CarePlan", deleted: false, isUploaded: true }];
        const document: RelicDocument = await relicDocuments.findOne(filters, { projection: { "_id": 0 } });
        if (!document) {
            throw fastify.httpErrors.notFound(`document not found, id:${id}`);
        }
        if (document.isUploaded) {
            document.hasTranslatedFiles = (await relicDocuments.countDocuments({
                "sourceDocumentId": id,
                "isUploaded": false,
                "deleted": false
            })) > 0 ? true : false;
        }
        return document;
    });

    fastify.decorate("saveDocument", async function (resource: RelicDocument): Promise<RelicDocument> {
        const profile: IUserIdentity = requestContext.get('whoami' as never) as IUserIdentity;
        resource.id = resource.id ?? crypto.randomUUID();
        resource.url = cleanURL(resource.url);
        resource.createdBy = resource.createdBy ?? { id: profile.id, resourceType: profile.resourceType };
        resource.createDate = new Date();
        resource.updatedBy = resource.createdBy;
        resource.updateDate = resource.createDate;
        resource.sourceDocumentId = resource?.sourceDocumentId ?? resource.id;
        resource.deleted = false;
        return await relicDocuments.findOneAndUpdate(
            { "id": resource.id },
            { "$set": resource },
            { "returnDocument": "after", "upsert": true, "projection": { "_id": 0 } }
        );
    });

    fastify.decorate("deleteDocument", async function (id: string): Promise<RelicDocument> {
        const profile: IUserIdentity = requestContext.get('whoami' as never) as IUserIdentity;

        let matchFilter: any = [{ "$match": { "deleted": false, "$or": [{ "id": id }, { "sourceDocumentId": id }] } }]; //obtain original files first

        if (profile.role.name !== 'admin') {
            matchFilter.organizationId = profile.portalIdentity.organizationId;
        }

        const relatedDocuments: RelicDocument[] = await relicDocuments
            .aggregate([...matchFilter])
            .toArray() as RelicDocument[];

        const thisDocument: RelicDocument = relatedDocuments.find(d => d.id == id);

        if (!thisDocument) {
            throw fastify.httpErrors.notFound(`Document not found, id:${id}`);
        }

        const updatedBy: DocumentUsers = { resourceType: profile.resourceType, id: profile.id };
        const update: any = { "deleted": true, "updatedBy": updatedBy, "updateDate": new Date() };
        if (relatedDocuments.length > 1) {
            //If this is a source document deletion, delete all related documents as well.
            for (let document of relatedDocuments) {
                await deleteAzureBlobStorageDocument(document);
            }
            const documentIds: string[] = relatedDocuments.map(d => d.id);
            await relicDocuments.deleteMany({ "id": { "$in": documentIds } });
        } else {
            await deleteAzureBlobStorageDocument(thisDocument);
            await relicDocuments.deleteOne({ "id": thisDocument.id });
        }
        return { ...thisDocument, ...update } as RelicDocument;
    });

    fastify.decorate("updateDocument", async function (id: string, doc: RelicDocument): Promise<RelicDocument> {
        const profile: IUserIdentity = requestContext.get('whoami' as never) as IUserIdentity;

        let matchFilter: any = [{ "$match": { "deleted": false, "$or": [{ "id": id }, { "sourceDocumentId": id }] } }]; //obtain original files first

        if (profile.role.name !== 'admin') {
            matchFilter.organizationId = profile.portalIdentity.organizationId;
        }

        const documents: RelicDocument[] = await relicDocuments
            .aggregate([...matchFilter])
            .toArray() as RelicDocument[];

        const document: RelicDocument = documents.find(d => d.id == id);
        if (!document) {
            throw fastify.httpErrors.notFound(`document not found, id:${id}`);
        }
        const updatedBy: DocumentUsers = { resourceType: profile.resourceType, id: profile.id };
        const extension = path.extname(document.filename);
        const newFilename = `${path.parse(doc.filename).name}${extension}`;
        const update: any = { "filename": newFilename, "header": doc.header, "footer": doc.footer, "updatedBy": updatedBy, "updateDate": new Date() };
        const result = await relicDocuments.findOneAndUpdate(
          { id: id },
          { $set: { ...update, status: doc.status, url: cleanURL(doc.url) } },
          { projection: { _id: 0 }, returnDocument: 'after', includeResultMetadata: true },
        );

        if (document.isUploaded) {
            //also rename translated files
            for (let d of documents) {
                if (!d.isUploaded) {
                    const basename = `${path.parse(newFilename).name}-${d.language.toUpperCase()}`;
                    const filename = `${basename}${extension}`;
                    await relicDocuments.updateOne({ id: d.id }, { $set: { ...update, filename: filename } });
                }
            }
        }
        return result.value ?? document;
    });

    fastify.decorate("createTranslatedDocument", async function (documentId: string, options: any): Promise<RelicDocument> {
        const targetLanguage = options.targetLanguage;
        let translationType = options.translationType;
        try {
            const sourceDocument: RelicDocument = await fastify.getDocumentById(documentId);
            if (!sourceDocument) {
                throw httpErrors.notFound(`Source document not found, id: ${documentId}`);
            }
            const carePlanAccess = sourceDocument?.access ? sourceDocument.access.find((item: DocumentUsers) => item.resourceType === 'CarePlan') : null;
            const isCarePlanDocument: boolean = carePlanAccess && carePlanAccess.resourceType == "CarePlan";
            if (isCarePlanDocument) translationType = 'mono';
            let translatedDocument: RelicDocument = null;
            //check if a translated document is available
            const filter = {
                deleted: false,
                sourceDocumentId: sourceDocument.id,
                translationType: { $in: [translationType] },
                language: { $in: [targetLanguage, targetLanguage.toUpperCase()] },
            };
            const found: RelicDocument = await relicDocuments.findOne(filter);
            if (found && ['pending', 'inprogress'].includes(found.status)) {
                return found;
            } else if (found && found.status === 'failed') {
                //reprocess the document
                translatedDocument = found;
                translatedDocument.status = 'pending';
                translatedDocument.url = '';
            } else {
                const extension = path.extname(sourceDocument.documentId);
                const basename = `${path.parse(sourceDocument.filename).name}-${translationType == 'bilingual' ? 'mod-fmt' : 'orig-fmt'}-${targetLanguage.toUpperCase()}`; //<original filename>-<language>
                const filename = `${basename}${extension}`; //.pdf
                const uploadFilename = `${basename}_${crypto.randomUUID()}${extension}`; //to avoid overwriting same name in azure
                translatedDocument = {
                    organizationId: sourceDocument.organizationId,
                    language: targetLanguage.toLowerCase(),
                    sourceDocumentId: sourceDocument.id,
                    type: sourceDocument.type,
                    url: '',
                    filename: filename,
                    documentId: uploadFilename,
                    isUploaded: false,
                    deleted: false,
                    status: 'pending',
                    header: sourceDocument.header,
                    footer: sourceDocument.footer,
                    access: sourceDocument.access,
                    translationType: translationType
                };
            }
            if (isCarePlanDocument && !translatedDocument?.data?.carePlan) {
                const translatedCarePlanJson: Map<string, any> = await getCarePlanTranslatedJson(sourceDocument, carePlanAccess, targetLanguage);
                translatedDocument.data = { carePlan: translatedCarePlanJson };
                translatedDocument.status = 'done';
            }
            const result = await fastify.saveDocument(translatedDocument);
            if (result) {
                if (isCarePlanDocument) {
                    return result;
                } else {
                    await translatorWorkQueue.add(translateJobName, { documentId: translatedDocument.id }, { jobId: translatedDocument.id, removeOnComplete: true });
                }
            }
            return result;
        } catch (e) {
            fastify.log.error(e, `document translation failed, sourceDocumentId: ${documentId}, language: ${targetLanguage}`);
            throw e;
        }
    });

    fastify.decorate("processCarePlans", async function (
        patientId: string,
        query: any,
        searchOptions: any
    ): Promise<PccPatientCarePlan[]> {
        const relicOrganizationId = requestContext.get('organizationId');
        const response = await pccService.getPatientCarePlans(patientId, query);
        const pccPatientCarePlans = response.data as PccPatientCarePlan[];
        for (const carePlan of pccPatientCarePlans) {
            const metadata: PccMetaData = {
                createdBy: carePlan.createdBy || '',
                createdDate: carePlan.createdDate || '',
                revisionBy: carePlan.revisionBy || '',
                revisionDate: carePlan.revisionDate || '',
                nextReviewDate: carePlan.nextReviewDate || ''
            };

            let document: RelicDocument = {
                organizationId: relicOrganizationId,
                language: "EN-US",
                type: "application/pdf",
                url: "",
                filename: `CarePlan-${carePlan.carePlanId}.pdf`,
                documentId: `CarePlan-${carePlan.carePlanId}_${randomUUID()}.pdf`,
                isUploaded: true,
                access: [{ id: patientId, resourceType: 'Patient' }, { id: carePlan.carePlanId?.toString() as string, resourceType: 'CarePlan' }],
                header: 0,
                footer: 0,
                status: 'done',
                translationType: 'mono',
                metadata: metadata,
                data: { carePlan: carePlan }
            };
            searchOptions['patientId'] = patientId;
            searchOptions['carePlanId'] = carePlan.carePlanId?.toString();
            const existingDocuments = await fastify.searchDocuments(searchOptions);
            if (existingDocuments && existingDocuments.total > 0) {
                if (existingDocuments.total > 1) {
                    throw fastify.httpErrors.badRequest('Multiple documents found for the same care plan.');
                }
                document = existingDocuments.data[0];
                // Check if the document exists in storage. If not, clear the URL.
                document = await syncDocUrlWithBlob(document);
                if (document.status === 'pending') {
                    // Refresh the care plan only if PDF was deleted.
                    document.data = {
                        carePlan: carePlan
                    };
                }
            }

            await fastify.saveDocument(document);
        }
        return pccPatientCarePlans;
    });

    fastify.decorate("getCarePlanDetails", async function (carePlanDocument: RelicDocument): Promise<RelicDocument> {
        const pccPatientCarePlan: PccPatientCarePlan = carePlanDocument.data?.carePlan;
        if (!pccPatientCarePlan) {
            throw fastify.httpErrors.badRequest('PccPatientCarePlan not found.');
        }
        const focusIds = pccPatientCarePlan.focuses?.map((focus: PccCarePlanFocus) => focus.focusId);
        if (focusIds && focusIds.length > 0) {
            const { data } = await pccService.getCarePlanFocuses(focusIds as number[], {});
            pccPatientCarePlan.focuses = data as PccCarePlanFocusResponse[];
        }
        carePlanDocument.data = {
            carePlan: pccPatientCarePlan
        };
        await fastify.saveDocument(carePlanDocument);
        return carePlanDocument;
    });

    async function getCarePlanTranslatedJson(sourceDocument: RelicDocument, carePlanAccess: any, targetLanguage: string): Promise<Map<string, any>> {
        try {
            const carePlanJsonDocument = sourceDocument.data.carePlan;
            const translatedCarePlanJson: Map<string, any> = await generateTranslatedCarePlanJson(carePlanJsonDocument, 'en', targetLanguage.toLowerCase());
            return translatedCarePlanJson;
        } catch (e) {
            throw new Error(`${e} Error while getting the careplan translated JSON.`);
        }
    }

    async function translateDocument(id: string): Promise<void> {
        const filesForCleanup: string[] = [];
        try {
            fastify.log.info(`[document-translate-job-${id}] Document translation started, id: ${id}`);
            const result = await relicDocuments.updateOne({ "id": id, "status": "pending", "deleted": false }, { "$set": { "status": "inprogress", "lastTranslateAttemptDate": new Date() } }); //try to obtain a lock
            if (result.matchedCount == 0 || result.modifiedCount == 0) {
                //the document is either deleted or status is not pending, ignore this job
                fastify.log.info(`[document-translate-job-${id}] Document is not in "pending" status, id: ${id}`);
                return;
            }
            const translatedDocument: RelicDocument = await relicDocuments.findOne({ "id": id });
            const sourceDocument: RelicDocument = await relicDocuments.findOne({ "id": translatedDocument.sourceDocumentId, "deleted": false });
            if (!sourceDocument) {
                //the source document is deleted, we can't proceed, throw error
                throw new Error(`Source document not found, document id="${id}", sourceDocumentId=${translatedDocument.sourceDocumentId}`);
            }
            const workingDirectory = path.join(ROOT_DOWNLOADS_FOLDER, sourceDocument.organizationId, `${crypto.randomUUID()}_${new Date().getTime()}`);
            const ok = mkdirSync(workingDirectory, { recursive: true });
            if (!ok) {
                throw new Error(`failed to create directory ${workingDirectory}`);
            }
            filesForCleanup.push(workingDirectory);

            const azureBlobServiceClient: BlobServiceClient = getAzureStorageBlobServiceClient();
            const containerClient: ContainerClient = azureBlobServiceClient.getContainerClient(sourceDocument.organizationId);
            const blobClient = containerClient.getBlockBlobClient(sourceDocument.documentId);

            const extension = path.extname(sourceDocument.documentId);
            const originalFilename = `${sourceDocument.id}_${new Date().getTime()}${extension}`; //<id>_<user_id>_<datetimestamp>.<extension>
            const originalFilePath = path.join(workingDirectory, originalFilename);
            await blobClient.downloadToFile(originalFilePath);

            const docOptions: TranslateDocumentOptions = {
                workingDirectory: workingDirectory,
                sourceDocumentId: sourceDocument.documentId,
                sourceFilepath: originalFilePath,
                sourceLang: sourceDocument.language.toLowerCase(),
                targetLang: translatedDocument.language,
                numFooterLines: sourceDocument.footer ?? 0,
                numHeaderLines: sourceDocument.header ?? 0,
                sourceJsonTextblocksFileDir: "",
                azureServiceClient: azureBlobServiceClient,
                translationType: translatedDocument.translationType
            }
            // check if textblocks are available in azure
            docOptions.sourceJsonTextblocksFileDir = await getTextBlocksFromStorage(containerClient, docOptions);
            const uploadSourceTextblocks = docOptions.sourceJsonTextblocksFileDir?.length == 0; //if true, files not in azure

            const pdfResult: TranslateDocumentResult = await generateTranslatedDocument(docOptions);
            if (!existsSync(pdfResult.translatedFilepath)) {
                throw new Error(`file not found: ${pdfResult.translatedFilepath}`);
            }

            if (uploadSourceTextblocks || pdfResult.sourceTextBlocksUpdated) {
                await uploadTextBlocksToStorage(containerClient, docOptions);
            }

            filesForCleanup.push(pdfResult.sourceJsonTextblocksFileDir);
            filesForCleanup.push(pdfResult.translatedFilepath);
            filesForCleanup.push(pdfResult.translatedJsonTextblocksFileDir);

            const uploadClient = containerClient.getBlockBlobClient(translatedDocument.documentId);
            await uploadClient.uploadFile(pdfResult.translatedFilepath, {
                blobHTTPHeaders: { blobContentType: sourceDocument.type },
            });
            const update: any = { "url": uploadClient.url, "status": "done", "lastTranslateAttemptDate": new Date() };
            await relicDocuments.findOneAndUpdate({ "id": translatedDocument.id }, { "$set": update }, { "projection": { "_id": 0 }, "returnDocument": "after" });
            fastify.log.info(`[document-translate-job-${id}] Document translation successful, id: ${id}`);
        } catch (err) {
            fastify.log.error(err, `[document-translate-job-${id}] Document translation failed, id: ${id}`);
            const update: any = { "status": "failed", "errorDetails": err.message || JSON.stringify(err), "lastTranslateAttemptDate": new Date() };
            await relicDocuments.findOneAndUpdate({ "id": id }, { "$set": update }, { "projection": { "_id": 0 }, "returnDocument": "after" });
        } finally {
            deleteLocalFiles(filesForCleanup);
        }
    }

    function deleteLocalFiles(filepaths: string[]) {
        filepaths.filter(v => v.length > 0).forEach(f => {
            try {
                rmSync(f, { recursive: true, force: true });
            } catch (e) {
                fastify.log.error(e, 'file/directory deletion failed');
            }
        });
    }

    function cleanURL(url: string): string {
        return url?.split('?')?.[0];
    }

    async function syncDocUrlWithBlob(document: RelicDocument): Promise<RelicDocument> {
        if (!document.url)
            return document; //Nothing to do since we don't know the url to check
        const azureBlobServiceClient: BlobServiceClient = getAzureStorageBlobServiceClient();
        const containerClient: ContainerClient = azureBlobServiceClient.getContainerClient(document.organizationId);
        const blobDocumentExists = await containerClient.getBlockBlobClient(document.documentId).exists();
        if (!blobDocumentExists) {
            document.url = '';
            document.status = 'pending';
            return document;
        } else {
            return document;
        }
    };

    function getAzureStorageBlobServiceClient(): BlobServiceClient {
        const accountName: string = config.get('AZURE.DOCUMENT_STORAGE_ACCOUNT_NAME') as string;
        const accountKey: string = config.get('AZURE.DOCUMENT_STORAGE_ACCOUNT_KEY') as string;
        return BlobServiceClient.fromConnectionString(`DefaultEndpointsProtocol=https;AccountName=${accountName};AccountKey=${accountKey};EndpointSuffix=core.windows.net`)
    }

    async function deleteAzureBlobStorageDocument(document: RelicDocument): Promise<void> {
        if (!document.url) {
            fastify.log.info(`[deleteAzureBlobStorageDocument] document url not found, id: ${document.id}`);
            return;
        }
        const azureBlobServiceClient: BlobServiceClient = getAzureStorageBlobServiceClient();
        const containerClient: ContainerClient = azureBlobServiceClient.getContainerClient(document.organizationId);
        const blobDocument = containerClient.getBlockBlobClient(document.documentId);
        await blobDocument.deleteIfExists({ "deleteSnapshots": "include" });
    }

    function getRelicDocumentsAggregate(sortArrayBy: any, patientId?: string) {
        const accessFilter = [];
        if (patientId) {
            accessFilter.push({
                $match: {
                    "access.id": patientId
                }
            },);
        }
        const relicDocumentsAggregate = [
            {
                "$lookup": {
                    "from": "documents",
                    "localField": "id",
                    "foreignField": "sourceDocumentId",
                    "as": "translations",
                }
            },
            {
                "$project": {
                    "translations": {
                        "$filter": {
                            "input": "$translations",
                            "cond": {
                                "$eq": ["$$this.deleted", false],
                            },
                        },
                    },
                }
            },
            {
                "$project": {
                    "translations": {
                        "$sortArray": {
                            "input": "$translations",
                            "sortBy": sortArrayBy,
                        },
                    },
                }
            },
            {
                "$addFields":
                {
                    "translations.numLinkedFiles": {
                        "$subtract": [
                            {
                                "$size": "$translations",
                            },
                            1,
                        ],
                    },
                }
            },
            {
                "$unwind": {
                    "path": "$translations",
                    "preserveNullAndEmptyArrays": false,
                }
            },
            { "$replaceRoot": { "newRoot": "$translations" } },
            ...accessFilter,
            {
                "$addFields":
                {
                    "hasTranslatedFiles": {
                        "$cond": {
                            "if": {
                                "$and": [
                                    {
                                        "$eq": ["$isUploaded", true],
                                    },
                                    {
                                        "$gt": ["$numLinkedFiles", 0],
                                    },
                                ],
                            },
                            "then": true,
                            "else": false,
                        },
                    },
                },
            },
            { "$project": { "numLinkedFiles": 0, "_id": 0 } },
        ];

        return relicDocumentsAggregate;
    }

    async function getTextBlocksFromStorage(containerClient: ContainerClient, docOptions: TranslateDocumentOptions): Promise<string> {
        let result = "";
        try {
            const filename = `${path.parse(docOptions.sourceDocumentId).name}-${docOptions.sourceLang}.${ARCHIVE_TYPE}`;
            const sourceTextblocksClient = containerClient.getBlockBlobClient(`${AZ_STORAGE_TEXTBLOCKS_PREFIX}/${filename}`);
            if (await sourceTextblocksClient.exists()) {
                const sourceJsonTextblocksFilepath = path.join(docOptions.workingDirectory, "source_textblocks", path.parse(docOptions.sourceDocumentId).name, filename);
                mkdirSync(path.parse(sourceJsonTextblocksFilepath).dir, { recursive: true });
                await sourceTextblocksClient.downloadToFile(sourceJsonTextblocksFilepath);
                result = await unzipTextblocksArchive(sourceJsonTextblocksFilepath);
            }
        } catch (e) {
            fastify.log.error(e, `textblocks retrieval failed, sourceDocumentId: ${docOptions.sourceDocumentId}`);
        } finally {
            return result;
        }
    }

    async function uploadTextBlocksToStorage(containerClient: ContainerClient, docOptions: TranslateDocumentOptions) {
        const filesForCleanup = [];
        try {

            const compressedFile = await compressTextblocksFolder(docOptions);
            filesForCleanup.push(compressedFile);
            await containerClient.createIfNotExists();
            const filename = `${path.parse(docOptions.sourceDocumentId).name}-${docOptions.sourceLang}.${ARCHIVE_TYPE}`;
            const uploadClient = containerClient.getBlockBlobClient(`${AZ_STORAGE_TEXTBLOCKS_PREFIX}/${filename}`);
            await uploadClient.uploadFile(compressedFile);
        } catch (e) {
            fastify.log.error(e, `textblocks upload failed, sourceDocumentId: ${docOptions.sourceDocumentId}`);
        } finally {
            deleteLocalFiles(filesForCleanup);
        }
    }

    async function compressTextblocksFolder(docOptions: TranslateDocumentOptions): Promise<string> {
        const outputpath = path.join(docOptions.workingDirectory, `${path.parse(docOptions.sourceFilepath).name}-${docOptions.sourceLang}.${ARCHIVE_TYPE}`);
        const files = readdirSync(docOptions.sourceJsonTextblocksFileDir)?.map(f => path.parse(f).base);
        return await tar.create({ file: outputpath, C: docOptions.sourceJsonTextblocksFileDir }, files)
            .then(() => {
                return outputpath;
            })
            .catch(e => {
                fastify.log.error(e, `textblocks file compression failed, sourceDocumentId: ${docOptions.sourceDocumentId}`);
                return "";
            });
    }

    async function unzipTextblocksArchive(zipfilepath: string): Promise<string> {
        return await tar.extract({
            file: zipfilepath,
            C: path.parse(zipfilepath).dir
        }).then(() => {
            return path.parse(zipfilepath).dir;
        }).catch(e => {
            fastify.log.error(e, `textblocks file decompression failed, file: ${zipfilepath}`);
            return "";
        }).finally(() => {
            deleteLocalFiles([zipfilepath]);
        });
    }

    fastify.register(routeProviderPlugin, options);

}


export default documentsPlugin;