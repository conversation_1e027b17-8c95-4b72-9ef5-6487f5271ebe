// import fp from 'fastify-plugin';
import { v4 as uuidv4 } from 'uuid';
import { RelicAgent, RelicPractitioner } from 'relic-ui';
import { User } from '@microsoft/microsoft-graph-types';
import { FastifyInstance, FastifyPluginAsync, FastifyPluginOptions } from 'fastify';
import { relicRequire } from "../../utils/common-js";
import agentPlugin from './agentPlugin';
import { CommunicationIdentity } from 'relic-ui';
 
const { AdminRole, MemberRole } = relicRequire('relic-ui/schema');

declare module 'fastify' {
  interface FastifyInstance {
    // Used by agentPlugin to convert RelicAgent to RelicPractitioner
    toRelicPractitioner(practitioner: RelicAgent, relicPractitioner?: RelicPractitioner): RelicPractitioner;
    // Used only by routes. We cannot use searchRelicAgents here due to encapsulation. To be fixed.
    getPractitioners(filters: any): Promise<{ practitioners: RelicPractitioner[]; totalCount: number }>;
    // Get operation - used by routes and auth hooks.
    getPractitioner(practitionerId: string): Promise<RelicPractitioner>;
    // Used by routes and createAgent
    createPractitioner(relicPractitioner: RelicPractitioner, msGraphPractitioner?: User): Promise<RelicPractitioner>;
    // Used only by routes
    updatePractitioner(
      relicPractitionerUpdates: RelicPractitioner,
      msGraphPractitioner: User,
    ): Promise<RelicPractitioner>;
    // Used by updateAgent
    saveRelicPractitioner(resource: RelicPractitioner): Promise<RelicPractitioner>;
    getPractitionerCommunicationIdentity(id: string): Promise<CommunicationIdentity>;
  }
}

// Cannot use `FastifyInstance` in the plugin signature because higher level plugins are not typed as `FastifyInstance`.
const practitionerPlugin: FastifyPluginAsync = async (fastify: FastifyInstance, options: FastifyPluginOptions) => {
  fastify.log.info('Practitioner Plugin loading...');

  const relicPractitionerCollection = fastify.mongo.reliccare.db.collection<RelicPractitioner>('practitioners');
  const relicAgentCollection = fastify.mongo.reliccare.db.collection<RelicPractitioner>('agents');

  fastify.decorate('toRelicPractitioner', function (practitioner: RelicAgent, relicPractitioner?: RelicPractitioner): RelicPractitioner {
    return {
      id: practitioner.id,
      resourceType: 'Practitioner',
      enabled: practitioner.active,
      organizationId: practitioner.organizationId,
      name: practitioner.name,
      mobilePhone: practitioner.mobilePhone,
      email: practitioner.email,
      communicationIdentities: practitioner.communicationIdentities,
      role: { name: 'admin'},
      provider: 'medplum'
    }
  });

  fastify.decorate('getPractitioners', async function (filters: any): Promise<{
    practitioners: RelicPractitioner[];
    totalCount: number;
  }> {
    let { filter, _sort, _order, _start, _end, _search } = filters;
    const practitioners = relicPractitionerCollection;
    const agents = relicAgentCollection;
    //TODO: due to encapsulation issues, DB query is updated to avoid using searchRelicAgents.
    //TODO: To be fixed whether we want to stick with searchRelicAgents + encapsulation
    // const agents: RelicAgent[] = await fastify.searchRelicAgents();

    // Get all practitioner IDs that exist in agents collection
    // const agentIds = agents.map(agent => agent.id);
    // if (filter.id) {
    //     filter.id = { ...(filter.id), $nin: agentIds }
    // } else {
    //     filter.id = { $nin: agentIds }
    // }

    // Add search filter if _search is provided
    if (_search) {
      filter['$or'] = [
        { name: { $regex: _search, $options: 'i' } },
        { email: { $regex: _search, $options: 'i' } },
        { mobilePhone: { $regex: _search, $options: 'i' } },
      ];
    }

    const totalCount = await practitioners.countDocuments(filter, {});

    const sortOptions = _sort ? { [_sort]: _order === 'desc' ? -1 : 1 } : {};

    // Add pagination if _start and _end are provided
    const skip = _start !== undefined ? parseInt(_start) : 0;
    const limit = _end !== undefined ? parseInt(_end) - skip : 10; // Default limit to 10 if _end is not provided

    const practitionersList = await practitioners
      .aggregate<RelicPractitioner>(
        [
          {
            $lookup: {
              from: agents.collectionName,
              localField: 'id',
              foreignField: 'id',
              as: 'agentData',
            },
          },
          {
            $match: {
              ...filter,
              agentData: { $eq: [] },
            },
          },
          {
            $project: {
              agentData: 0,
              _id: 0,
            },
          },
          {
            $sort: sortOptions,
          },
          {
            $skip: skip,
          },
          {
            $limit: limit,
          },
        ],
        {
          collation: {
            locale: 'en',
            strength: 2,
          },
        },
      )
      .toArray();

    return { practitioners: practitionersList, totalCount };
  });

  fastify.decorate('getPractitioner', async function (practitionerId: string): Promise<RelicPractitioner> {
    const practitioner = await relicPractitionerCollection.findOne({
      $or: [
        { id: practitionerId },
        { 'communicationIdentities.userId': practitionerId },
        { mobilePhone: practitionerId },
        { email: practitionerId },
      ],
    });
    if (!practitioner) {
      throw fastify.httpErrors.notFound(`Practitioner not found - ${practitionerId}`);
    }
    return practitioner;
  });

  fastify.decorate(
    'createPractitioner',
    async function (relicPractitioner: RelicPractitioner, msGraphPractitioner?: User): Promise<RelicPractitioner> {
      // Generate missing fields for relicPractitioner
      relicPractitioner.id = relicPractitioner.id || uuidv4();
      relicPractitioner.resourceType = relicPractitioner.resourceType || 'Practitioner';
      relicPractitioner.provider = relicPractitioner.provider || 'msgraph';
      relicPractitioner.role = (['reliccare.com' , 'minimals.cc'].includes(relicPractitioner.email.split('@').at(-1)))? AdminRole: MemberRole

      // Call createMsGraphPractitioner only if provider is msgraph
      if (relicPractitioner.provider === 'msgraph') {
        // This step will also perform duplicate email / mobile phone conflict if any.
        // And if MsGraphPractitioner creation fails, the process will stop.
        await (fastify as any).createMsGraphPractitioner(relicPractitioner, msGraphPractitioner);
      }

      // Generate Communication Identities for the practitioner based on the organization
      const identity = await fastify.acs.createIdentity(relicPractitioner);
      relicPractitioner.communicationIdentities = [identity];

      // Save the Relic Practitioner
      await fastify.saveRelicPractitioner(relicPractitioner);

      return relicPractitioner;
    },
  );

  fastify.decorate(
    'updatePractitioner',
    async function (
      relicPractitionerUpdates: RelicPractitioner,
      msGraphPractitioner: User,
    ): Promise<RelicPractitioner> {
      if (!relicPractitionerUpdates.id) {
        throw fastify.httpErrors.badRequest('Practitioner id is required.');
      }

      // Find relicPractitioner
      let relicPractitioner = await relicPractitionerCollection.findOne({ id: relicPractitionerUpdates.id });
      if (!relicPractitioner) {
        throw fastify.httpErrors.notFound('Practitioner not found.');
      }
      // Note for Maria - Below implementation is not correct. This method should only work with Relic AI Database.
      // The update to MS Graph Practitioner should be done by B2C Provider Service before calling this method.
      if (relicPractitioner.provider === 'msgraph') {
        // Update MSGraphPractitioner
        await (fastify as any).updateMsGraphPractitioner(
          { ...relicPractitioner, ...relicPractitionerUpdates },
          msGraphPractitioner,
        );
      }
      relicPractitioner = { ...relicPractitioner, ...relicPractitionerUpdates };
      relicPractitioner.communicationIdentities[0].displayName = relicPractitioner.name; //Update the name
      const updatedPractitioner: RelicPractitioner = await fastify.saveRelicPractitioner(relicPractitioner);
      return updatedPractitioner;
    },
  );

  fastify.decorate('saveRelicPractitioner', async function (resource: RelicPractitioner): Promise<RelicPractitioner> {
    return await relicPractitionerCollection.findOneAndUpdate(
      { id: resource.id },
      { $set: resource },
      { upsert: true, returnDocument: 'after', projection: { _id: 0 } },
    );
  });

  fastify.decorate('getPractitionerCommunicationIdentity', async function (id: string): Promise<CommunicationIdentity> {
    const resource = await relicPractitionerCollection.findOne({
      $or: [{ id: id }, { 'communicationIdentities.userId': id }, { mobilePhone: id }, { email: id }],
    });
    const identity = await fastify.acs.refreshIdentity(resource, resource.communicationIdentities[0]);
    identity.displayName = resource.name ? resource.name : 'Practitioner';
    return identity;
  });

  fastify.register(agentPlugin, options);
};

// export default fp(practitionerPlugin);
export default practitionerPlugin;
