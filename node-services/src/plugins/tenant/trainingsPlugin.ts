import { FastifyInstance, FastifyPluginAsync, FastifyPluginOptions } from 'fastify';
import config from 'config';
import { Training, TrainingModule, TrainingContent, AssignedTrainingModule } from 'relic-ui';
import { RelicOrganization } from 'relic-ui';
import { requestContext } from '@fastify/request-context';
import path from 'path';
import { mkdirSync, readFileSync, rmSync } from 'fs';
import { IUserIdentity } from 'relic-ui';
import {
  AzureKeyCredential,
  SearchIndex,
  SearchIndexClient,
  SearchIndexer,
  SearchIndexerClient,
  SearchIndexerDataSourceConnection,
  SearchIndexerIndexProjection,
  SearchIndexerIndexProjectionSelector,
  SearchIndexerSkillset,
} from '@azure/search-documents';
import { v4 as uuidv4 } from 'uuid';
import { BlobServiceClient, ContainerClient } from '@azure/storage-blob';
import { ScraperOutput, CrawlRequest, RelicCrawlerOptions, URLLike } from 'relic-ui';
import docParser from 'officeparser';
import { homedir } from 'os';
import { ActorRun } from 'apify-client';
import { RelicSearchQuery } from '@/types/request';
import { MongoQueryBuilder } from '../../utils/mongo';
import { RelicMessageQueue } from '../../services/relic.queue';
import communicationPlugin from './communicationPlugin';

const trainingsPlugin: FastifyPluginAsync = async (fastify: FastifyInstance, options: FastifyPluginOptions) => {
  //#region Constants, Initialization
  const ROOT_DOWNLOADS_FOLDER = path.resolve(homedir(), 'downloads');
  const TEMPLATE_DIR: string = path.resolve(`${__dirname}/../../utils/trainings`);
  const INDEX_JSON_TEMPLATE: SearchIndex = JSON.parse(
    readFileSync(path.join(TEMPLATE_DIR, '/index.template.json'), { encoding: 'utf-8' }),
  ) as SearchIndex;
  const INDEXER_JSON_TEMPLATE: SearchIndexer = JSON.parse(
    readFileSync(path.join(TEMPLATE_DIR, '/indexer.template.json'), { encoding: 'utf-8' }),
  ) as SearchIndexer;
  const SKILLSET_JSON_TEMPLATE: SearchIndexerSkillset = JSON.parse(
    readFileSync(path.join(TEMPLATE_DIR, '/skillset.template.json'), { encoding: 'utf-8' }),
  ) as SearchIndexerSkillset;
  const SKILLSET_PROJECTION_JSON_TEMPLATE: SearchIndexerIndexProjection = JSON.parse(
    readFileSync(path.join(TEMPLATE_DIR, '/skillset.projection.template.json'), { encoding: 'utf-8' }),
  ) as SearchIndexerIndexProjection;
  const DATASOURCE_JSON_TEMPLATE: SearchIndexerDataSourceConnection = JSON.parse(
    readFileSync(path.join(TEMPLATE_DIR, '/datasource.template.json'), { encoding: 'utf-8' }),
  ) as SearchIndexerDataSourceConnection;
  const DB_COL_TRAININGS: string = 'trainings';
  const DB_COL_TRAINING_MODULES: string = 'trainingModules';
  const DB_COL_TRAINING_CONTENTS: string = 'trainingContents';
  const JOB_HANDLE_TRAINING_CONTENT: string = 'handle_training_content';
  const JOB_HANDLE_SCRAPED_CONTENT: string = 'handle_scraped_content';
  const JOB_HANDLE_ESTIMATOR_OUTPUT: string = 'handle_estimator_output';

  const azureConfig = {
    search: {
      endpoint: config.get('AZURE.AI_SEARCH_ENDPOINT') as string,
      apiKey: config.get('AZURE.AI_SEARCH_API_KEY') as string,
      version: config.get('AZURE.AI_SEARCH_VERSION') as string,
    },
    storage: {
      accountName: config.get('AZURE.TRAINING_STORAGE_ACCOUNT_NAME') as string,
      accountKey: config.get('AZURE.TRAINING_STORAGE_ACCOUNT_KEY') as string,
      subscriptionId: config.get('AZURE.SUBSCRIPTION_ID') as string,
    },
    openai: {
      endpoint: config.get('OPENAI.ENDPOINT') as string,
      apiKey: config.get('OPENAI.API_KEY') as string,
      version: config.get('OPENAI.VERSION') as string,
    },
  };

  //#region
  const trainingsColl = fastify.mongo.reliccare!.db!.collection<Training>(DB_COL_TRAININGS);
  const trainingModulesColl = fastify.mongo.reliccare!.db!.collection<TrainingModule>(DB_COL_TRAINING_MODULES);
  const trainingContentsColl = fastify.mongo.reliccare!.db!.collection<TrainingContent>(DB_COL_TRAINING_CONTENTS);
  //#endregion

  //child logger to add more context specific to trainingsPlugin
  const logger = fastify.log.child({ plugin: 'trainingsPlugin' }, { msgPrefix: '[trainings] ' });

  //default training jobs queue and worker for Facility Portal requests
  const contentWriterWorkQueue = RelicMessageQueue.CreateQueueForWorker('TRAINING_CONTENT_WRITER');
  contentWriterWorkQueue.attachWorkerToQueue(async (job) => {
    logger.info({ jobId: job.id, jobName: job.name }, 'Training job received.');
    const jobId: string = job.id!;
    if (job.name == JOB_HANDLE_TRAINING_CONTENT) {
      const trainingContentType = job.data?.type || '';
      if (!trainingContentType) {
        throw fastify.httpErrors.badRequest('Training content type is required.');
      }
      if (trainingContentType === 'url') {
        await handleUrlContent(jobId, job.data);
        return;
      }
      if (trainingContentType === 'folder') {
        await handleFolderContent(jobId, job.data);
        return;
      }
      await handleFileContent(jobId, job.data);
      return;
    }

    if (job.name == JOB_HANDLE_SCRAPED_CONTENT) {
      await handleScrapedContent(jobId, job.data as ScraperOutput);
      return;
    }

    logger.info({ jobId: job.id, jobName: job.name }, 'Unsupported job name received.');
  });

  const estimatorWorkQueue = RelicMessageQueue.CreateQueueForWorker('TRAINING_CONTENT_ESTIMATOR');
  estimatorWorkQueue.attachWorkerToQueue(async (job) => {
    logger.info({ jobId: job.id, jobName: job.name }, 'Estimator job received.');
    if (job.name == JOB_HANDLE_ESTIMATOR_OUTPUT) {
      await handleUrlEstimatorOutput(job.id as string, job.data as Partial<ScraperOutput>);
      return;
    }

    logger.info({ jobId: job.id, jobName: job.name }, 'Unsupported job name received.');
  });

  const azureCredential: AzureKeyCredential = new AzureKeyCredential(azureConfig.search.apiKey);
  const indexClient: SearchIndexClient = new SearchIndexClient(azureConfig.search.endpoint, azureCredential, {
    serviceVersion: azureConfig.search.version,
  });
  const indexerClient = new SearchIndexerClient(azureConfig.search.endpoint, azureCredential);
  const storageConnection: string = `DefaultEndpointsProtocol=https;AccountName=${azureConfig.storage.accountName};AccountKey=${azureConfig.storage.accountKey};EndpointSuffix=core.windows.net`;
  const dataSourceConnection: string = `ResourceId=/subscriptions/${azureConfig.storage.subscriptionId}/resourceGroups/ai-counsellor/providers/Microsoft.Storage/storageAccounts/${azureConfig.storage.accountName};`;
  const blobServiceClient = BlobServiceClient.fromConnectionString(storageConnection);

  //#endregion

  //#region Azure Search API integration
  async function createAzureAISearchIndex(def: SearchIndex): Promise<SearchIndex> {
    def.name = def.name?.toLowerCase();
    def.vectorSearch?.vectorizers?.map((vectorizer) => {
      if (vectorizer.kind == 'azureOpenAI') {
        vectorizer.parameters = {
          ...(vectorizer.parameters || {}),
          apiKey: azureConfig.openai.apiKey,
          resourceUrl: azureConfig.openai.endpoint,
        };
      }
    });

    return await indexClient.createIndex(def);
  }

  async function createAzureAISearchIndexer(def: SearchIndexer): Promise<SearchIndexer> {
    return await indexerClient.createIndexer(def);
  }

  async function createSkillset(def: SearchIndexerSkillset): Promise<SearchIndexerSkillset> {
    def.skills.forEach((skill) => {
      if (skill.odatatype === '#Microsoft.Skills.Text.AzureOpenAIEmbeddingSkill') {
        skill.resourceUrl = azureConfig.openai.endpoint;
      }
    });
    return await indexerClient.createSkillset(def);
  }

  async function createDataSourceConnection(
    def: SearchIndexerDataSourceConnection,
  ): Promise<SearchIndexerDataSourceConnection> {
    def.name = def.name.toLowerCase();
    return await indexerClient.createDataSourceConnection(def);
  }

  /**
   * When an indexer is removed,
   * documents under that indexer(datasource) should be removed from the index
   * In this process, documents are kept in the datasource BUT no longer indexed under the target index.
   * This process could take time, waiting is not advisable
   * @param targetIndexName Azure AI Search Index name, target index for document removal
   * @param searchIndexer Azure AI Search Indexer
   * @returns Process stats
   * @throws {Error} Module NotFoundError
   */
  async function deleteAzureAISearchIndexer(
    targetIndexName: string,
    moduleToRemove: AssignedTrainingModule,
  ): Promise<{ id: string; documentsCount: number; successCount: number; failureCount: number }> {
    const module: TrainingModule = await getTrainingModuleFromDB({ id: moduleToRemove.id });
    await indexerClient.deleteIndexer(moduleToRemove.indexerName);

    const searchClient = indexClient.getSearchClient(targetIndexName);
    const containerName: string = module.storage.containerName;

    //Obtain list of indexed-documents which were processed by the indexer
    //We use the filepath to determine these documents, since a module will use 1 datasource/storage
    const search = `https://${azureConfig.storage.accountName}.blob.core.windows.net/${containerName}*`;
    const documentsToDelete: Array<{ id: string }> = [];
    for await (let result of (
      await searchClient.search(search, { queryType: 'simple', searchFields: ['filepath'], select: ['id', 'filepath'] })
    ).results) {
      const { id } = result.document as { id: string };
      documentsToDelete.push({ id });
    }
    const documentsCount = documentsToDelete.length;
    if (documentsCount == 0) {
      logger.info({ index: targetIndexName, containerName, search }, 'Search returned empty list.');
      return { id: module.id, documentsCount, successCount: 0, failureCount: 0 };
    }
    logger.info({ index: targetIndexName, containerName, documentsCount }, 'Deleting documents from index.');
    const deleteResult = await searchClient.deleteDocuments(documentsToDelete);
    const failureCount = deleteResult.results.filter((r) => !r.succeeded).length;
    const successCount = documentsCount - failureCount;
    if (failureCount > 0) {
      logger.error(
        { index: targetIndexName, containerName, documentsCount, failureCount },
        'Document deletion failed.',
      );
    }
    return { id: module.id, documentsCount, successCount, failureCount };
  }

  //#endregion

  //#region For Training, Training Modules, Content
  //getDefaultTrainingModules: get list of system/default training modules
  // async function getDefaultTrainingModules(): Promise<TrainingModule[]> {
  //     const relicOrganization = await encapsulatedInstance.orgService.getRelicCareOrganization();
  //     return (await instance.mongo.reliccare.db.collection<TrainingModule>(DB_COL_TRAINING_MODULES)
  //         .find({ organizationId: relicOrganization.id }, { projection: { _id: 0 } })
  //         .toArray());
  // }

  //getTrainingModules: get list of training modules
  async function getTrainingModulesFromDB(ids: string[]): Promise<TrainingModule[]> {
    if (ids.length == 0) {
      return [];
    }
    return await trainingModulesColl.find({ id: { $in: ids.map((id) => id) } }, { projection: { _id: 0 } }).toArray();
  }

  async function saveTrainingToDB(resource: Training): Promise<Training> {
    const whoami: IUserIdentity = requestContext.get('whoami') as IUserIdentity;
    const currentUser = {
      id: whoami?.id as string,
      resourceType: whoami?.resourceType as string,
    };
    const now: Date = new Date();
    resource.id = uuidv4().toString();
    resource.createDate = resource.createDate || now;
    resource.createdBy = resource.createdBy || currentUser;
    const insertResult = await trainingsColl.insertOne(resource);
    if (!(insertResult.acknowledged && insertResult.insertedId)) {
      throw fastify.httpErrors.internalServerError(`Save training failed, acknowledged: ${insertResult.acknowledged}.`);
    }
    return await getTrainingFromDB({ id: resource.id });
  }

  /**
   * Retrieves Training Details from DB
   * @param id
   * @returns {Training}
   * @throws {Error} NotFoundError
   */
  async function getTrainingFromDB(query: Partial<RelicSearchQuery>): Promise<Training> {
    const {
      filter,
      options: { projection },
    } = new MongoQueryBuilder().withQuery(query).build();
    const training = await trainingsColl.findOne(filter, { projection });
    if (!training) {
      throw fastify.httpErrors.notFound(`Training not found, id: ${query.id} `);
    }
    const trainingModules: TrainingModule[] = await getTrainingModulesFromDB(
      training.modules?.map(({ id }) => id) || [],
    );
    training.modules = trainingModules.map((module) => {
      return {
        ...module,
        ...(training.modules.find((tmodule) => module.id === tmodule.id) as AssignedTrainingModule),
      };
    });
    return training;
  }

  async function saveTrainingModuleToDB(resource: TrainingModule): Promise<TrainingModule> {
    const whoami: IUserIdentity = requestContext.get('whoami') as IUserIdentity;
    const currentUser = {
      id: whoami?.id as string,
      resourceType: whoami?.resourceType as string,
    };
    const now: Date = new Date();
    resource.id = uuidv4().toString();
    resource.createDate = resource.createDate || now;
    resource.createdBy = resource.createdBy || currentUser;
    resource.updateDate = now;
    resource.updatedBy = currentUser;

    const insertResult = await trainingModulesColl.insertOne(resource);
    if (!(insertResult.acknowledged && insertResult.insertedId)) {
      throw fastify.httpErrors.internalServerError(
        `Save training module failed, acknowledged: ${insertResult.acknowledged}`,
      );
    }
    return resource;
  }

  /**
   * Retrieves Training Module details from DB
   * @param id Training Module Id
   * @returns {TrainingModule}
   * @throws {Error} NotFoundError
   */
  async function getTrainingModuleFromDB(query: Partial<RelicSearchQuery>): Promise<TrainingModule> {
    const {
      filter,
      options: { projection },
    } = new MongoQueryBuilder().withQuery(query).build();
    const trainingModule = await trainingModulesColl.findOne(filter, { projection: projection });
    if (!trainingModule) {
      throw fastify.httpErrors.notFound(`Training module not found, id: ${query.id}.`);
    }
    return trainingModule;
  }

  /**
   * Updates Training Module details in DB
   * @param query
   * @param input
   * @returns {TrainingModule}
   * @throws {Error}
   */
  async function updateTrainingModuleToDB(
    query: Partial<RelicSearchQuery>,
    input: Partial<TrainingModule>,
  ): Promise<TrainingModule> {
    const {
      filter,
      options: { projection },
    } = new MongoQueryBuilder().withQuery(query).build();
    const whoami: IUserIdentity = requestContext.get('whoami') as IUserIdentity;
    const currentUser = { id: whoami?.id as string, resourceType: whoami?.resourceType as string };
    input.updatedBy = currentUser;
    input.updateDate = new Date();
    const result = await trainingModulesColl.findOneAndUpdate(
      filter,
      { $set: input },
      { projection, includeResultMetadata: true },
    );
    if (!result.ok || !result.value) {
      throw fastify.httpErrors.internalServerError(
        `Update training module failed, acknowledged: ${result.lastErrorObject}`,
      );
    }
    return result.value;
  }
  /**
   * addModuleToTraining: add training modules to a Training
   * If indexer creation failed, module is not added and error is ignored (logged only)
   * @param id Training ID
   * @param {Array<{ id: string }>} modules List of Training Modules to add
   * @returns {Training} Updated Training
   * @throws {Error} Training NotFoundError, Training Module NotFoundError
   */
  async function addModulesToTraining(id: string, modules: Array<{ id: string }>): Promise<Training> {
    const training: Training = await getTrainingFromDB({ id });
    const assignedModuleIds: string[] = training.modules.map(({ id }) => id);

    for (const moduleToAdd of modules) {
      if (assignedModuleIds.includes(moduleToAdd.id)) {
        logger.info({ id, moduleId: moduleToAdd.id }, 'Module skipped. Module already assigned to training.');
        continue;
      }

      const module: TrainingModule = await getTrainingModuleFromDB({ id: moduleToAdd.id });
      const { indexerName, parsingMode } = generateTrainingIndexer(training.name, module);
      await createAzureAISearchIndexer({
        ...INDEXER_JSON_TEMPLATE,
        name: indexerName,
        targetIndexName: training.indexName,
        skillsetName: training.skillsetName,
        dataSourceName: module.storage.datasourceName as string,
        schedule: {
          interval: training.schedule?.interval || INDEXER_JSON_TEMPLATE.schedule?.interval || 'PT24H',
          startTime: training.schedule?.startTime || new Date(),
        },
      })
        .catch((err) => {
          logger.error(err, `Azure Indexer creation failed, moduledId: ${module.id}, trainingId: ${id}`);
        })
        .then(async () => {
          logger.info({ id, moduleId: module.id, indexer: indexerName }, 'Azure Indexer creation succeeded.');
          const assignedTrainingModule: AssignedTrainingModule = {
            ...module,
            indexerName,
            parsingMode,
          };
          await trainingsColl.updateOne({ id: training.id }, { $push: { modules: assignedTrainingModule } });
        });
    }
    return await getTrainingDetails({ id });
  }

  /**
   * removeModulesFromTraining: remove training modules from Training
   * @param id Training ID
   * @param {Array<{ id: string }>} modules List of Training Modules to remove
   * @returns {Training} Updated Training
   * @throws {Error} Training NotFoundError, Training Module NotFoundError
   */
  async function removeModulesFromTraining(id: string, modules: Array<{ id: string }>): Promise<Training> {
    const training: Training = await getTrainingFromDB({ id });

    for (const moduleToRemove of modules) {
      const module: AssignedTrainingModule = training.modules.find(
        (m) => m.id == moduleToRemove.id,
      ) as AssignedTrainingModule;
      if (!module) {
        logger.info({ id, moduleId: moduleToRemove.id }, 'Module skipped. Module is not assigned to training.');
        continue;
      }

      //temporarily set training module status = 'inprogress_delete'
      const result = await trainingsColl.updateOne(
        { id: training.id, 'modules.id': module.id },
        { $set: { 'modules.$.indexerStatus': 'inprogress_delete', 'modules.$.indexerLastUpdateDate': new Date() } },
        { upsert: true },
      );

      if (result.matchedCount == 0 || result.modifiedCount == 0) {
        //module not assigned to training or module already in 'inprogress_delete' state, then do nothing
        logger.info({ id, moduleId: moduleToRemove.id }, 'Module skipped. Delete process inprogress.');
        continue;
      }

      //Perform actual deletion, no waiting
      deleteAzureAISearchIndexer(training.indexName, module)
        .then(async (result) => {
          logger.info({ stats: result }, 'Document deletion from index succeeded.');
          await trainingsColl.updateOne(
            { id: training.id, 'modules.id': module.id },
            { $pull: { modules: { id: result.id } } },
          );
        })
        .catch((err) => logger.error(err, 'Document deletion from index failed.'));
    }
    return await getTrainingDetails({ id });
  }

  /**
   *
   * @param id Training ID
   * @returns {Training} Training
   * @throws {Error} Training NotFoundErr
   */
  async function getTrainingDetails(query: Partial<RelicSearchQuery>): Promise<Training> {
    const training: Training = await getTrainingFromDB(query);
    training.updateDate = training.createDate as Date;
    //attach status for each indexer(module)
    for (let i = 0; i < training.modules.length; i++) {
      const module = training.modules[i];
      if (!module) continue;
      const { indexerName, indexerStatus } = module;
      //delete process in progress
      if (indexerStatus && indexerStatus == 'inprogress_delete') {
        continue;
      }
      await indexerClient
        .getIndexerStatus(indexerName)
        .then((result) => {
          const indexerStatus = result.lastResult?.status?.toLowerCase() as typeof module.indexerStatus;
          const indexerLastUpdateDate: Date = result.lastResult?.endTime as Date;
          const updateDate: Date =
            indexerLastUpdateDate > (training.updateDate as Date)
              ? indexerLastUpdateDate
              : (training.updateDate as Date);
          const updatedModule = {
            ...module,
            indexerStatus,
            indexerLastUpdateDate,
            updateDate,
          };
          training.modules[i] = updatedModule as AssignedTrainingModule;
        })
        .catch((err) => {
          logger.error(err, `Failed to retrieve module status, indexerName: ${indexerName}`);
        });
    }
    return training;
  }

  //#endregion

  //#region For Crawler and Training Content
  async function saveTrainingContentToDB(resource: TrainingContent): Promise<TrainingContent> {
    logger.info('Saving new training content to database.');
    const whoami: IUserIdentity = requestContext.get('whoami') as IUserIdentity;
    const currentUser = {
      id: whoami?.id as string,
      resourceType: whoami?.resourceType || 'system',
    };
    const now: Date = new Date();
    resource.id = uuidv4().toString();
    resource.createDate = resource.createDate || now;
    resource.createdBy = resource.createdBy || currentUser;
    resource.updateDate = now;
    resource.updatedBy = currentUser;
    resource.deleted = false;
    if (!resource.sourceDocumentId) resource.sourceDocumentId = resource.id;

    const insertResult = await trainingContentsColl.insertOne(resource);
    if (!(insertResult.acknowledged && insertResult.insertedId)) {
      throw fastify.httpErrors.internalServerError(
        `Save training content failed, acknowledged: ${insertResult.acknowledged}`,
      );
    }
    logger.info({ id: resource.id }, 'Training content succesfully saved.');
    return resource;
  }

  /**
   *
   * @param moduleId
   * @param query
   * @returns {TrainingContent}
   * @throws {Error}
   */
  async function getTrainingContentFromDB(query: Partial<RelicSearchQuery>): Promise<TrainingContent> {
    const {
      filter,
      options: { projection },
    } = new MongoQueryBuilder().withQuery(query).deleted(false).build();

    const content = await trainingContentsColl.findOne(filter, { projection });
    if (!content) {
      throw fastify.httpErrors.notFound(`Training content not found, id: ${query.id} `);
    }
    return content;
  }

  async function getTextContent(filepath: string) {
    const isTxt: boolean = path.extname(filepath).includes('txt');
    const isJson: boolean = path.extname(filepath).includes('json');
    if (isTxt) {
      return readFileSync(filepath, 'utf-8');
    }
    if (!isJson) {
      return await docParser.parseOfficeAsync(filepath, { putNotesAtLast: true });
    }
    return '';
  }

  /**
   * Training Job Handler for type = 'file'
   * @param input
   * @returns
   */
  async function handleFileContent(jobId: string, input: TrainingContent): Promise<void> {
    const filesForCleanup = [];
    try {
      logger.info({ jobId }, 'Processing job request.');
      const result = await trainingContentsColl.updateOne(
        { id: input.id, status: 'pending', deleted: false },
        { $set: { status: 'inprogress', lastProcessingDate: new Date() } },
      );
      if (result.matchedCount == 0 || result.modifiedCount == 0) {
        logger.info({ jobId }, 'Training content not in PENDING state.');
        return;
      }
      const content: TrainingContent = await getTrainingContentFromDB({ id: input.id });
      const module: TrainingModule = await getTrainingModuleFromDB({ id: content.moduleId });
      const containerClient = blobServiceClient.getContainerClient(module.storage.containerName);

      const { id, filename, documentId, moduleId } = content;
      const workingDirectory = path.join(
        ROOT_DOWNLOADS_FOLDER,
        moduleId,
        `${crypto.randomUUID()}_${new Date().getTime()}`,
      );
      if (!mkdirSync(workingDirectory, { recursive: true })) {
        throw new Error(`Directory creation failed, directory: ${workingDirectory}`);
      }
      filesForCleanup.push(workingDirectory);
      const downloadFilepath = path.join(workingDirectory, `${Date.now()}_${id}_${filename}`);
      const blobClient = containerClient.getBlockBlobClient(documentId);
      if (!(await blobClient.exists())) {
        throw new Error(`Blob not found, documentId: ${documentId}`);
      }
      await blobClient.downloadToFile(downloadFilepath);
      filesForCleanup.push(downloadFilepath);
      const txtContent: string = await getTextContent(downloadFilepath);
      const data = JSON.stringify(
        {
          url: content.url,
          title: content.filename,
          content: txtContent,
          originalFilepath: content.url,
        },
        null,
        2,
      );

      const extractedJsonDocumentId = `${path.parse(content.documentId).name}.json`;
      const jsonBlobClient = containerClient.getBlockBlobClient(extractedJsonDocumentId);
      await jsonBlobClient.upload(data, data.length, {
        metadata: { deleted: 'false' },
        blobHTTPHeaders: { blobContentType: 'application/json' },
      });
      await trainingContentsColl.updateOne(
        { id: content.id },
        { $set: { status: 'done', jsonContentUrl: jsonBlobClient.url } },
      );
      logger.info({ jobId }, 'Training content successfully processed.');
    } catch (err) {
      logger.error({ jobId, err }, 'Training content processing failed.');
      await trainingContentsColl.updateOne(
        { id: input.id },
        { $set: { status: 'failed', errorDetails: (err as Error).message || JSON.stringify(err) } },
      );
    } finally {
      filesForCleanup.forEach((f) => {
        try {
          if (f.length > 0) rmSync(f, { recursive: true, force: true });
        } catch (err) {
          logger.error({ jobId, err, path: f }, 'File/Directory deletion failed.');
        }
      });
    }
  }

  /**
   * Training Job Handler for types = 'url'.
   * For url with status = 'pending' or 'sitemap_ready'
   * For url with status = 'sitemap_ready', sitemap is sent to the crawler
   * @param input
   * @returns
   */
  async function handleUrlContent(
    jobId: string,
    input: TrainingContent & { options?: RelicCrawlerOptions },
  ): Promise<void> {
    try {
      const result = await trainingContentsColl.updateOne(
        { id: input.id, status: { $in: ['pending', 'sitemap_ready'] }, deleted: false },
        { $set: { status: 'inprogress', lastProcessingDate: new Date() } },
      );
      if (result.matchedCount == 0 || result.modifiedCount == 0) {
        logger.info({ jobId }, 'Training content not in PENDING or SITEMAP_READY state.');
        return;
      }
      const content: TrainingContent = await getTrainingContentFromDB({ id: input.id });
      const module: TrainingModule = await getTrainingModuleFromDB({ id: content.moduleId });
      if (!module) {
        throw fastify.httpErrors.badRequest(`training module not found, id: ${content.moduleId}`);
      }
      let urls: URLLike[] = [];
      if (content.sitemap && content.sitemap.length > 0) {
        //handle type=url with status = 'sitemap_ready'
        urls = content.sitemap;
        urls.forEach((url) => {
          url.sourceDocumentId = content.id as string;
        }); // ensure current content is root parent
      } else {
        urls = [
          {
            id: content.id as string,
            sourceDocumentId: content.sourceDocumentId as string,
            url: content.url as string,
          },
        ];
      }

      const crawlerRequest: CrawlRequest = {
        isEstimatorRun: false,
        urls,
        crawlStorage: {
          accountName: azureConfig.storage.accountName,
          accountKey: azureConfig.storage.accountKey,
          containerName: module.storage.containerName,
        },
        relicMessageQueueParams: {
          ...contentWriterWorkQueue.getQueueParams(),
          jobName: JOB_HANDLE_SCRAPED_CONTENT,
          moduleId: module.id,
          organizationId: module.organizationId,
          createdBy: content.createdBy,
        },
        options: {
          crawlEmbeddedLinks: false,
          maxUrlsToCrawl: 300,
          useReadableContent: true,
          maxDepth: 5,
          relevanceParams: {
            relevanceApiEndpoint: config.get('RELEVANCE_CHECKER_URL'),
            subject: module.subject,
            intent: module.intent,
          },
          ...(input.options || {}),
        } as RelicCrawlerOptions,
      };

      const apifyActorRunDetails: ActorRun = await fastify.runCrawler(crawlerRequest);
      if (apifyActorRunDetails.status == 'FAILED') {
        throw new Error(
          `Crawler failed, runId: ${apifyActorRunDetails.id}, status: ${apifyActorRunDetails.status}, message: ${apifyActorRunDetails.statusMessage}`,
        );
      }
      logger.info({ jobId, runId: apifyActorRunDetails.id }, 'Crawler process triggered.');
    } catch (e) {
      logger.error({ jobId, err: e }, 'Training content processing failed.');
      await trainingContentsColl.updateOne(
        { id: input.id },
        { $set: { status: 'failed', errorDetails: (e as Error).message || JSON.stringify(e) } },
      );
    }
  }

  /**
   * Training Job Handler for types = 'folder'.
   * For folder, sitemap is sent to the crawler
   * @param input
   * @returns
   */
  async function handleFolderContent(
    jobId: string,
    input: TrainingContent & { options?: RelicCrawlerOptions },
  ): Promise<void> {
    try {
      const result = await trainingContentsColl.updateOne(
        { id: input.id, status: { $in: ['pending'] }, deleted: false },
        { $set: { status: 'inprogress', lastProcessingDate: new Date() } },
      );
      if (result.matchedCount == 0 || result.modifiedCount == 0) {
        logger.info({ jobId }, 'Training content not in PENDING state.');
        return;
      }
      const content: TrainingContent = await getTrainingContentFromDB({ id: input.id });
      const module: TrainingModule = await getTrainingModuleFromDB({ id: content.moduleId });
      if (!module) {
        throw fastify.httpErrors.badRequest(`training module not found, id: ${content.moduleId}`);
      }
      let urls: URLLike[] = [];
      if (content.sitemap && content.sitemap.length > 0) {
        urls = content.sitemap;
        urls.forEach((url) => {
          url.sourceDocumentId = content.id as string;
        }); // ensure current content is root parent
      } else {
        throw new Error(`Sitemap not found, id: ${content.id!}`);
      }

      const crawlerRequest: CrawlRequest = {
        isEstimatorRun: false,
        urls,
        crawlStorage: {
          accountName: azureConfig.storage.accountName,
          accountKey: azureConfig.storage.accountKey,
          containerName: module.storage.containerName,
        },
        relicMessageQueueParams: {
          ...contentWriterWorkQueue.getQueueParams(),
          jobName: JOB_HANDLE_SCRAPED_CONTENT,
          moduleId: module.id,
          organizationId: module.organizationId,
          createdBy: content.createdBy,
        },
        options: {
          crawlEmbeddedLinks: false,
          maxUrlsToCrawl: 300,
          useReadableContent: true,
          maxDepth: 5,
          relevanceParams: {
            relevanceApiEndpoint: config.get('RELEVANCE_CHECKER_URL'),
            subject: module.subject,
            intent: module.intent,
          },
          ...(input.options || {}),
        } as RelicCrawlerOptions,
      };

      const apifyActorRunDetails: ActorRun = await fastify.runCrawler(crawlerRequest);
      if (apifyActorRunDetails.status == 'FAILED') {
        throw new Error(
          `Crawler failed, runId: ${apifyActorRunDetails.id}, status: ${apifyActorRunDetails.status}, message: ${apifyActorRunDetails.statusMessage}`,
        );
      }
      logger.info({ jobId, runId: apifyActorRunDetails.id }, 'Crawler process triggered.');
      await trainingContentsColl.updateOne({ id: input.id }, { $set: { status: 'done' } });
    } catch (e) {
      logger.error({ jobId, err: e }, 'Training content processing failed.');
      await trainingContentsColl.updateOne(
        { id: input.id },
        { $set: { status: 'failed', errorDetails: (e as Error).message || JSON.stringify(e) } },
      );
    }
  }

  async function handleScrapedContent(jobId: string, scraperOutput: ScraperOutput) {
    logger.info({ jobId }, 'Processing job request.');
    const { id, url, documentId, sourceDocumentId } = scraperOutput;
    delete scraperOutput.createDate; //createDate from (redis) scraperoutput is string, not Date
    const result = await trainingContentsColl.updateOne(
      { id },
      {
        $set: {
          ...scraperOutput,
          lastProcessingDate: new Date(),
          updateDate: new Date(),
        },
      },
    );

    if (result.modifiedCount > 0) {
      logger.info({ jobId, id }, 'Training content successfully updated with scraped content.');
      return;
    }

    //check parent details
    const parentContent: TrainingContent = await getTrainingContentFromDB({ id: sourceDocumentId });
    // type=folder is ignored
    if (!parentContent || (parentContent.type !== 'folder' && parentContent.status != 'done')) {
      //parent content not found, fail then retry
      throw fastify.httpErrors.badRequest(`Parent content not found or not in DONE state, id: ${sourceDocumentId}`);
    }

    const { moduleId, organizationId } = parentContent;

    //check if url is duplicate
    const { hasDuplicates } = await checkForDuplicateUrlsInModule(moduleId, [url]);
    if (hasDuplicates) {
      logger.info({ jobId, id }, 'Training content with same URL found, deleting file from storage.');
      await removeDuplicateCrawledUrlContentFromStorage(moduleId, documentId);
      return;
    }
    //create new training content
    logger.info({ jobId, id }, 'Creating new training content from scraped content.');
    const newTrainingContent: TrainingContent = {
      ...scraperOutput,
      moduleId,
      organizationId,
      createDate: new Date(),
      lastProcessingDate: new Date(),
      updateDate: new Date(),
      deleted: false,
    };
    await trainingContentsColl.insertOne(newTrainingContent);
    logger.info({ jobId, id }, 'Successfully created new training content.');
  }

  async function removeDuplicateCrawledUrlContentFromStorage(moduleId: string, documentId: string): Promise<void> {
    logger.info({ moduleId, documentId }, 'Deleting duplicate training content from storage.');
    //assume that training content is not yet saved in database, and indexer not yet running
    const module: TrainingModule = await getTrainingModuleFromDB({ id: moduleId });
    const containerClient = blobServiceClient.getContainerClient(module.storage.containerName);
    await containerClient.getBlockBlobClient(documentId).deleteIfExists();
    logger.info({ moduleId, documentId }, 'File deletion success.');
  }

  //#endregion

  //#region Training utils
  /**
   * generateTrainingIndexer create training indexer details (indexerName)
   * @param training
   * @param module
   * @returns {TrainingIndexer}
   */
  function generateTrainingIndexer(
    trainingName: string,
    module: TrainingModule,
  ): { indexerName: string; parsingMode: 'json' } {
    return {
      //<trainingname full>-<first 35 characters of data source>
      indexerName: `${trainingName.split(' ')?.join('')}-${Date.now()}-${module.storage?.datasourceName?.substring(
        0,
        35,
      )}`
        ?.toLowerCase()
        ?.replace(/[^a-z0-9-]+|(^-+|-+$)/g, ''),
      parsingMode: 'json',
    };
  }

  /**
   * Note: All crawled pages are considered child content.
   * @param moduleId
   * @param url
   * @returns
   */
  async function checkForDuplicateUrlsInModule(
    moduleId: string,
    urls: string[],
  ): Promise<{ hasDuplicates: boolean; urls: string[] }> {
    const normalizedRegexes = urls.map((url) => {
      const parsedUrl = new URL(url);
      const normalizedUrl = `${parsedUrl.hostname}${parsedUrl.pathname.replace(/\/+$/, '')}${parsedUrl.search}${
        parsedUrl.hash
      }`;
      const escaped = normalizedUrl.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
      return {
        url: {
          $regex: `^.*?${escaped}$`,
          $options: 'i',
        },
      };
    });

    const filter = {
      moduleId,
      deleted: false,
      type: { $ne: 'folder' },
      $or: normalizedRegexes,
    };

    logger.info(filter, 'Checking for duplicate URLs in module.');
    const duplicateUrls = await trainingContentsColl.find(filter, { projection: { url: 1 } }).toArray();
    return { hasDuplicates: duplicateUrls.length > 0, urls: duplicateUrls.map((v) => v.url) };
  }

  //#endregion

  //#region Decorators, exposed,to be used by routers/handlers
  async function createTraining(input: Training): Promise<Training> {
    let organization: RelicOrganization = requestContext.get('organization') as RelicOrganization;
    input.organizationId = input.organizationId || (organization.id as string);
    if (organization.id != input.organizationId) {
      organization = await fastify.orgService.getOrganization(input.organizationId);
    }

    const found = await trainingContentsColl.findOne(
      { organizationId: input.organizationId, name: { $regex: `^${input.name}$`, $options: 'i' } },
      { projection: { _id: 0 } },
    );
    if (found) {
      throw fastify.httpErrors.badRequest(`Training with name "${input.name}" already exists.`);
    }

    // <trainingname full>-<org name 10char>-<org id 10char> (45 total char limit)
    const indexName: string = `${input.name.split(' ')?.join('')}-${organization.name
      .split(' ')
      ?.join('')
      .substring(0, 10)}-${organization?.id?.substring(0, 10)}`
      .substring(0, 45)
      .toLowerCase()
      ?.replace(/[^a-z0-9-]+|(^-+|-+$)/g, '');
    const modules: TrainingModule[] = [];

    if (input.modules.length > 0) {
      const addModules: TrainingModule[] = await getTrainingModulesFromDB(input.modules.map((module) => module.id));
      if (addModules.length > 0) {
        modules.push(...addModules);
      }
    }
    const index: SearchIndex = await createAzureAISearchIndex({
      ...INDEX_JSON_TEMPLATE,
      name: indexName,
    });

    const training: Training = await saveTrainingToDB({
      schedule: {
        interval: 'PT24H',
        startTime: new Date(),
      },
      ...input,
      id: '',
      indexName,
      skillsetName: indexName,
      modules: modules.map((module: TrainingModule) => {
        const { indexerName, parsingMode } = generateTrainingIndexer(input.name, module);
        return {
          ...module,
          indexerName,
          parsingMode,
        };
      }),
    });

    const skillsetDef: SearchIndexerSkillset = {
      ...SKILLSET_JSON_TEMPLATE,
      name: index.name,
      indexProjection: SKILLSET_PROJECTION_JSON_TEMPLATE,
    };
    skillsetDef.indexProjection?.selectors.forEach(
      (selector: SearchIndexerIndexProjectionSelector) => (selector.targetIndexName = index.name),
    );
    const skillset: SearchIndexerSkillset = await createSkillset(skillsetDef);

    training.modules.forEach(async (module) => {
      const { id, indexerName, storage, name } = module;
      await createAzureAISearchIndexer({
        ...INDEXER_JSON_TEMPLATE,
        name: indexerName,
        targetIndexName: index.name,
        skillsetName: skillset.name,
        dataSourceName: storage.datasourceName as string,
        schedule: {
          interval: training.schedule?.interval || INDEXER_JSON_TEMPLATE.schedule?.interval || 'PT24H',
          startTime: training.schedule?.startTime || new Date(),
        },
      }).catch((err: Error) => {
        training.modules = training.modules.filter((module) => module.id != id);
        logger.error(err, `failed to create indexer for module "${name}"`);
        trainingsColl.updateOne({ id: training.id }, { $pull: { modules: { id: module.id } } });
      });
    });

    return training;
  }

  async function searchTrainings(query: Partial<RelicSearchQuery>): Promise<{ total: number; data: Training[] }> {
    const { filter, options } = new MongoQueryBuilder()
      .withQuery(query)
      .withSearchField(query._search as string, ['name', 'indexName', 'description'])
      .build();

    const data = await trainingsColl.find(filter, options).toArray();

    const total = await trainingsColl.countDocuments(filter);
    return { total, data };
  }

  async function deleteTraining(query: Partial<RelicSearchQuery>): Promise<void> {
    const training: Training = await getTrainingFromDB(query);
    for (let module of training.modules) {
      await indexerClient.deleteIndexer(module.indexerName);
    }
    await indexerClient.deleteSkillset(training.skillsetName);
    await indexClient.deleteIndex(training.indexName);
    await trainingsColl.deleteOne({ id: training.id });
  }

  async function createTrainingModule(input: TrainingModule): Promise<TrainingModule> {
    let organization: RelicOrganization = requestContext.get('organization') as RelicOrganization;
    input.organizationId = input.organizationId || (organization.id as string);
    if (organization.id != input.organizationId) {
      organization = await fastify.orgService.getOrganization(input.organizationId);
    }

    const found = await trainingModulesColl.findOne(
      { organizationId: input.organizationId, name: { $regex: `^${input.name}$`, $options: 'i' } },
      { projection: { _id: 0 } },
    );
    if (found) {
      throw fastify.httpErrors.badRequest(`Training module with name "${input.name}" already exists.`);
    }
    const containerName: string = `${organization.name
      ?.split(' ')
      ?.join('')
      ?.substring(0, 10)}-${input.organizationId?.substring(0, 10)}-${input.name
      ?.split(' ')
      ?.join('')
      ?.substring(0, 20)}-${Date.now()}`
      .toLowerCase()
      ?.substring(0, 60);
    //create storage
    const containerClient: ContainerClient = blobServiceClient.getContainerClient(containerName);
    await containerClient.createIfNotExists();

    //create datasource
    const datasourceName = containerName;
    const datasource: SearchIndexerDataSourceConnection = await createDataSourceConnection({
      ...DATASOURCE_JSON_TEMPLATE,
      name: datasourceName,
      connectionString: dataSourceConnection,
      description: input.description,
      container: {
        name: containerName,
      },
    });

    return await saveTrainingModuleToDB({
      ...input,
      organizationId: input.organizationId,
      id: '',
      storage: {
        containerName: containerClient.containerName,
        datasourceName: datasource.name,
      },
      role: input.role,
    });
  }

  async function searchTrainingModules(
    query: Partial<RelicSearchQuery>,
  ): Promise<{ total: number; data: TrainingModule[] }> {
    const { filter, options } = new MongoQueryBuilder()
      .withQuery(query)
      .withSearchField(query._search as string, ['name', 'description', 'storage.containerName'])
      .build();
    const data = await trainingModulesColl.find(filter, options).toArray();
    const total = await trainingModulesColl.countDocuments(filter);
    return { total, data };
  }

  async function getTrainingModuleDetails(query: Partial<RelicSearchQuery>): Promise<TrainingModule> {
    return await getTrainingModuleFromDB(query);
  }

  /**
   * Updates Training Module details
   * @param query
   * @param input
   * @returns {TrainingModule}
   * @throws {Error}
   */
  async function updateTrainingModuleDetails(
    query: Partial<RelicSearchQuery>,
    input: Partial<TrainingModule>,
  ): Promise<TrainingModule> {
    const trainingModule: TrainingModule = await getTrainingModuleFromDB(query);
    //cleanup
    delete input.id;
    delete input.organizationId;
    delete input.storage;
    delete input.createdBy;
    delete input.createDate;
    //check if name is duplicated
    const found = await trainingModulesColl.findOne(
      {
        organizationId: trainingModule.organizationId,
        id: { $ne: trainingModule.id },
        name: { $regex: `^${input.name}$`, $options: 'i' },
      },
      { projection: { _id: 0 } },
    );
    if (found) {
      throw fastify.httpErrors.badRequest(`Training module with name "${input.name}" already exists.`);
    }
    const updatedTrainingModule: TrainingModule = {
      ...trainingModule,
      ...input,
    };
    return await updateTrainingModuleToDB(query, updatedTrainingModule);
  }

  async function deleteTrainingModule(query: Partial<RelicSearchQuery>): Promise<void> {
    const trainingModule: TrainingModule = await getTrainingModuleFromDB(query);
    const trainings: Training[] = await trainingsColl.find({ 'modules.id': trainingModule.id }).toArray();
    if (trainings.length > 0) {
      throw fastify.httpErrors.preconditionFailed(
        `module is assigned to trainings [${trainings.map((training) => training.name)}]`,
      );
    }
    await trainingModulesColl.deleteOne({ id: trainingModule.id });
    await indexerClient.deleteDataSourceConnection(trainingModule.storage.datasourceName!);
    await blobServiceClient.deleteContainer(trainingModule.storage.containerName);
    //might take time
    trainingContentsColl
      .deleteMany({ moduleId: trainingModule.id })
      .then((result) => logger.info(result, 'Training contents deleted.'))
      .catch((err) => logger.info(err, 'Error deleting training contents.'));
  }

  async function addTrainingContent(
    moduleId: string,
    input: TrainingContent & { options?: RelicCrawlerOptions },
  ): Promise<TrainingContent> {
    const module: TrainingModule = await getTrainingModuleFromDB({ id: moduleId });
    const { type, sitemap } = input;
    if (type === 'folder' && sitemap?.length == 0) {
      throw fastify.httpErrors.badRequest('Sitemap is required for folder content.');
    }

    const urlsForValidation: string[] = [];
    if (type === 'folder') {
      const collectUrls = function (parent: URLLike) {
        urlsForValidation.push(parent.url);
        parent.children?.forEach(collectUrls);
      };
      sitemap?.forEach(collectUrls);
    } else {
      urlsForValidation.push(input.url);
    }
    const { hasDuplicates, urls: duplicateUrls } = await checkForDuplicateUrlsInModule(module.id, urlsForValidation);
    if (hasDuplicates) {
      throw fastify.httpErrors.badRequest(
        `Remove Web Pages already included in this module.\n ${duplicateUrls.join('\n')}`,
      );
    }

    const { options, ...requestInput } = input;
    const trainingContent: TrainingContent = {
      ...requestInput,
      isUploaded: !['url', 'folder'].includes(input.type),
      id: '',
      moduleId,
      organizationId: module.organizationId,
      language: requestInput.language || 'EN-US',
      header: requestInput.header || 0,
      footer: requestInput.footer || 0,
      translationType: requestInput.translationType || 'mono',
      filename: requestInput.filename || input.url,
      status: requestInput.status || 'pending',
      documentId: requestInput.documentId || '',
      type: requestInput.type || '',
    };
    const content = await saveTrainingContentToDB(trainingContent);

    await contentWriterWorkQueue.add(
      JOB_HANDLE_TRAINING_CONTENT,
      { ...content, options },
      { jobId: `${content.id}-${Date.now()}` },
    );
    return content;
  }

  async function getTrainingContentDetails(query: Partial<RelicSearchQuery>): Promise<TrainingContent> {
    return await getTrainingContentFromDB(query);
  }

  async function searchTrainingContent(
    moduleId: string,
    query: Partial<RelicSearchQuery>,
  ): Promise<{ total: number; data: TrainingContent[] }> {
    const module = await getTrainingModuleFromDB({ id: moduleId });
    //search for training content directly matching the query
    const { filter, options } = new MongoQueryBuilder()
      .withQuery(query)
      .withForeignId('moduleId', module.id)
      .withSearchField(query._search as string, ['filename'])
      .deleted(false)
      .build();

    if (!query._search) {
      //if no seach filter, start search from root nodes
      //this skips upward traversal
      filter.$expr = { $eq: ['$id', '$sourceDocumentId'] };
    }

    let total = await trainingContentsColl.countDocuments(filter);
    fastify.log.debug(`Found ${total} documents`);
    if (total == 0) {
      fastify.log.debug('no data found, returning empty result');
      return { total, data: [] };
    }

    const pipeline: object[] = [{ $match: filter }, { $sort: options.sort }, { $project: options.projection }];
    if (!query._search) {
      //if no search filter, apply pagination to root nodes
      //it's easier to apply pagination for this case since we already know results start from root nodes
      pipeline.push({ $skip: options.skip });
      pipeline.push({ $limit: options.limit });
    }

    const matchedData = (await trainingContentsColl.aggregate(pipeline).toArray()) as TrainingContent[];
    fastify.log.debug(`Found ${matchedData.length} matched data`);
    if (matchedData.length == 0) {
      fastify.log.debug('no matched data, returning empty result');
      return { total, data: [] };
    }

    const tasks = [];
    tasks.push(traverseContentsFromNodeToRoot(matchedData));
    tasks.push(
      traverseContentsFromNodeToLeaf(
        matchedData,
        //query._search is not used here, as we want to return all children nodes of the initial matched nodes
        new MongoQueryBuilder().withQuery(query).withForeignId('moduleId', module.id).deleted(false),
      ),
    );

    let result: Map<string, TrainingContent> = new Map<string, TrainingContent>();
    await Promise.allSettled(tasks).then((taskResults) => {
      taskResults.forEach((taskResult) => {
        if (taskResult.status === 'fulfilled') {
          const taskResultMap = taskResult.value as Map<string, TrainingContent>;
          result = new Map<string, TrainingContent>([...result, ...taskResultMap]);
        }
      });
    });

    let finalResult = Array.from(result.values());
    if (query._search) {
      //there are new set of parents, recalculate total after upward and downward traversal
      const rootNodes = finalResult.filter((content) => content.id == content.sourceDocumentId);
      total = rootNodes.length; //only count root parents
      if (rootNodes.length > options.limit) {
        //apply pagination
        const start = options.skip;
        const end = options.skip + options.limit;
        const subset = rootNodes.slice(start, end);
        for (const r of result.values()) {
          if (!subset.find((content) => content.id == r.id)) {
            result.delete(r.id as string);
          }
        }
        finalResult = Array.from(result.values());
      }
    }
    return { total, data: finalResult };
  }

  /**
   * Traverse contents from node to root
   * @param nodes Starting nodes
   * @returns
   */
  async function traverseContentsFromNodeToRoot(nodes: TrainingContent[]): Promise<Map<string, TrainingContent>> {
    fastify.log.debug('[traverseContentsFromNodeToRoot] running...');
    if (nodes.length == 0) {
      fastify.log.debug('[traverseContentsFromNodeToRoot] done, no nodes to process');
      return new Map<string, TrainingContent>();
    }
    const nonRootNodes = nodes.filter((content) => content.id !== content.sourceDocumentId);
    if (nonRootNodes.length == 0) {
      fastify.log.debug('[traverseContentsFromNodeToRoot] done, no non-root nodes to process');
      return new Map<string, TrainingContent>();
    }
    const result = new Map<string, TrainingContent>();
    for (const content of nodes) {
      result.set(content.id as string, content);
    }
    //start a queue with non-root nodes that are not in the result
    const queue = new Set<string>(
      nodes
        .filter((content) => content.id !== content.sourceDocumentId && !result.has(content.sourceDocumentId as string))
        .map((content) => content.sourceDocumentId)
        .filter((id): id is string => id !== undefined),
    );
    fastify.log.debug(`[traverseContentsFromNodeToRoot] Found ${queue.size} non-root nodes`);
    if (queue.size == 0) {
      fastify.log.debug('[traverseContentsFromNodeToRoot] done, no non-root nodes to process');
      return result;
    }
    const iterator = queue.values();
    let current = iterator.next();
    while (!current.done) {
      const id = current.value;
      let content = result.get(id);
      if (!content) {
        //this content is not in the result, get from database
        fastify.log.debug(`[traverseContentsFromNodeToRoot] retrieving ${id} from database`);
        const contentFromDB = await trainingContentsColl.findOne({ id }, { projection: { _id: 0 } });
        if (contentFromDB) {
          content = contentFromDB;
          result.set(id, content);
        }
      }
      if (content && content.id !== content.sourceDocumentId && !result.has(content.sourceDocumentId as string)) {
        // this content is not a root node and not in the result, add the parent to queue for processing
        fastify.log.debug(`[traverseContentsFromNodeToRoot] adding ${content.sourceDocumentId} to queue`);
        queue.add(content.sourceDocumentId as string);
      }
      current = iterator.next();
    }
    fastify.log.debug('[traverseContentsFromNodeToRoot] done');
    return result;
  }

  /**
   * Traverse contents from node to leaf
   * @param nodes Starting nodes
   * @param queryBuilder Query builder
   * @returns
   */
  async function traverseContentsFromNodeToLeaf(
    nodes: TrainingContent[],
    queryBuilder: MongoQueryBuilder,
  ): Promise<Map<string, TrainingContent>> {
    fastify.log.debug('[traverseContentsFromNodeToLeaf] running...');
    if (nodes.length == 0) {
      fastify.log.debug('[traverseContentsFromNodeToLeaf] done, no nodes to process');
      return new Map<string, TrainingContent>();
    }
    const result = new Map<string, TrainingContent>();
    for (const content of nodes) {
      result.set(content.id as string, content);
    }
    //Retrieve children of nodes from the initial match only
    let parents = new Set<string>(nodes.map((content) => content.id as string));
    fastify.log.debug(`[traverseContentsFromNodeToLeaf] Found ${parents.size} parents`);
    if (parents.size == 0) {
      fastify.log.debug('[traverseContentsFromNodeToLeaf] done, no parents to process');
      return result;
    }
    const { filter: childrenFilter, options: childrenOptions } = queryBuilder.build();

    const getChildren = async function (parents: Set<string>): Promise<Set<string>> {
      if (parents.size === 0) {
        return new Set<string>();
      }
      fastify.log.debug(`[traverseContentsFromNodeToLeaf] getting children for ${parents.size} parents`);
      const parentIds = Array.from(parents);

      const children = (await trainingContentsColl
        .aggregate([
          {
            $match: {
              ...childrenFilter,
              sourceDocumentId: { $in: parentIds },
              id: { $nin: parentIds },
            },
          },
          { $sort: childrenOptions.sort },
          { $project: childrenOptions.projection },
        ])
        .toArray()) as TrainingContent[];
      fastify.log.debug(`[traverseContentsFromNodeToLeaf] got ${children.length} children`);
      for (const child of children) {
        result.set(child.id as string, child);
      }
      return new Set<string>(children.map((child) => child.id as string));
    };

    while (parents.size > 0) {
      parents = await getChildren(parents);
    }
    fastify.log.debug('[traverseContentsFromNodeToLeaf] done');
    return result;
  }

  async function deleteTrainingContent(moduleId: string, query: Partial<RelicSearchQuery>): Promise<void> {
    const module: TrainingModule = await getTrainingModuleFromDB({
      id: moduleId,
      organizationId: query.organizationId,
    });
    const whoami: IUserIdentity = requestContext.get('whoami') as IUserIdentity;
    const currentUser = { id: whoami?.id, resourceType: whoami?.resourceType };

    const containerClient = blobServiceClient.getContainerClient(module.storage.containerName);
    const deleteFromAzureStorage = async function (content: TrainingContent, containerClient: ContainerClient) {
      if (content == null) {
        return;
      }
      if (content.type != 'url') {
        //files (ex. pdf) will have .pdf and .json
        if (content.documentId) {
          //delete the uploaded source content
          await containerClient.getBlockBlobClient(content.documentId).deleteIfExists();
        }
      }

      //all contents will have .json
      //soft-delete json file while waiting for indexer to reprocess
      if (content.jsonContentUrl) {
        const jsonDocumentId = `${path.parse(content.documentId).name}.json`;
        await containerClient.getBlockBlobClient(jsonDocumentId).setMetadata({ deleted: 'true' });
      }
    };

    const { filter } = new MongoQueryBuilder().withQuery(query).withBooleanField('deleted', false).build();
    const result = await trainingContentsColl.findOneAndUpdate(
      filter,
      { $set: { deleted: true, updateDate: new Date(), updatedBy: currentUser } },
      { returnDocument: 'after', includeResultMetadata: true },
    );
    if (!result.ok) {
      throw fastify.httpErrors.internalServerError(JSON.stringify(result.lastErrorObject));
    }
    if (!result.value) {
      return;
    }
    const content: TrainingContent = result.value;
    await deleteFromAzureStorage(content, containerClient);

    //children can have children, so we need to delete them until leaf
    const deleteChildren = async function (parentIds: string[]) {
      if (parentIds.length === 0) {
        return;
      }
      const childrenFilter = { moduleId: content.moduleId, deleted: false, sourceDocumentId: { $in: parentIds } };
      let children = await trainingContentsColl.find(childrenFilter).toArray();
      while (children.length > 0) {
        const promises = [];
        const childrenIds = children.map((child) => child.id as string).filter((id) => id !== undefined);
        promises.push(
          trainingContentsColl.updateMany(
            { id: { $in: childrenIds } },
            { $set: { deleted: true, updateDate: new Date(), updatedBy: currentUser } },
          ),
        );
        children.forEach((child) => promises.push(deleteFromAzureStorage(child, containerClient)));
        const result = await Promise.allSettled(promises);
        if (result.some((r) => r.status === 'rejected')) {
          console.error(result, 'error deleting children contents');
        }
        parentIds = childrenIds;
        children = await trainingContentsColl
          .find({ ...childrenFilter, sourceDocumentId: { $in: parentIds } })
          .toArray();
      }
    };

    deleteChildren([content.id as string])
      .then(() => {
        logger.info({ id: content.id }, 'Training content deleted.');
      })
      .catch((err: Error) => {
        logger.error(err, `Error deleting training content ${content.id}`);
      });
  }

  /**
   * Estimate training content
   * Creates a training content and trigger crawler with isEstimator=true
   * @param moduleId
   * @param url
   * @param options
   * @returns
   */
  async function estimateTrainingContent(
    moduleId: string,
    url: string,
    options: RelicCrawlerOptions,
  ): Promise<TrainingContent> {
    const contentType = await getContentType(url);
    const isValidHTMLPage = contentType.includes('text/html') || contentType.includes('application/xhtml+xml');
    if (!isValidHTMLPage) {
      throw fastify.httpErrors.badRequest(`Only HTML Web Pages can be browsed. Submitted content is "${contentType}"`);
    }
    const whoami: IUserIdentity = requestContext.get('whoami') as IUserIdentity;
    const currentUser = { id: whoami?.id, resourceType: whoami?.resourceType };
    options = { ...options, crawlEmbeddedLinks: true };
    const module: TrainingModule = await getTrainingModuleFromDB({ id: moduleId });
    if (await checkForDuplicateUrlsInModule(module.id, [url])) {
      throw fastify.httpErrors.badRequest(`This page is already included in the training, url=${url}`);
    }
    const content: TrainingContent = await saveTrainingContentToDB({
      id: '',
      moduleId,
      organizationId: module.organizationId,
      url,
      language: 'EN-US',
      header: 0,
      footer: 0,
      translationType: 'mono',
      filename: url,
      status: 'pending',
      documentId: '',
      type: 'url',
      isUploaded: false,
    } as TrainingContent);

    const crawlerRequest: CrawlRequest = {
      isEstimatorRun: true,
      urls: [{ url, id: content.id as string, sourceDocumentId: content.sourceDocumentId as string }],
      options,
      crawlStorage: { containerName: '' }, //Apify console will not accept null (input_schema.json)
      relicMessageQueueParams: {
        ...estimatorWorkQueue.getQueueParams(),
        jobName: JOB_HANDLE_ESTIMATOR_OUTPUT,
        moduleId: content.moduleId,
        organizationId: content.organizationId,
        createdBy: currentUser,
      },
    };
    const apifyActorRunDetails: ActorRun = await fastify.runCrawler(crawlerRequest);
    fastify.log.info({ id: content.id, apifyActorRunDetails }, 'Apify actor run details');
    //update content with status
    const result = await trainingContentsColl.findOneAndUpdate(
      { id: content.id },
      {
        $set: {
          status: apifyActorRunDetails.status === 'FAILED' ? 'sitemap_failed' : 'estimating_sitemap',
          updateDate: new Date(),
          updatedBy: currentUser,
          errorDetails:
            apifyActorRunDetails.status === 'FAILED'
              ? `Crawler failed, runId: ${apifyActorRunDetails.id}, status: ${apifyActorRunDetails.status}, message: ${apifyActorRunDetails.statusMessage}`
              : '',
        },
      },
      { returnDocument: 'after', includeResultMetadata: true },
    );
    if (!result.ok) {
      throw fastify.httpErrors.internalServerError(JSON.stringify(result.lastErrorObject));
    }
    return result.value as TrainingContent;
  }

  /**
   * Handle estimator output
   * save sitemap and status to training content
   * @param jobId
   * @param estimatorOutput
   * @returns
   */
  async function handleUrlEstimatorOutput(jobId: string, estimatorOutput: Partial<ScraperOutput>) {
    logger.info({ jobId }, 'Processing job request.');
    const { id, status, sitemap, errorDetails } = estimatorOutput;
    const update: Partial<TrainingContent> = {
      updateDate: new Date(),
      lastProcessingDate: new Date(),
      status: status,
      sitemap: sitemap,
      errorDetails: errorDetails,
    };

    const result = await trainingContentsColl.findOneAndUpdate(
      { id },
      { $set: update },
      { returnDocument: 'after', includeResultMetadata: true },
    );
    if (!result.ok) {
      throw fastify.httpErrors.internalServerError(JSON.stringify(result.lastErrorObject));
    }
    logger.info({ jobId, id }, 'Training content successfully updated with estimator result.');
    return result.value as TrainingContent;
  }

  async function scrapeWithSitemap(input: TrainingContent & { options?: RelicCrawlerOptions }) {
    const { id, sitemap } = input;
    const content: TrainingContent = await getTrainingContentFromDB({ id });
    if (!sitemap || sitemap.length == 0) {
      throw fastify.httpErrors.badRequest('Sitemap is required.');
    }
    //update sitemap
    const result = await trainingContentsColl.updateOne(
      { id: content.id, status: 'sitemap_ready' },
      { $set: { sitemap: sitemap } },
    );
    if (result.matchedCount == 0) {
      throw fastify.httpErrors.preconditionFailed(`Training content with id ${id} is not in sitemap_ready status.`);
    }
    //add to queue
    await contentWriterWorkQueue.add(
      JOB_HANDLE_TRAINING_CONTENT,
      { ...content, options: input.options },
      { jobId: `${content.id}-${Date.now()}` },
    );
    return content;
  }

  async function getContentType(url: string): Promise<string> {
    const resp = await fetch(url, {
      method: 'GET',
    });
    if (!resp.ok) {
      return '';
    }
    const contentType = resp.headers.get('content-type') || '';
    return contentType;
  }

  fastify.decorate('createTraining', createTraining);
  fastify.decorate('getTrainingDetails', getTrainingDetails);
  fastify.decorate('searchTrainings', searchTrainings);
  fastify.decorate('deleteTraining', deleteTraining);
  fastify.decorate('addModulesToTraining', addModulesToTraining);
  fastify.decorate('removeModulesFromTraining', removeModulesFromTraining);
  fastify.decorate('createTrainingModule', createTrainingModule);
  fastify.decorate('searchTrainingModules', searchTrainingModules);
  fastify.decorate('getTrainingModuleDetails', getTrainingModuleDetails);
  fastify.decorate('updateTrainingModuleDetails', updateTrainingModuleDetails);
  fastify.decorate('deleteTrainingModule', deleteTrainingModule);
  fastify.decorate('addTrainingContent', addTrainingContent);
  fastify.decorate('getTrainingContentDetails', getTrainingContentDetails);
  fastify.decorate('searchTrainingContent', searchTrainingContent);
  fastify.decorate('deleteTrainingContent', deleteTrainingContent);
  fastify.decorate('estimateTrainingContent', estimateTrainingContent);
  fastify.decorate('scrapeWithSitemap', scrapeWithSitemap);

  //#endregion
  fastify.register(communicationPlugin, options);
};

export default trainingsPlugin;
