import { FastifyInstance, FastifyPluginAsync, FastifyPluginOptions, FastifyRegisterOptions } from 'fastify'
import pccProvider from '../../routes/pccProvider'
import guestProvider from '../../routes/guestProvider'
import medplumProvider from '../../routes/medplumProvider'

declare module 'fastify' {}

// Data Provider Options
type ProviderOptions = {
    name: string
    schema?: Record<string, any>
}
// Add ProviderOptions to RegisterOptions
type ProviderRegisterOptions = FastifyRegisterOptions<ProviderOptions>

const routeProviderPlugin: FastifyPluginAsync = async (fastify: FastifyInstance, options: FastifyPluginOptions) => {
    fastify.log.info('Route Provider loading...');

    // Register Guest APIs through guestProvider
    fastify.register(guestProvider, {
        ...options,
        name: 'guestProvider',
        schema: {
            tags: ['Guest Services']
        }
    } as ProviderRegisterOptions)

    // Register pcc APIs through pccProvider
    fastify.register(pccProvider, {
        ...options,
        name: 'pccProvider',
        schema: {
            tags: ['PointClickCare Backend Service']
        }
    } as ProviderRegisterOptions)

    // Register medplum APIs through medplumProvider
    fastify.register(medplumProvider, {
        ...options,
        name: 'medplumProvider',
        schema: {
            tags: ['Medplum Backend Service']
        }
    } as ProviderRegisterOptions)
}

export default routeProviderPlugin
