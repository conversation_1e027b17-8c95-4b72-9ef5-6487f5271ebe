'use strict';

import { FastifyPluginAsync } from "fastify";
import { BlobSASSignatureValues, BlobServiceClient, ContainerSASPermissions, generateBlobSASQueryParameters, SASProtocol, StorageSharedKeyCredential } from '@azure/storage-blob';
import * as msal from '@azure/msal-node';
import config from "config";
import { RelicPatient } from "relic-ui";
import { RelicOrganization } from "relic-ui";
import { User, PasswordProfile, ObjectIdentity } from "@microsoft/microsoft-graph-types";
import { AuthenticationResult } from "@azure/msal-node";
import { ClientSecretCredential } from "@azure/identity";
import { Client } from "@microsoft/microsoft-graph-client";
import { TokenCredentialAuthenticationProvider } from "@microsoft/microsoft-graph-client/authProviders/azureTokenCredentials";
import { RelicPractitioner } from 'relic-ui';

export type generateSASTokenOptions = {
    containerName: string;
    accountName?: string;
    accountKey?: string;
    permissions?: string;
}

declare module 'fastify'{
    export interface FastifyInstance {
        deleteB2cPatient(b2cId: string): Promise<void>;
    }
}

const azurePlugin: FastifyPluginAsync = async (encapsulatedInstance: any, options: any) => {

    const issuerUrl: string = config.get('AZURE.AADB2C.ISSUER_URL');
    /**
     * permissions: string, default=rwc
     * sample value: 
     *         rwc  (refers to read, write, create)
     *         rwcd  (refers to read, write, create, delete)
     *         
     * see: https://learn.microsoft.com/en-us/azure/storage/blobs/sas-service-create-javascript
     */
    encapsulatedInstance.decorate('generateAzureStorageSASToken', async function (options: generateSASTokenOptions): Promise<string> {
        let { containerName, accountName, permissions, accountKey } = {
            accountName: config.get('AZURE.DOCUMENT_STORAGE_ACCOUNT_NAME') as string,
            accountKey: config.get('AZURE.DOCUMENT_STORAGE_ACCOUNT_KEY') as string,
            permissions: 'rwc', // read,create,write
            ...options
        }
        if (accountName == (config.get('AZURE.TRAINING_STORAGE_ACCOUNT_NAME') as string)) {
            accountKey = config.get('AZURE.TRAINING_STORAGE_ACCOUNT_KEY') as string;
        }
        const startsOn: Date = new Date();
        const expiresOn: Date = new Date(startsOn.getTime() + (10 * 60 * 1000)); // 10mins
        const blobServiceClient = BlobServiceClient.fromConnectionString(`DefaultEndpointsProtocol=https;AccountName=${accountName};AccountKey=${accountKey};EndpointSuffix=core.windows.net`);
        const containerClient = blobServiceClient.getContainerClient(containerName);
        //Create container if it does not exist.
        if (accountName == (config.get('AZURE.DOCUMENT_STORAGE_ACCOUNT_NAME') as string)) {
            await containerClient.createIfNotExists();
        }

        const sasOptions: BlobSASSignatureValues = {
            containerName: containerName,
            permissions: ContainerSASPermissions.parse(permissions),
            startsOn: startsOn,
            expiresOn: expiresOn,
            protocol: SASProtocol.Https
        };

        return generateBlobSASQueryParameters(sasOptions, blobServiceClient.credential as StorageSharedKeyCredential).toString();
    });

    function initializeMicrosoftGraphClient(): Client {
        const clientId: string = config.get('AZURE.AADB2C.CLIENT_ID');
        const tenantId: string = config.get('AZURE.AADB2C.TENANT_ID');
        const clientSecret: string = config.get('AZURE.AADB2C.CLIENT_SECRET');
        const graphMicrosoftUrl: string = config.get('AZURE.AADB2C.GRAPH_MICROSOFT_URL');
        const credential = new ClientSecretCredential(
            tenantId,
            clientId,
            clientSecret,
        );
        const url: string = `${graphMicrosoftUrl}/.default`;
        const authProvider = new TokenCredentialAuthenticationProvider(credential, {
            // The client credentials flow requires that you request the
            // /.default scope, and pre-configure your permissions on the
            // app registration in Azure. An administrator must grant consent
            // to those permissions beforehand.
            scopes: [url],
        });

        const graphClient: Client = Client.initWithMiddleware({ authProvider: authProvider });
        return graphClient;
    }

    const microsoftGraphClientInstance: Client = initializeMicrosoftGraphClient();

    encapsulatedInstance.decorate('generateAadB2cToken', async function (): Promise<AuthenticationResult> {
        const clientId: string = config.get('AZURE.AADB2C.CLIENT_ID');
        const authority: string = config.get('AZURE.AADB2C.AUTHORITY');
        const clientSecret: string = config.get('AZURE.AADB2C.CLIENT_SECRET');
        const clientObjectId: string = config.get('AZURE.AADB2C.CLIENT_OBJECT_ID');
        const graphMicrosoftUrl: string = config.get('AZURE.AADB2C.GRAPH_MICROSOFT_URL');
        // MSAL configs
        const msalConfig = {
            auth: {
                // 'Application (client) ID' of app registration in the Azure AD B2C admin center - this value is a GUID
                clientId: clientId,
                // Client secret 'Value' (not the ID) from 'Client secrets' in app registration in Azure AD B2C admin center
                clientSecret: clientSecret,
                // Full directory URL, in the form of https://login.microsoftonline.com/<tenant>
                authority: authority,
                // 'Object ID' of app registration in the Microsoft Azure AD B2C - this value is a GUID
                clientObjectId: clientObjectId
            }
        }
        // Initialize MSAL
        const msalConfidentialClientApp = new msal.ConfidentialClientApplication(msalConfig)

        // In a client credentials flow, the scope is always in the format '<resource>/.default'
        const url: string = `${graphMicrosoftUrl}/.default`;
        const tokenRequest = {
            scopes: [url]
        }

        // Obtain the token. This is a limited time token but will work for the demo scenario.
        // It is possible to cache this token and refresh it when it expires to address concurrent requests
        return await msalConfidentialClientApp.acquireTokenByClientCredential(tokenRequest);
    });

    function generateRandomPassword(length = 16) {
        const upperChars: string = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const lowerChars: string = "abcdefghijklmnopqrstuvwxyz";
        const numbers: string = "0123456789";
        const specialChars: string = "@#$&%*";
        const allChars: string = upperChars + lowerChars + numbers + specialChars;

        let password: string = '';
        password += upperChars[Math.floor(Math.random() * upperChars.length)];
        password += lowerChars[Math.floor(Math.random() * lowerChars.length)];
        password += numbers[Math.floor(Math.random() * numbers.length)];
        password += specialChars[Math.floor(Math.random() * specialChars.length)];

        for (let i = 4; i < length; i++) {
            password += allChars[Math.floor(Math.random() * allChars.length)];
        }

        return password.split('').sort(() => 0.5 - Math.random()).join('');
    }

    function generatePractitionerUPN(practitioner: RelicPractitioner): string {
        return `${practitioner.provider}-${practitioner.id}@${issuerUrl}`;
    }

    async function getMsGraphUser (user: RelicPractitioner | RelicPatient): Promise<User> {
        const email = user.email;
        const mobilePhone = user.mobilePhone;
        if (!email && !mobilePhone) {
            throw encapsulatedInstance.httpErrors.badRequest("Please provide email or phone number to search user.");
        }
        let filterQuery = '';
        if (email) {
            filterQuery = `identities/any(c:c/issuerAssignedId eq '${email}' and c/issuer eq '${issuerUrl}')`;
        }
        const emailUserMatch = await microsoftGraphClientInstance.api('/users').filter(filterQuery).get();
        if (emailUserMatch.value.length > 0) {
            return emailUserMatch.value[0] as User;
        }
        if (mobilePhone && encapsulatedInstance.validatePhoneNumber(mobilePhone)) {
            const sanitizedPhoneNumber = encapsulatedInstance.formatPhoneNumber(mobilePhone);
            filterQuery = `identities/any(c:c/issuerAssignedId eq '${encodeURIComponent(sanitizedPhoneNumber)}' and c/issuer eq '${issuerUrl}')`;
        }
        const mobilePhoneUserMatch = await microsoftGraphClientInstance.api('/users').filter(filterQuery).get();
        if (mobilePhoneUserMatch.value.length > 0) {
            return mobilePhoneUserMatch.value[0] as User;
        }
        return null;
    }

    encapsulatedInstance.decorate('createB2cPatient', async function (relicPatient: RelicPatient): Promise<string> {
        // We cannot create a B2C user unless the patient has phone or email or both.
        if (!relicPatient.email && !relicPatient.mobilePhone) {
            throw encapsulatedInstance.httpErrors.badRequest("Please provide email or phone number to create patient login.");
        }
        if (!relicPatient.organizationId) {
            throw encapsulatedInstance.httpErrors.badRequest("Please provide organization id to create patient login.");
        }
        // If the b2c user was previously created, we can return the b2cid.
        if (relicPatient.b2cid) {
            //Todo: Upsert logic needs to be added here.
            return relicPatient.b2cid;
        }
        // If we don't have a b2c id, we can still check for b2c User using UPN.
        // UPN is built using EHR provider & EHR patient id so it is constant + unique to each patient mastered in EHR.
        const patientId: string = relicPatient.id;
        const organization: RelicOrganization = await encapsulatedInstance.orgService.getOrganization(relicPatient.organizationId);
        let provider: string;
        if (organization?.pointClickCare?.id && organization?.pointClickCare?.id != '') {
            provider = 'pcc';
        } else {
            provider = 'medplum';
        }
        const userPrincipalName: string = `${provider}-${patientId}@${issuerUrl}`;
        try {
            const response = await microsoftGraphClientInstance.api(`/users/${userPrincipalName}`).get();
            if (response) {
                // Todo: Upsert logic needs to be added here.
                return response.id;
            }
        } catch (error) {
            encapsulatedInstance.log.info(`Users UPN does not exist in B2C, ${error.code}, ${error.message}`);
        }
        //Now we can go ahead and create the user in B2C since we know that B2C user does not exist.
        //We can still get into a rare condition where the same email address or phone is used for a different patient.
        //Todo: For now, we are ignoring this rare condition. However, it needs to be fixed.
        //Todo: More work is needed to sanitize the phone number. Needs to be a common utility function.
        let sanitizedPhoneNumber: string = '';
        if (relicPatient.mobilePhone && encapsulatedInstance.validatePhoneNumber(relicPatient.mobilePhone)) {
            sanitizedPhoneNumber = encapsulatedInstance.formatPhoneNumber(relicPatient.mobilePhone);
        }
        const mailNickname: string = userPrincipalName.split('@')[0];
        const displayName: string = relicPatient.name;
        const givenName: string = relicPatient.name.split(' ')[0] ? relicPatient.name.split(' ')[0] : '';
        const surname: string = relicPatient.name.split(' ')[1] ? relicPatient.name.split(' ')[1] : '';
        const userEmail: string = relicPatient.email;
        const userPhoneNumber: string = relicPatient.mobilePhone;
        const randomPassword = generateRandomPassword();
        const passwordProfile: PasswordProfile = {
            password: randomPassword,
            forceChangePasswordNextSignIn: false,
        };
        const identities: ObjectIdentity[] = [];
        //We can have either email or phone number as the sign-in identity of the user.
        //This email or phone number is where OTP will be received during sign-in process making it a passwordless login.
        if (userEmail) {
            identities.push({
                signInType: 'emailAddress',
                issuer: issuerUrl,
                issuerAssignedId: userEmail,
            });
        }
        if (userPhoneNumber) {
            identities.push({
                signInType: 'phoneNumber',
                issuer: issuerUrl,
                issuerAssignedId: sanitizedPhoneNumber,
            });
        }
        const b2cUser: User = {
            displayName: displayName,
            givenName: givenName,
            surname: surname,
            accountEnabled: true,
            userPrincipalName: userPrincipalName,
            mailNickname: mailNickname,
            mail: userEmail,
            mobilePhone: sanitizedPhoneNumber,
            identities: identities,
            passwordProfile: passwordProfile,
            passwordPolicies: 'DisablePasswordExpiration',
            companyName: organization.id,
            employeeType: 'Patient',
        };
        if (relicPatient?.streetAddress) {
            b2cUser.streetAddress = relicPatient.streetAddress;
        }
        if (relicPatient?.city) {
            b2cUser.city = relicPatient.city;
        }
        if (relicPatient?.state) {
            b2cUser.state = relicPatient.state;
        }
        const response = await microsoftGraphClientInstance.api('/users').post(b2cUser);
        return response?.id
    });

    encapsulatedInstance.decorate('deleteB2cPatient', async function (b2cid: string) {
        await microsoftGraphClientInstance.api(`/users/${b2cid}`).delete();
    });

    encapsulatedInstance.decorate('createMsGraphPractitioner', async function (relicPractitioner: RelicPractitioner, msGraphPractitioner: User): Promise<User> {
        const existingUser: User = await getMsGraphUser(relicPractitioner);
        if (existingUser) {
            throw encapsulatedInstance.httpErrors.conflict('An account with this email or mobile phone already exists. Contact support for assistance.');
        }
        const graphMicrosoftUrl: string = config.get('AZURE.AADB2C.GRAPH_MICROSOFT_URL');
        const userPrincipalName: string = generatePractitionerUPN(relicPractitioner);
        msGraphPractitioner.userPrincipalName = userPrincipalName;
        const mailNickname: string = userPrincipalName.split('@')[0];
        msGraphPractitioner.mailNickname = mailNickname;
        if (!msGraphPractitioner.displayName) {
            msGraphPractitioner.displayName = relicPractitioner.name || relicPractitioner.email;
        }
        msGraphPractitioner.accountEnabled = true;
        const randomPassword = generateRandomPassword();
        const passwordProfile: PasswordProfile = {
          password: randomPassword,
          forceChangePasswordNextSignIn: false,
        };
        const identities: ObjectIdentity[] = [{
          signInType: 'emailAddress',
          issuer: issuerUrl,
          issuerAssignedId: msGraphPractitioner.mail,
        }];
        if (msGraphPractitioner.mobilePhone) {
            identities.push({
                signInType: 'phoneNumber',
                issuer: issuerUrl,
                issuerAssignedId: msGraphPractitioner.mobilePhone,
            });
        }
        msGraphPractitioner.identities = identities;
        msGraphPractitioner.passwordProfile = passwordProfile;
        msGraphPractitioner.passwordPolicies = 'DisablePasswordExpiration';
        msGraphPractitioner.companyName = relicPractitioner.organizationId;
        msGraphPractitioner.employeeType = 'Practitioner';
        const createdMsGraphPractitioner = await microsoftGraphClientInstance.api('/users').post(msGraphPractitioner) as User;
        // Add this practitioner to the practitioner group. That is how we are identifying practitioners in B2C.
        const practitionerGroupId = config.get('AZURE.AADB2C.PRACTITIONER_GROUP_ID');
        await microsoftGraphClientInstance.api(`/groups/${practitionerGroupId}/members/$ref`).post({
          "@odata.id": `${graphMicrosoftUrl}/v1.0/directoryObjects/${createdMsGraphPractitioner.id}`
        });
    
        return createdMsGraphPractitioner;
    });
      
    encapsulatedInstance.decorate('updateMsGraphPractitioner', async function (relicPractitioner: RelicPractitioner, msGraphPractitioner: User) {
        const userPrincipalName: string = generatePractitionerUPN(relicPractitioner);
        const existingUser: User = await getMsGraphUser(relicPractitioner);
        const userIdentities = [];
        if (existingUser && existingUser.userPrincipalName !== userPrincipalName) {
            throw encapsulatedInstance.httpErrors.conflict('An account with this email or mobile phone already exists. Contact support for assistance.');
        }
        if (msGraphPractitioner.mail) {
            userIdentities.push({
                signInType: 'emailAddress',
                issuer: issuerUrl,
                issuerAssignedId: msGraphPractitioner.mail,
            });
        }
        if (msGraphPractitioner.mobilePhone) {
            userIdentities.push({
                signInType: 'phoneNumber',
                issuer: issuerUrl,
                issuerAssignedId: msGraphPractitioner.mobilePhone,
            });
        }
        msGraphPractitioner.identities = userIdentities;
        const practitionerToBeUpdated: User = {
            ...existingUser,
            ...msGraphPractitioner,
        };
        await microsoftGraphClientInstance.api(`/users/${userPrincipalName}`).update(practitionerToBeUpdated);
    });
      
    encapsulatedInstance.decorate('deleteMsGraphPractitioner', async function (relicPractitioner: RelicPractitioner) {
        const userPrincipalName: string = generatePractitionerUPN(relicPractitioner);

        await microsoftGraphClientInstance.api(`/users/${userPrincipalName}`).delete();
    });    
}

export default azurePlugin;