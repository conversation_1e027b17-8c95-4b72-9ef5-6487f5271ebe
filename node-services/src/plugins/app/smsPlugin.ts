'use strict';

import { FastifyInstance, FastifyPluginAsync, FastifyPluginOptions } from 'fastify';
import { SmsClient } from '@azure/communication-sms';
import config from 'config';
import { SmsNotificationOptions, WelcomeUserNotificationOptions } from '../../types/notification';
import Handlebars from 'handlebars';
import { convert } from 'html-to-text';
import libphone, { PhoneNumberFormat } from 'google-libphonenumber';

declare module 'fastify' {
    interface FastifyInstance {
        validatePhoneNumber: (number: string) => boolean;
        formatPhoneNumber: (number: string, format?: PhoneNumberFormat) => string;
        sendSms: (options: SmsNotificationOptions) => Promise<void>;
        sendWelcomeSms: (smsOptions: WelcomeUserNotificationOptions) => Promise<void>;
    }
}

const smsPlugin: FastifyPluginAsync = async(fastify: FastifyInstance, options:FastifyPluginOptions) =>{
    const phoneUtil = libphone.PhoneNumberUtil.getInstance();
    const PNF = libphone.PhoneNumberFormat;

    fastify.decorate('validatePhoneNumber', (number: string): boolean => {
        try {
            if (number === '+1' || number === '1' || number === '') {
                return false;
            }
            const parsedNumber = phoneUtil.parse(number, 'US');
            return phoneUtil.isValidNumberForRegion(parsedNumber, 'US');
        } catch (e) {
            throw Error(`Error validating phone number ${number}: ${e.message}`);
        }
    });

    fastify.decorate('formatPhoneNumber', (number: string, format: PhoneNumberFormat = PNF.E164): string => {
        try {
            if (number === '+1' || number === '1' || number === '') {
                return '';
            }
            const parsedNumber = phoneUtil.parse(number, 'US');
            return phoneUtil.format(parsedNumber, format);
        } catch (e) {
            throw Error(`Error formatting phone number ${number}: ${e.message}`);
        }
    });

    fastify.decorate('sendSms', async (options: SmsNotificationOptions) => {
        try {
            if (!options.recipient || !fastify.validatePhoneNumber(options.recipient)) {
                throw new Error("Invalid recipient phone number.");
            }
            const fromNumber: string = fastify.formatPhoneNumber(config.get('ACS.PHONENUMBER'));
            const toNumber: string = fastify.formatPhoneNumber(options.recipient);
            const connectionString: string = `endpoint=${config.get('ACS.ENDPOINT')};accesskey=${config.get('ACS.ACCESS_KEY')}`;
            const client = new SmsClient(connectionString, { retryOptions: { maxRetries: config.get("ACS.SMS_MAX_RETRY") ?? 3 } });

            const sendResults = await client.send({
                from: fromNumber,
                to: [toNumber],
                message: options.message
            });

            for (const sendResult of sendResults) {
                if (!sendResult.successful) {
                    fastify.log.error("Failed to send message:", sendResult);
                } else {
                    fastify.log.info("Message sent successfully:", sendResult);
                }
            }
        } catch (error) {
            fastify.log.error(error, 'Error in sendSms function');
            throw new Error("Failed to send SMS. Please check the logs for details.");
        }
    });

    fastify.decorate('sendWelcomeSms', async(smsOptions:WelcomeUserNotificationOptions) => {
        if(smsOptions.mobileNumber === ''){
            return;
        }
        const template = Handlebars.compile(smsOptions.template);
        const message:string = convert(template(smsOptions.data));
        const options:SmsNotificationOptions = {
            recipient: smsOptions.mobileNumber,
            message: message,
            tag: "onboarding",
            enableDeliveryReport: true
        }
        await fastify.sendSms(options);
    });
}

export default smsPlugin;