import { Condition, Patient, Related<PERSON>erson, Observation } from '@medplum/fhirtypes';
import {  ObfuscationMap, PatientEverything, RelicCondition, RelicLink, RelicPatient } from "relic-ui";
import { conditionToRelicCondition, getPatientSummary, patientToRelicPatient, relatedPersonToRelicRelatedPerson, relicLinkToRelatedPerson, relicPatientToFhirPatient } from "../types/relicPatientConversion";
import { RelicOrganization } from "relic-ui";
import { IUserIdentity } from "relic-ui";


/**
 * Transforms the response payload into a structure compatible with the RelicPatient format.
 *
 * @param {any} request - The request object.
 * @param {any} reply - The reply object.
 * @param {any} payload - The original response payload to be transformed. Can be an array or a single object.
 * @returns {Promise<RelicPatient | RelicPatient[]>} A promise that resolves to a transformed payload, either a single RelicPatient object or an array of them.
 * @throws {<PERSON>rror} Throws an error if any of the asynchronous operations (like fetching obfuscation maps) fail.
 *
 */
async function transformResponse(request: any, reply: any, payload: any): Promise<RelicPatient | RelicPatient[]> {

    const { obfuscationApi } = request.server;
    const { organization } = request;
    if (Array.isArray(payload)) {

        // const relicPatients: RelicPatient[] = payload.map((payloadItem) => {
        //     const relicPatient: RelicPatient = patientToRelicPatient(payloadItem as PatientEverything, organization);
        //     if(!relicPatient){
        //         return null;
        //     }
        //     relicPatient.Condition = transformCondition(payloadItem?.Conditions || []);
        //     relicPatient.link = transformRelatedPerson(payloadItem.RelatedPersons || []);
        //     relicPatient.b2cid = payloadItem.b2cid;
        //     return relicPatient;
        // }).filter(element => {
        //     return element !== null;
        // }
        // )
        // return relicPatients;
        return payload;
    } else {
        const relicPatient: RelicPatient = patientToRelicPatient(payload as PatientEverything, organization);
        // if there is a change in email during update emailVerified set to false
        relicPatient.emailVerified = payload.emailVerified;
        relicPatient.Condition = transformCondition(payload?.Conditions || []);
        relicPatient.link = transformRelatedPerson(payload?.RelatedPersons || []);
        relicPatient.obfuscationMap = payload.obfuscationMap ? payload.obfuscationMap : null;
        relicPatient.height = getValue(payload.Observations,'363808006');
        relicPatient.weight = getValue(payload.Observations,'363808001');
        relicPatient.summary = getPatientSummary(organization, relicPatient);
        let obfuscationMap: ObfuscationMap = await obfuscationApi.Get(relicPatient);
        relicPatient.obfuscationMap = obfuscationMap;
        relicPatient.b2cid = payload.b2cid;
        return relicPatient;

    }

}

/**
 * Transforms the request object by adding additional data.
 *
 * @param {any} request - The request object containing the original request data.
 * @returns {Promise<void>} A promise that resolves when the request object has been successfully transformed and augmented.
 * @throws {Error} Throws an error if the transformation process or asynchronous operations (like fetching obfuscation maps) fail.
 *
 */
async function transformRequest(request: any): Promise<void> {
    if (request.body) {
        const relicPatient = JSON.parse(JSON.stringify(request.body)) as RelicPatient; // copy to avoid circular reference issue
        request.body.relicPatient = relicPatient;
        let organization: RelicOrganization = request.organization;
        // Change request organization in case an admin is sending a patient request.
        if((request.requestContext.get('whoami') as IUserIdentity)?.role?.name == 'admin'){
            if (request.organization.id != request.body.relicPatient.organizationId) {
                organization = await request.server.orgService.getOrganization(request.body.relicPatient.organizationId);
            }
        }
        let { link: links } = request.body;
        const fhirPatient: Patient = relicPatientToFhirPatient(request.body.relicPatient, organization);
        request.body.patientData = fhirPatient;
        request.log.info('Tranformation complete for Patient')

        if (links && links.length > 0) {
            const relatedPersonData: RelatedPerson[] = await Promise.all(links.map(async (relicLink: RelicLink) => {
                const relatedPerson: RelatedPerson = relicLinkToRelatedPerson(relicLink, '<patientId>');

                return relatedPerson;
            }));
            request.log.info('Tranformation complete for RelatedPerson');
            request.body.relatedPersonData = relatedPersonData;
        }

        const obfuscationApi = request.server.obfuscationApi;

        let { obfuscationMap, cleanMap } = await obfuscationApi.Create(request.body);
        request.body.obfuscationMap = obfuscationMap
        request.body.cleanMap = cleanMap;
    }
}

/**
 * Transforms an array of RelatedPerson objects into an array of RelicLink objects.
 * Each RelatedPerson object in the input array is converted into a corresponding RelicLink object.
 *
 * @param {RelatedPerson[]} link - An array of RelatedPerson objects.
 * @returns {RelicLink[]} An array of transformed RelicLink objects.
 */
function transformRelatedPerson(link: RelatedPerson[]): RelicLink[] | [] {
    let relatedPerson:RelicLink[] | [];
    if(link[0] != null){
        relatedPerson =  (link).map(link => relatedPersonToRelicRelatedPerson(link));
    }else{
        relatedPerson = [];
    }
    return  relatedPerson;
}

/**
 * Retrieves the value and unit of the first observation matching a given code.
 * 
 * This function searches through an array of Observation objects to find the first
 * observation that matches the specified code. It then extracts and returns the
 * value and unit of this observation concatenated as a string. If the observation
 * or the valueQuantity is not found, it returns an empty string.
 *
 * @param {Observation[]} observations - An array of Observation objects to search through.
 * @param {string} code - The code of the observation to find.
 * @returns {string} The value and unit of the matching observation, concatenated as a string,
 *                   or an empty string if the observation or valueQuantity is not found.
 * 
 */
function getValue(observations: Observation[], code: string): string{
    const observation = observations?.filter(observation => observation.code?.coding?.[0]?.code == code)
    const valueQuantity = observation?.[0]?.valueQuantity;
    return valueQuantity?.value + valueQuantity?.unit;
  }

/**
 * Transforms an array of Condition objects into an array of RelicCondition objects.
 * Each Condition object in the input array is converted into a corresponding RelicCondition object.
 *
 * @param {Condition[]} conditions - An array of Condition objects.
 * @returns {RelicCondition[]} An array of transformed RelicCondition objects.
 */
function transformCondition(conditions: Condition[]): RelicCondition[] {
    return (conditions).map(condition => conditionToRelicCondition(condition));
}


export { transformRequest, transformResponse }