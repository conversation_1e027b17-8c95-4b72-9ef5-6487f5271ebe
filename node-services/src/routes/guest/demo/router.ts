import { FastifyInstance, FastifyReply } from "fastify";
import config from "config";
import { PatientEverything, RelicPatient } from "relic-ui";
import { transformRequest, transformResponse } from "../../../utils/patient";
import { IPortalIdentity, IUserIdentity } from "relic-ui";
import { Thread } from "relic-ui";
import { CommunicationIdentity } from "relic-ui";
import { RelicOrganization } from "relic-ui";
import { MedplumService } from "../../../services/ehr.medplum";

export default function (instance: FastifyInstance, options: any, next: any) {
    // instance.register(require('../../plugins/patientPlugin'), { dbName: 'demoDb' });

    async function onCreateThreadRequestHook(req: any, rep: FastifyReply) {
        //1. obtain the patient details from database
        const patientId = req.body.id;
        let patient: RelicPatient = await req.server.relicPatients().findOne({ id: patientId });
        if (!patient) {
            return rep.status(404).send({ message: `Patient not found for patient id: ${patientId}` });
        }

        const portalIdentity: IPortalIdentity = {
            email: patient.obfuscationMap.email.value ?? '',
            name: patient.communicationIdentities[0].displayName,
            companyId: '',
            clientId: '',
            organizationId: patient.organizationId,
        };

        const acsIdentity: CommunicationIdentity = {
            userId: patient.communicationIdentities[0].userId,
            displayName: patient.communicationIdentities[0].displayName,
            service: patient.communicationIdentities[0].service,
            endpoint: patient.communicationIdentities[0].endpoint,
            secret: {
                token: patient.communicationIdentities[0].secret.token,
                expiresOn: patient.communicationIdentities[0].secret.expiresOn
            },
            threads: [],
        };

        // 3. Create a whoami using patient details
        const whoami: IUserIdentity = {
            id: patient.id,
            resourceType: 'Patient',
            email: patient.email,
            portalIdentity: portalIdentity,
            role: {
                name: 'admin'
            },
            communicationIdentities: [acsIdentity],
            provider: 'medplum',
        };

        req.requestContext.set('whoami' as never, whoami as never);
    }

    instance.get('/organization/patients',
        {
            schema: {
                tags: ['Demo'],
                description: 'Get all patients for an Organization / Facility',
                summary: 'Get all patients for an Organization / Facility',
                querystring: {
                    type: 'object',
                    properties: {
                        _sort: {
                            type: 'string',
                            default: 'name',
                            description: 'Sample value: name, birthDate, gender, email, link, organizationId, active. Where to get: it is the value of patient table column name'
                        },
                        _order: {
                            type: 'string',
                            enum: ['asc', 'desc'],
                            default: 'asc'
                        },
                        _start: {
                            type: 'number',
                            default: 0,
                            description: 'The starting index for pagination'
                        },
                        _end: {
                            type: 'number',
                            default: 25,
                            description: 'The ending index for pagination'
                        },
                        active: {
                            type: 'boolean',
                            default: true,
                            description: 'Filter by active status'
                        },
                        _search: {
                            type: 'string',
                            maxLength: 255,
                            description: 'Search by name, email, phone'
                        }
                    },
                }
            }
        },
        async function (req: any, rep: any) {
            try {
                const relicOrg = await req.server.orgService.getRelicCareOrganization()
                req.server.attachServerMedplumAccessToken();
                const ehr = new MedplumService(instance, options);
                req.query.organizationId = relicOrg?.id;
                const {total, data} = await ehr.searchPatients(req.query);
                if (!data || total === 0) {
                    return rep.status(404).send({ message: `No patients found for the organizationId : ${relicOrg?.id}` });
                }
                rep.header('x-total-count', total);
                return data;
            } catch (error) {
                instance.log.error(`Error fetching patients: ${error.message}`);
                return rep.status(500).send({ error: 'An error occurred while fetching patients' });
            }
        }
    );

    instance.get('/patients',
        {
            schema: {
                tags: ['Demo'],
                description: 'Get patient details',
                summary: 'Get patient details',
                querystring: {
                    type: 'object',
                    properties: {
                        email: { type: 'string', description: 'patient\'s email' },
                        mobilePhone: { type: 'string', description: 'patient\'s mobile phone' },
                    },
                },
            },
        },
        async function (req: any, rep: any) {
            try {
                const { email, mobilePhone } = req.query
                // Check if the patient exists in the database - search by phone or email
                let relicPatient: RelicPatient;
                const demoOrganization = this.requestContext.get('organization' as never) as RelicOrganization;
                if (mobilePhone) {
                    const validatedMobilePhone = req.server.validatePhoneNumber(mobilePhone) ? req.server.FormatPhoneNumber(mobilePhone) : '';
                    relicPatient = await req.server.relicPatients().findOne(
                        {
                            organizationId: demoOrganization.id,
                            mobilePhone: validatedMobilePhone,
                        }
                    );
                }
                if (email && !relicPatient) {
                    relicPatient = await req.server.relicPatients().findOne(
                        { 
                            organizationId: demoOrganization.id,
                            email: email 
                        }
                    );
                }
                if (relicPatient) {
                    const patientId: string = relicPatient.id
                    const patientEverything: PatientEverything = await req.server.fetchPatientById(patientId)
                    req.organization = await req.server.orgService.getRelicCareOrganization();
                    return await transformResponse(req, rep, patientEverything)
                } else {
                    console.log('No patient found in the database');
                    return rep.status(204).send({ message: 'No patients found for the specified query.' })
                }
            } catch (error) {
                instance.log.error(`Error fetching patients: ${error.message}`)
                return rep.status(500).send({ error: 'An error occurred while fetching patients' })
            }
        }
    );

    instance.post('/patients',
        {
            schema: {
                tags: ['Demo'],
                description: 'There is no guarantee of patient login creation. Check for b2cid field in created patient object to verify if the patient login is created.',
                summary: 'Create a demo record. No failures will occur if the patient already exists in the database.',
                body: {
                    type: 'object',
                    properties: {
                        sourcePatientId: { type: 'string', description: 'Existing patient ID' },
                        name: { type: 'string', description: 'Patient\'s name' },
                        email: { type: 'string', description: 'Patient\'s email' },
                        mobilePhone: { type: 'string', description: 'Patient\'s mobile phone number' },
                        facilityName: { type: 'string', description: 'Facility Name' },
                        facilityType: { type: 'string', description: 'Facility type' },
                        state: { type: 'string', description: 'all US States' },
                        primaryLanguage: {
                            type: 'array',
                            description: 'Patient\'s communication language preferences',
                            items: {
                                type: 'object',
                                properties: {
                                    system: { type: 'string', description: 'System identifier (e.g., HL7 ValueSet URL)' },
                                    code: { type: 'string', description: 'Language code (e.g., en-US)' },
                                    display: { type: 'string', description: 'Language display name' },
                                    preferred: { type: 'boolean', description: 'Indicates if this is the preferred language' }
                                },
                                required: ['system', 'code', 'display']
                            }
                        }
                    },
                    required: ['sourcePatientId'],
                },
            },
        },
        async function (req: any, rep: any) {
            const { sourcePatientId, name, email, mobilePhone, facilityName, facilityType, state, primaryLanguage } = req.body;

            req.organization = await req.server.orgService.getOrganization(config.get('RELICCARE.ORG_ID'));
            const demoOrganization = req.organization as RelicOrganization;
            // Fetch the existing patient details based on sourcePatientId
            let sourcePatientEverything: PatientEverything = await req.server.fetchPatientById(sourcePatientId);
            if (!sourcePatientEverything) {
                // Get the first patient in Relic Care Organization
                const sourcePatient: RelicPatient = await req.server.relicPatients().findOne({ organizationId: demoOrganization.id });
                sourcePatientEverything = await req.server.fetchPatientById(sourcePatient.id);
            }
            let newPatient: RelicPatient = {
                id: null,
                resourceType: 'Patient',
                organizationId: demoOrganization.id,
            };
            //Check if the patient already exists in the database - search by phone or email
            let validatedMobilePhone:string;
            if (mobilePhone) {
                validatedMobilePhone = req.server.validatePhoneNumber(mobilePhone) ? req.server.FormatPhoneNumber(mobilePhone) : '';
                newPatient = await req.server.relicPatients().findOne(
                    {
                        organizationId: demoOrganization.id,
                        mobilePhone: validatedMobilePhone,
                    }
                );
            }
            if (email && !newPatient) {
                newPatient = await req.server.relicPatients().findOne(
                    { 
                        organizationId: demoOrganization.id,
                        email: email 
                    }
                );
            }
            if (!newPatient) {
                newPatient = {
                    ...newPatient,
                    resourceType: 'Patient',
                    organizationId: config.get('RELICCARE.ORG_ID'),
                    name: name,
                    birthDate: sourcePatientEverything?.Patient?.birthDate,
                    gender: sourcePatientEverything?.Patient?.gender,
                    maritalStatus: sourcePatientEverything.Patient.maritalStatus.coding[0].display,
                    email: email,
                    mobilePhone: validatedMobilePhone,
                    homePhone: validatedMobilePhone,
                    active: sourcePatientEverything?.Patient?.active,
                    streetAddress: facilityName,
                    city: facilityType,
                    state: state,
                    primaryLanguage: primaryLanguage ? primaryLanguage[0] : []
                };
            }
            req.body = newPatient;
            req.organization = demoOrganization;
            await transformRequest(req);
            const patientAllDetails: PatientEverything = await req.server.createPatient(req);
            return await transformResponse(req, rep, patientAllDetails);
        }
    );

    instance.post('/patients/chat/threads',
        {
            schema: {
                tags: ['Demo'],
                description: 'Create Thread, if type is default then it will create default thread, if type is caregiver then it will create caregiver thread.',
                summary: 'Create threads',
                body: {
                    type: 'object',
                    properties: {
                        participants: {
                            type: 'array',
                            description: 'List of participants',
                            items: {
                                type: 'object',
                                properties: {
                                    id: {
                                        type: 'object',
                                        properties: {
                                            communicationUserId: { type: 'string', description: 'Communication user ID' }
                                        },
                                    },
                                    resourceId: { type: 'string', description: 'Resource ID' },
                                    resourceType: { type: 'string', description: 'Type of resource' },
                                    displayName: { type: 'string', description: 'Participant display name' }
                                },
                            }
                        },
                        id: { type: 'string', description: 'Patient id' },
                        type: { type: 'string', description: 'Type of thread (e.g., Default_Practitioner)' },
                        inviteVia: { type: 'string', description: 'Method of invitation (e.g., none)' },
                        receiverEmail: { type: 'string', description: 'Receiver email address' },
                        receiverPhone: { type: 'string', description: 'Receiver phone number' },
                        topic: { type: 'string', description: 'Thread topic' },
                        subject: {
                            type: 'object',
                            properties: {
                                organizationId: { type: 'string', description: 'Organization ID' },
                                type: { type: 'string', description: 'Subject type (e.g., Default)' },
                                questionnaireId: { type: 'string', description: 'Questionnaire ID' },
                                ThreadOwner: {
                                    type: 'object',
                                    properties: {
                                        resourceType: { type: 'string', description: 'Resource type' },
                                        id: { type: 'string', description: 'Owner ID' }
                                    },
                                },
                                targetPhoneNumber: { type: 'string', description: 'Target phone number' },
                                patientLanguage: {
                                    type: 'object',
                                    description: 'Patient language',
                                    properties: {
                                        system: { type: 'string', description: 'System identifier (e.g., HL7 ValueSet URL)' },
                                        code: { type: 'string', description: 'Language code (e.g., en-US)' },
                                        display: { type: 'string', description: 'Language display name' },
                                        preferred: { type: 'boolean', description: 'Indicates if this is the preferred language' }
                                    }
                                },
                                calleeNumber: {
                                    type: 'object',
                                    properties: {
                                        rawId: { type: 'string', description: 'Raw ID of callee' },
                                        phoneNumber: { type: 'string', description: 'Callee phone number' }
                                    },
                                },
                                callerNumber: {
                                    type: 'object',
                                    properties: {
                                        rawId: { type: 'string', description: 'Raw ID of caller' },
                                        phoneNumber: { type: 'string', description: 'Caller phone number' }
                                    },
                                }
                            },
                        }
                    },
                }
            },
            preHandler: onCreateThreadRequestHook,
        },
        async function createThread(request: any, reply: any) {
            request.organization = await request.server.orgService.getRelicCareOrganization();
            let { inviteVia, subject, participants } = request.body;
            const thread: Thread = {
                endpoint: '',
                threadId: '',
                threadTopic: '',
                threadSubject: subject,
                participants: participants,
                status: 'active',
                inviteVia: inviteVia,
                createDate: new Date(),
            };
            return await request.server.createThreadV2( thread );
        }
    );

    next();
}