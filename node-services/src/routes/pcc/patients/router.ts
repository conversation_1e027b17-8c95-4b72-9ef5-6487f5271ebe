import { S } from 'fluent-json-schema';
import { RelicPatient } from 'relic-ui';
import { Thread, ThreadsWithCount, RelicChatParticipant } from 'relic-ui';
import { IUserIdentity } from 'relic-ui';
import { FastifyInstance, FastifyPluginOptions, FastifyReply, FastifyRequest } from 'fastify';

export default async function pccPatientsRouter(fastify: FastifyInstance, options: FastifyPluginOptions) {
  fastify.get(
    '/',
    {
      schema: {
        ...options.schema,
        query: S.object()
          .prop('_sort', S.string().enum(['name', 'birthDate', 'location', 'primaryLanguage']).default('name'))
          .prop('_order', S.string().enum(['asc', 'desc']).default('asc'))
          .prop('_start', S.number().default(0))
          .prop('_end', S.number().default(25))
          .prop(
            'patientStatus',
            <PERSON>.enum(['new', 'current', 'discharged', 'New', 'Current', 'Discharged']).default('current'),
          )
          .prop('_search', S.string().maxLength(255).description('Search by patient name')),
        description: 'List of patients for the logged in User in selected Organization / Facility',
        summary: 'List of patients for the logged in User in selected Organization / Facility',
      },
    },
    async function (req: FastifyRequest, rep: FastifyReply) {
      const { total: count, data: relicPatients } = await fastify.providerService.searchPatients(req.query);
      rep.headers['x-total-count'] = count;
      return relicPatients;
    },
  );

  fastify.get(
    '/:id',
    {
      schema: {
        ...options.schema,
        params: S.object().prop(
          'id',
          S.string()
            .examples(['d1d4b57d-5c8b-423a-9791-e4007b2e7eb3'])
            .description('Where to get: id field present in patients collection')
            .minLength(1),
        ),
        description: 'Get a patient for an Organization / Facility',
        summary: 'Get a patient for an Organization / Facility',
      },
    },
    async function (req: FastifyRequest<{ Params: { id: string } }>, rep: FastifyReply) {
      let relicPatient: RelicPatient = await fastify.providerService.getPatientById(req.params.id);
      if (!relicPatient) {
        rep.status(404);
        return { message: `Patient with id ${req.params.id} not found` };
      }
      return relicPatient;
    },
  );

  fastify.get(
    '/:id/conditions',
    {
      schema: {
        ...options.schema,
        query: S.object().prop('_start', S.number().default(0)).prop('_end', S.number().default(25)),
        description: 'returns patient condition based on patient id',
        summary: 'Get patient condition',
      },
    },
    async function (req: FastifyRequest<{ Params: { id: string } }>, rep: FastifyReply) {
      const { total, data } = await fastify.providerService.getPatientConditions(req.params.id, req.query);
      rep.headers['x-total-count'] = total;
      return data;
    },
  );

  fastify.get(
    '/:id/chat',
    {
      schema: {
        ...options.schema,
        params: S.object().prop(
          'id',
          S.string()
            .minLength(1)
            .required()
            .description(
              'id can be resource id or ACS id\nSample value: 8:acs:8ca4fbc2-e63c-4283-a5b7-feb83074370e_0000001b-57e2-3b1a-28f4-343a0d000dc\nWhere to get: acsId field present in globalAgents collection',
            ),
        ),
        summary: `Get Patient's default thread or a thread with Patient participant depending on the caller's resource type.`,
        description: `Returns Patient's Default Chat thread if caller is Patient. Returns a Chat thread between Practitioner and Patient if caller is Practitioner`,
      },
    },
    async function getPccPatientThread(req: FastifyRequest<{ Params: { id: string } }>, rep: FastifyReply) {
      const me: IUserIdentity = req.requestContext.get('whoami');
      const filter: any = {};
      const participants: RelicChatParticipant[] = [];
      //Filter for active threads and return the latest updated thread
      filter.status = 'active';
      filter.sort = 'updateDate';
      filter.order = 'desc';
      participants.push({
        resourceId: me.id,
        resourceType: me.resourceType === 'Patient' ? 'Patient' : 'Practitioner', //This will typically be a practitioner
        id: undefined, // ParticipantACS id will be patched by Communication Plugin
      });
      participants.push({
        resourceId: req.params.id,
        resourceType: 'Patient', //Param is a patient since this is a patient route
        id: undefined, // ParticipantACS id will be patched by Communication Plugin
      });
      filter.relicChatParticipants = participants;
      const threadsWithCount: ThreadsWithCount = await fastify.getUserThreads(filter);
      const threads: Thread[] = threadsWithCount.threads;
      const thread: Thread = threads?.[0];
      if (thread) {
        return thread;
      }
      //Add patient name, language & phone to the participant request while creating new thread.
      const pccPatient: RelicPatient = await fastify.providerService.getPatientById(req.params.id);
      participants.find((p) => p.resourceType === 'Patient').displayName = pccPatient.name;
      participants.find((p) => p.resourceType === 'Patient').chatLanguage = pccPatient.primaryLanguage;
      participants.find((p) => p.resourceType === 'Patient').mobilePhone = pccPatient.mobilePhone;
      const newThread: Thread = {
        endpoint: '',
        threadId: '',
        threadTopic: '',
        threadSubject: {
          organizationId: me.portalIdentity.organizationId,
        },
        participants: participants,
        status: 'active',
        inviteVia: 'none',
        createDate: new Date(),
      };
      console.log('Creating new thread for patient', pccPatient.name, 'with participants', participants);
      return await fastify.createThreadV2(newThread);
    },
  );

  fastify.get(
    '/:id/identities',
    {
      schema: {
        ...options.schema,
        params: S.object().prop('id', S.string().minLength(1).required().description("patient's resource id")),
        summary: `Get Patient's communication identities`,
        tags: ['Communication Service'],
        description: `Returns Patient's communication identities`,
      },
    },
    async function (req: FastifyRequest, rep: FastifyReply) {
      const { id } = req.params as { id: string };
      const relicPatient: RelicPatient = await req.server.providerService.getPatientById(id);
      const communicationIdentities = relicPatient.communicationIdentities;
      for (let identity of communicationIdentities) {
        const updatedIdentity = await fastify.acs.refreshIdentity(relicPatient, identity);
        identity.secret = updatedIdentity.secret;
      }
      return communicationIdentities;
    },
  );
}
