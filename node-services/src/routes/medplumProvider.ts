import path from 'path';
import config from 'config';
import proxy from '@fastify/http-proxy';
import { FastifyHttpProxyOptions } from '@fastify/http-proxy';
import fastifyAutoload from '@fastify/autoload';
import { FastifyPluginAsync, FastifyInstance, FastifyPluginOptions, FastifyRequest, FastifyReply } from 'fastify';
import { MedplumService } from '../services/ehr.medplum';
import fastifyJwt, { FastifyJWTOptions, TokenOrHeader, SignPayloadType } from '@fastify/jwt';
import buildGetJwks from 'get-jwks';
import { EhrServiceInterface } from 'src/services/interface';
import { IUser } from 'relic-ui';
import { relicRequire } from '../utils/common-js';

const getJwks = buildGetJwks();

declare module 'fastify' {
  export interface FastifyInstance {
    providerService: EhrServiceInterface;
    verifyJwt(req: FastifyRequest, rep: FastifyReply): Promise<void>;
  }
}

declare module '@fastify/jwt' {
  interface FastifyJWT {
    userType: IUser;
  }
}

// Schema definition for RelicAgentQuery. Can be imported from 'relic-ui'.
const { RelicHeaderSchema } = relicRequire('relic-ui/schema');

const medplumProvider: FastifyPluginAsync = async (fastify: FastifyInstance, opts: FastifyPluginOptions) => {
  // providerService decorator for MedplumService
  if (!fastify.hasDecorator('providerService')) {
    fastify.decorate('providerService', new MedplumService(fastify, opts));
  }
  fastify.log.info('providerService(Medplum) decorator successful.');

  // jwt configuration for Medplum
  const medplumJwtConfig: FastifyJWTOptions = {
    namespace: 'medplum',
    jwtVerify: 'jwtVerify',
    decode: {
      complete: true,
    },
    verify: {
      algorithms: ['RS256'],
      complete: true,
      allowedIss: ['https://api.medplum.com/'],
      extractToken: function (req: FastifyRequest): string {
        return req.headers['x-access-token'] as string;
      },
    },
    secret: async function (req: FastifyRequest, token: TokenOrHeader): Promise<string> {
      const {
        header: { kid, alg },
        payload: { iss },
      } = token;
      return await getJwks.getPublicKey({ kid, domain: iss, alg });
    },
    // Refer https://github.com/fastify/fastify-jwt#formatuser for more details
    formatUser: async function (tokenPayload: SignPayloadType): Promise<IUser> {
      // Obtain User Identity using Jwt Token
      const relicAiUser = await fastify.providerService.whoAmI(tokenPayload);
      return relicAiUser;
    },
  };

  // jwt registration for Medplum
  fastify.register(fastifyJwt, medplumJwtConfig);

  /**
   * Verifies the Medplum JWT token from the incoming request, constructs the user identity,
   * and sets relevant authentication and user context values into the request context.
   *
   * This function is intended to be used as an authentication hook in Fastify.
   * 
   * For medplum, organizationId is set through jwt verification based on practitioner's role & associated org in Medplum.
   * @param req - The Fastify request object.
   * @param rep - The Fastify reply object.
   * @returns A Promise that resolves when verification and context setup are complete.
   * @throws Will throw if JWT verification fails.
   */
  async function verifyJwt(req: FastifyRequest, rep: FastifyReply): Promise<void> {
    req.requestContext.set('accessToken', req.headers['x-access-token'] as string);
    const relicAiUser = (await req.jwtVerify()) as IUser;
    req.requestContext.set('user', relicAiUser);
    req.requestContext.set('whoami', relicAiUser.userIdentity);
    req.requestContext.set('organizationId', relicAiUser.organizationId);
    //TODO: Legacy needs these values available in requestcontext. To be fixed by using req.user value
    req.requestContext.set('decodedJwtToken', relicAiUser.decodedJwtToken);
    await fastify.setMyOrganization(relicAiUser.organizationId);
  }

  async function verifyAccess(req: FastifyRequest, rep: FastifyReply) {
    return true; // Placeholder for access verification logic. TBD: To be fixed later.
  }
  
  // Authentication & Authorization hooks
  fastify.addHook('onRequest', fastify.auth([verifyJwt, verifyAccess], { relation: 'and' }));

  fastify.log.info('Medplum Routes loading...');

  //Publishing Medplum Routes
  await fastify.register(fastifyAutoload, {
    dir: path.join(__dirname, './api'),
    options: {
      ...opts,
      prefix: '/api/medplum',
      schema: {
        headers: RelicHeaderSchema
      }
    },
    autoHooks: true,
    cascadeHooks: true,
    routeParams: true,
    ignoreFilter: (filePath: string) => {
      return filePath.includes('register');
    },
  });

  // Medplum Specific Services exposed via Medplum Routes
  await fastify.register(fastifyAutoload, {
    dir: path.join(__dirname, './medplum'),
    options: {
      ...opts,
      prefix: '/api/medplum',
    },
    autoHooks: true,
    cascadeHooks: true,
    routeParams: true,
  });

  // separate instance for Flowise proxy. Documented in src/plugins/external/swagger.ts > transform function
  fastify.register(proxy, {
      upstream: config.get('FLOWISE.API_URL'),
      prefix: '/api/medplum/flowise',
      httpMethods: ['GET'],
      replyOptions: {
          rewriteRequestHeaders: (originalReq: any, headers: any) => ({
              Authorization: `Bearer ${config.get('FLOWISE.API_KEY')}`,
              'Content-Type': 'application/json'
          })
      }
  } as FastifyHttpProxyOptions)

  fastify.log.info('Medplum Routes loaded successfully.');
};

export default medplumProvider;
