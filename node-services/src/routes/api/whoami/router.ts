import { FastifyInstance, FastifyPluginAsync, FastifyPluginOptions, FastifyReply, FastifyRequest } from 'fastify';

const identityRoutes: FastifyPluginAsync = async (
  encapsulatedRouter: FastifyInstance,
  options: FastifyPluginOptions,
) => {
  encapsulatedRouter.get(
    '/',
    {
      schema: {
        ...options.schema,
        description: 'Returns User information based on x-access-token',
        tags: ['Access & Identity Service'],
        summary: 'Get User Information',
      },
    },
    async function whoami(request: FastifyRequest, reply: FastifyReply) {
      return request.requestContext?.get('whoami');
    },
  );
};

export default identityRoutes;
