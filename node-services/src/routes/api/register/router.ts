import { FastifyInstance } from "fastify";
import { S } from "fluent-json-schema";
import { createPractitionerHandler } from "../practitioners/handler";

export default function (instance: FastifyInstance, options: any, next: any) {

    instance.post('/', {
        schema: {
            tags: ['Registration'],
            description: 'Register new Practitioner',
            summary: 'API registers a new practitioner under an organization.',
            body: S.object()
                .prop('givenName', S.string().required().description('First name'))
                .prop('surname', S.string().description('Last name').default(''))
                .prop('email', S.string().format('email').required().description('Email address'))
                // .prop('mobilePhone', S.string().description('Mobile phone number').default(''))
                .prop('organizationId', S.string().description('Organization ID').default(''))
                .prop('sendInvite', S.boolean().description('Send invite email').default(false))
                .prop('enabled', S.boolean().description('Enabled').default(true))
                .prop('displayName', S.string().description('Display name').default(''))
                .prop('id', S.string().required().description('Unique identifier from provider'))
                .prop('provider', S.enum(['msgraph', 'entra']).required().description('Provider'))
        }
    }, 
    async function createPractitionerRouter(request: any, reply: any) {
        const practitioner = await createPractitionerHandler(request, reply);
        reply.send(practitioner);
    });

    next();
}
