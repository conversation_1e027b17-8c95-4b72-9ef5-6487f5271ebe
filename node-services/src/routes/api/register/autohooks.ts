import { FastifyInstance, FastifyRequest, FastifyReply } from "fastify";
import { RelicOrganization } from "relic-ui";

export default function (encapsulatedInstance: FastifyInstance, options: any, next: any) {
    // By the time this hook is called, we know that there is a valid Jwt presented by the user obtained via entra or equivalent api.
    // Trying to mimic setOrganization in Authentication Hook but we may not have an organization yet. 
    // In such a scenario, we modify the request body to create a new organization.
    encapsulatedInstance.addHook('preValidation', async (request: FastifyRequest, reply: FastifyReply) => {
        const { email, organizationId, provider } = request.body as any;
        const relicCareOrg: RelicOrganization = await encapsulatedInstance.orgService.getRelicCareOrganization();
        request.requestContext.set('organization' as never, relicCareOrg as never);
        const domain = (email as string).split('@').at(-1);
        const filter: any[] = [
            { website: domain },
        ];
        if (organizationId) {
            filter.push({ "externalIdentifier.id": organizationId });
        }
        let registeredOrg: RelicOrganization = await encapsulatedInstance.mongo.reliccare.db
            .collection<RelicOrganization>('organizations')
            .findOne({ $or: filter }, { projection: { _id: 0 } });
        if (registeredOrg) {
            encapsulatedInstance.log.debug({
                msg: 'Organization already exists:', 
                organization: registeredOrg
            });
            const registrationRequest: any = request.body;
            request.body = {
                ...registrationRequest,
                organizationId: registeredOrg.id,
            };
            request.requestContext.set('organization' as never, registeredOrg as never);
            request.headers['x-organization-id'] = registeredOrg.id;
        } else {
            //Create a base business organization using just the domain name.
            const newOrganization: RelicOrganization = {
                id: undefined,
                resourceType: 'Organization',
                type: 'bus',
                name: domain,
                website: domain,
                externalIdentifier: {
                    id: organizationId,
                    provider: provider
                },
            };
            //Temporarily set the request body to the new organization.
            const registrationRequest: any = request.body;
            const registeredOrg = await encapsulatedInstance.orgService.createOrganization(newOrganization);

            request.requestContext.set('organization' as never, registeredOrg as never);
            request.headers['x-organization-id'] = registeredOrg.id;
            //Restore the registration request to the original request body.
            request.body = {
                ...registrationRequest,
                organizationId: registeredOrg.id,
            };
        }
    });
    
    //Organization has been provisioned. Practitioner can now be provisioned via registration request.
    next();
};
