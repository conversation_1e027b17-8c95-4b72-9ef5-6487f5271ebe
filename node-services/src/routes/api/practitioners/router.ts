import { S } from 'fluent-json-schema';
import { FastifyInstance, FastifyPluginOptions, FastifyReply, FastifyRequest } from 'fastify';
import { IUserIdentity } from 'relic-ui';
import { RelicChatParticipant, Thread, ThreadsWithCount } from 'relic-ui';
import { User } from '@microsoft/microsoft-graph-types';
import { RelicPractitioner, PractitionerFilters } from 'relic-ui';
import { createPractitionerHandler } from './handler';

interface PractitionerIdParams {
  Params: { id: string };
}

export default function (encapsulatedRouter: FastifyInstance, options: FastifyPluginOptions, next: () => void) {
  encapsulatedRouter.get(
    '/',
    {
      preHandler: async (request: any, reply: any) => {
        let { ids, organizationId, name, email, mobilePhone, _start, _end, _sort, _order, _search } = request.query;
        const filter = {} as PractitionerFilters;
        // Ensure 'ids' is an array
        const idArray = ids ? (ids as string).split(',') : [];
        const validIdArray = idArray.filter((id) => id !== undefined);
        if (validIdArray && validIdArray.length > 0) {
          filter.id = { $in: validIdArray };
        }
        if (name !== undefined) {
          filter.name = name;
        }
        if (email !== undefined) {
          filter.email = email;
        }
        if (mobilePhone !== undefined) {
          filter.mobilePhone = mobilePhone;
        }
        if (organizationId !== undefined) {
          filter.organizationId = organizationId;
        }
        request.filters = {
          filter,
          _search,
          _count: _end - _start ? _end - _start : 20,
          _sort,
          _order,
          _start,
          _end,
        };
      },
      schema: {
        querystring: S.object()
          .prop('ids', S.string().description('Comma-separated list of practitioner IDs to filter'))
          .prop('_sort', S.string().default('name'))
          .prop('_order', S.enum(['asc', 'desc']).default('asc')),
        summary: `Get all practitioners or filter by IDs.`,
        tags: ['Practitioner Service'],
        description: `Fetches all practitioners or filters them based on provided IDs.`,
      },
    },
    async function getPractitioners(request: any, reply: any) {
      const { practitioners, totalCount } = await request.server.getPractitioners(request.filters);
      reply.header('x-total-count', totalCount);
      reply.send(practitioners);
    },
  );

  encapsulatedRouter.get(
    '/:id',
    {
      schema: {
        params: S.object().prop('id', S.string().required().description('ID of the practitioner')),
        summary: `Get a specific practitioner by Id. Id can be EHR Id, Relic Care Id, email or mobile number.`,
        tags: ['Practitioner Service'],
        description: `Fetches a specific practitioner from the database using the provided ID.`,
      },
    },
    async function getPractitioner(request: FastifyRequest<PractitionerIdParams>, reply: FastifyReply) {
      const practitionerId = request.params.id;
      const practitioner = await request.server.getPractitioner(practitionerId);
      reply.send(practitioner);
    },
  );

  encapsulatedRouter.get(
    '/:id/chat',
    {
      schema: {
        ...options.schema,
        params: S.object().prop(
          'id',
          S.string().minLength(1).required().description(`id can be resource id or ACS id\n
        Sample value: 8:acs:8ca4fbc2-e63c-4283-a5b7-feb83074370e_0000001b-57e2-3b1a-28f4-343a0d000dc\n
        Where to get: acs Id (practitioners.communicationIdentities.userId or patients.communicationIdentities.userId)\n
        resource id (practitioners.id or patients.id)`),
        ),
        summary: `Get Practitioner's default thread or a thread with Practitioner participant depending on the caller's resource type.`,
        tags: ['Communication Service'],
        description: `Returns Practitioner's Default Chat thread if caller is Practitioner. Returns Caregiver Chat thread between Practitioner and Patient if caller is Patient`,
      },
    },
    async function getPractitionerThread(request: any, reply: any) {
      const me: IUserIdentity = request.requestContext.get('whoami');
      const filter: any = {};
      const participants: RelicChatParticipant[] = [];
      filter.status = 'active';
      participants.push({
        resourceId: me.id,
        resourceType: me.resourceType === 'Patient' ? 'Patient' : 'Practitioner', //I can be patient or practitioner
        id: undefined, //ParticipantACS id will be patched by Communication Plugin
      });
      participants.push({
        resourceId: request.params.id,
        resourceType: 'Practitioner', //Param is a practitioner since this is a practitioner route
        id: undefined, //ParticipantACS id will be patched by Communication Plugin
      });
      filter.relicChatParticipants = participants;
      const threadsWithCount: ThreadsWithCount = await request.server.getUserThreads(filter);
      const threads: Thread[] = threadsWithCount.threads;
      const thread: Thread = threads?.[0];
      const newThread: Thread = {
        endpoint: '',
        threadId: '',
        threadTopic: '',
        threadSubject: {
          organizationId: me.portalIdentity.organizationId,
        },
        participants: participants,
        status: 'active',
        inviteVia: 'none',
        createDate: new Date(),
      };
      if (!thread) {
        return await request.server.createThreadV2(newThread);
      }
      return thread;
    },
  );

  encapsulatedRouter.post(
    '/',
    {
      schema: {
        body: S.object()
          .prop('givenName', S.string().required().description('Given name of the practitioner'))
          .prop('surname', S.string().required().description('Surname of the practitioner'))
          .prop('email', S.string().format('email').required().description('Email of the practitioner'))
          .prop('mobilePhone', S.string().description('Mobile phone number of the practitioner'))
          .prop(
            'organizationId',
            S.string().required().description('Organization Id to which the practitioner belongs'),
          )
          .prop('sendInvite', S.boolean().required().description('Whether to send an invite to the practitioner'))
          .prop('enabled', S.boolean().required().description('Whether the account is enabled for logging in'))
          .prop('displayName', S.string().required().description('Display name of the practitioner'))
          .prop('provider', S.enum(['msgraph', 'entra', 'medplum', 'pcc']).required().description('Provider'))
          .prop('id', S.string().description('ID of the practitioner')),
        summary: `Create a new Practitioner using Azure AD B2C as the provider.`,
        tags: ['Practitioner Service'],
        description: `Creates a new Practitioner in Azure AD B2C and Relic Care Mongo DB.`,
      },
    },
    async function createPractitionerRouter(request: any, reply: any) {
      const practitioner = await createPractitionerHandler(request, reply);
      reply.send(practitioner);
    },
  );

  encapsulatedRouter.patch(
    '/:id',
    {
      schema: {
        body: S.object()
          .prop('name', S.string().required().description('Name of the practitioner'))
          .prop('email', S.string().format('email').required().description('Email of the practitioner'))
          .prop('mobilePhone', S.string().description('Mobile phone number of the practitioner'))
          .prop(
            'organizationId',
            S.string().required().description('Organization Id to which the practitioner belongs'),
          )
          .prop('enabled', S.boolean().required().description('Status of the practitioner'))
          .prop('userPrincipalName', S.string().required().description('User Principal Name of the practitioner'))
          .prop('provider', S.enum(['msgraph', 'entra', 'medplum', 'pcc']).required().description('Provider')),
        summary: `Update an existing Practitioner using Azure AD B2C as the provider.`,
        tags: ['Practitioner Service'],
        description: `Updates an existing Practitioner in Azure AD B2C and Relic Care Mongo DB.`,
      },
    },
    async function updatePractitioner(request: any, reply: any) {
      let { name, email, mobilePhone, organizationId, enabled, provider } = request.body;
      const names = name.trim().split(' ');
      const givenName = names.slice(0, -1).join(' ');
      const surname = names.slice(-1).join('');
      if (mobilePhone && request.server.validatePhoneNumber(mobilePhone)) {
          mobilePhone = request.server.formatPhoneNumber(mobilePhone);
      }
      const relicPractitioner: RelicPractitioner = {
        id: request.params.id,
        resourceType: 'Practitioner',
        email: email,
        mobilePhone: mobilePhone,
        enabled: enabled,
        organizationId: organizationId,
        name: name,
        provider: provider,
        role: { name: 'member' }, // Default role, can be customized as needed
      };
      const msGraphPractitioner: User = {
        givenName,
        surname,
        mail: email,
        mobilePhone,
        accountEnabled: enabled,
        displayName: `${givenName} ${surname}`,
      };
      const practitioner = await request.server.updatePractitioner(relicPractitioner, msGraphPractitioner);
      reply.send(practitioner);
    },
  );

  next();
}
