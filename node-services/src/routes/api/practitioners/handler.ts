import { FastifyRequest, FastifyReply } from 'fastify';
import { RelicPractitioner } from 'relic-ui';
import { User } from '@microsoft/microsoft-graph-types';

export async function createPractitionerHandler(request: FastifyRequest, reply: FastifyReply): Promise<RelicPractitioner> {
    let { givenName, surname, email, mobilePhone, organizationId, enabled, displayName, provider, id } = request.body as any;

    if (!displayName) {
        displayName = `${givenName} ${surname}`;
    }

    if (mobilePhone && (request.server as any).validatePhoneNumber(mobilePhone)) {
        mobilePhone = (request.server as any).formatPhoneNumber(mobilePhone);
    }

    const relicPractitioner: RelicPractitioner = {
        id: id,
        resourceType: 'Practitioner',
        email: email,
        mobilePhone: mobilePhone,
        organizationId: organizationId,
        name: displayName,
        enabled: enabled,
        provider: provider,
        role: { name: 'member' } // Default role, can be customized as needed
    };

    const msGraphPractitioner: User = {
        givenName,
        surname,
        mail: email,
        mobilePhone,
        companyName: organizationId,
        accountEnabled: enabled,
        displayName
    };

    const practitioner = await (request.server as any).createPractitioner(relicPractitioner, msGraphPractitioner);
    return practitioner;
}
