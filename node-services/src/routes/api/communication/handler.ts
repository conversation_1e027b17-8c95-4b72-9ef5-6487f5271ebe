import { Thread, ThreadsWithCount } from "relic-ui";

export async function getUserThreads(req:any, resp:any){
    const service:any = req.server;
    const threadsWithCount:ThreadsWithCount = await service.getUserThreads(req.filter);
    const threads: Thread[] = threadsWithCount.threads;
    const count: any = threadsWithCount.count;
    if ((count) && (count[0])) {
        resp.header('x-total-count', count[0].count);
    }
    return threads;
}

export async function getUserThreadById(req:any, resp:any){
    const service:any = req.server;
    return await service.getUserThreadById(req.params.threadId);
}

export async function updateUserThread(req:any, resp:any){
    const service:any = req.server;
    const threadRequest:Thread = req.body as Thread;
    const threadId:string = req.params.threadId;
    threadRequest.threadId = threadId;
    const thread = await service.updateUserThread(threadId, threadRequest) as Thread;
    return thread;
}
