import { S } from "fluent-json-schema";
import { relicRequire } from "../../../utils/common-js";
import { FastifyInstance, FastifyPluginOptions, FastifyRequest, FastifyReply } from "fastify";
import { requestContext } from '@fastify/request-context';
import { getUserThreadById, getUserThreads, updateUserThread } from './handler';
import { IUserIdentity } from "relic-ui";
import { RelicOrganization } from "relic-ui";
import { RelicChatParticipant, Thread } from "relic-ui";
import * as Handlebars from 'handlebars';
import * as jwt from 'jsonwebtoken';
import axios from "axios";
import { RelicPatient, RelicIdParam } from "relic-ui";
import config from 'config';

// Schema definition for Id param. Can be imported from 'relic-ui'.
const { RelicIdParamSchema } = relicRequire('relic-ui/schema');

const communicationRoutes = async (fastify:FastifyInstance, options:FastifyPluginOptions) => {

    fastify.get(
      '/identity/:id',
      {
        schema: {
          ...options.schema,
          description: `Query communication identity through email, phone, ACS or EHR Id.\n
                "Patient" users can query only their own communication identity.\n
                "Practitioner" users can query communication identity for patients they have access to.\n
                "Agent" users can query communication identity for all practitioners and patients in their organization.
            `,
          tags: ['Communication Service'],
          summary: 'Get Communication Identity',
          params: RelicIdParamSchema,
        },
      },
      async function (req: FastifyRequest, rep: FastifyReply) {
        const userIdentity: IUserIdentity = requestContext.get('whoami');
        const { id } = req.params as RelicIdParam;
        if (userIdentity.resourceType === 'Patient' && userIdentity.id !== id) {
            throw fastify.httpErrors.forbidden(
                'Patients can only query their own communication identity.',
                );
        }
        const identity = await fastify.getCommunicationIdentityById(id);
        return identity;
      },
    );

    async function shortenUrl(longUrl: string) {
        const BITLY_API_TOKEN: string = config.get('BITLY.TOKEN');
        const BITLY_API_URL: string = config.get('BITLY.URL');
        try {
            const response = await axios.post(BITLY_API_URL,
                {
                    "long_url": longUrl,
                },
                {
                    headers: {
                        'Authorization': `Bearer ${BITLY_API_TOKEN}`,
                        'Content-Type': 'application/json'
                    }
                }
            );
            return response.data.link;
        } catch (error) {
            console.error('Error shortening URL:', error.response?.data || error.message);
            throw error;
        }
    }

    async function getToken(req: any, rep: any) {
        const { inviteVia, receiverEmail, receiverPhone } = req.body;
        // For valid email id/phone number we need to create an id Hint.
        // ID Hint will expire in 30 days making essentially the link expire in 30 days.
        const currentEpochTime = Math.floor(Date.now() / 1000);
        const thirtyDaysInSeconds = 30 * 24 * 60 * 60;
        const expirationEpochTime = currentEpochTime + thirtyDaysInSeconds;
        let idHint = {
            "aud": "aadB2c",
            "iss": "node-services",
            "exp": expirationEpochTime,
            "iat": currentEpochTime,
            "relicEmail": '',
            "relicPhone": '',
        };

        if (inviteVia == 'both') {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(receiverEmail)) {
                throw req.server.httpErrors.badRequest('Invalid email format.');
            }
            idHint.relicEmail = receiverEmail;

            const phoneRegex = /^\+\d{1,2}\d{10}$/;
            if (!phoneRegex.test(receiverPhone)) {
                throw req.server.httpErrors.badRequest('Invalid phone number format.');
            }
            idHint.relicPhone = receiverPhone;
        } else if (inviteVia === 'email') {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(receiverEmail)) {
                throw req.server.httpErrors.badRequest('Invalid email format.');
            }
            idHint.relicEmail = receiverEmail;
            delete idHint.relicPhone;
        } else if (inviteVia === 'cellphone') {
            const phoneRegex = /^\+\d{1,2}\d{10}$/;
            if (!phoneRegex.test(receiverPhone)) {
                throw req.server.httpErrors.badRequest('Invalid phone number format.');
            }
            idHint.relicPhone = receiverPhone;
            delete idHint.relicEmail;
        }

        //This secret is hardcoded since it will be same across environments. It is also configured in AD B2C. 
        const secret : string = config.get('AZURE.AADB2C.SECRET');;
        const signedIdHint = jwt.sign(idHint, secret);
        return signedIdHint;
    }

    async function sendNotification(req: any, rep: any) {
        try {
            const { inviteVia, receiverEmail, receiverPhone , participants} = req.body;
            const templateOrganization: RelicOrganization = await req.server.orgService.getRelicCareOrganization();
            const emailTemplate = templateOrganization.template.chatNotificationEmail;
            const smsTemplate = templateOrganization.template.chatNotificationSms;

            const patientParticipant = participants.find(participant => participant.resourceType === 'Patient');
            const patientId = patientParticipant?.resourceId;
            const relicPatient: RelicPatient = await req.server.relicPatients().findOne({ id: patientId });
            // check b2cid exist for patient
            if (!relicPatient || !relicPatient.b2cid) {
            throw new Error(`b2cid not found for patient id : ${patientId}`);
            }

            const token : string  = await getToken(req, rep);            
            const inviteUrl : string = `${config.get("SERVER.FRONTEND_URL")}/lobby/login?id_token_hint=${token}`;
            const shortUrl = await shortenUrl(inviteUrl);
            const templateString = Handlebars.compile(emailTemplate);  
            const replace = {
              name: patientParticipant?.displayName ?? "",
              inviteUrl: shortUrl,
            }
            const compiledEmailTemplate = templateString(replace);
            const emailOptions = {
                recipient: receiverEmail,
                subject: 'Welcome to Our Relic Service',
                text: 'Thank you for joining our  relic service!',
                html: `${compiledEmailTemplate}` 
            };

            const smsTemplateString = Handlebars.compile(smsTemplate);
            const compiledSmsTemplate = smsTemplateString(replace);
            const smsOptions = {
                recipient: receiverPhone,
                message: compiledSmsTemplate,
                enableDeliveryReport: true,
                tag: 'verification'
            };
            switch (inviteVia.toLowerCase()) {
                case 'email':
                    await req.server.sendMail(emailOptions);
                    fastify.log.info(`Email sent successfully to : ${receiverEmail}`);
                    break;
                case 'cellphone':
                    await req.server.sendSms(smsOptions);
                    fastify.log.info(`SMS sent successfully to : ${receiverPhone}`);
                    break;
                case 'both':
                    await req.server.sendMail(emailOptions);
                    fastify.log.info(`Email sent successfully to : ${receiverEmail}`);
                    await req.server.sendSms(smsOptions);
                    fastify.log.info(`SMS sent successfully to : ${receiverPhone}`);
                    break;
                default:
                    fastify.log.info('No notification sent as inviteVia is none.');
            }
        }
        catch (error) {
            throw new Error(`Error in send notification ${error}`);
        }
    }

    fastify.post("/chat/threads", {
        schema: {
            body: S.object()
                .prop('participants', S.array().items(
                    S.object()
                        .prop('resourceId', S.string())
                        .prop('resourceType', S.string())
                )).required()
                .prop('inviteVia', S.enum(['email', 'cellphone', 'both', 'none']).default('none'))
                .prop('receiverEmail', S.string().description('receiver email'))
                .prop('receiverPhone', S.string().description('receiver phone'))
                .prop('topic', S.string().description('(optional) topic refers to agentid'))
                .prop('subject', S.object().description('(optional)')
                    .prop('organizationId', S.string())
                    .prop('type', S.enum(['Default', 'Caregiver']))
                    .prop('questionnaireId', S.string())
                    .prop('title', S.string().description('Thread title'))
                    .prop('ThreadOwner', S.object()
                        .prop('resourceType', S.string())
                        .prop('id', S.string()))
                    .prop('targetPhoneNumber', S.string())
                    .prop('patientLanguage', S.object().description('patient language'))
                        .prop('code', S.string())
                        .prop('display', S.string())
                        .prop('system', S.string())
                        .prop('preferred', S.boolean())
                    .prop('calleeNumber', S.object()
                        .prop('rawId', S.string())
                        .prop('phoneNumber', S.string()))
                    .prop('callerNumber', S.object()
                        .prop('rawId', S.string())
                        .prop('phoneNumber', S.string()))),
            summary: `Create threads`,
            tags: ["Communication Service"],
            description: `Create Thread, participants are mandatory. Pass targetPhoneNumber and patientLanguage in subject for outbound call.`
        },
        onResponse: async function (request: any, reply: any) {
            if (reply.statusCode == 200) {
                try {
                    if(request.body.inviteVia != 'none'){
                        await sendNotification(request, reply);
                    } else{
                        fastify.log.info('No notification sent as inviteVia is none.');
                    }
                } catch (error) {
                    throw new Error(`Error sending notification: ${error}`);
                }
            }
        },
    }, async function createThread(request: any, reply: any) {
        let { topic, subject, participants, inviteVia } = request.body;
        const whoami: IUserIdentity = await request.server.requestContext.get('whoami');
        const newThread: Thread = {
            endpoint: '',
            threadId: '',
            threadTopic: topic,
            threadSubject: subject,
            participants: participants,
            status: 'active',
            inviteVia: inviteVia,
            createdBy: {
                id: whoami.id,
                resourceType: whoami.resourceType,
            },
            createDate: new Date(),
            updatedBy: {
                id: whoami.id,
                resourceType: whoami.resourceType,
            },
            updateDate: new Date(),
          };
        return await request.server.createThreadV2(newThread);
    })

    fastify.get('/chat/threads', {
    schema: {
        description: 'Retrieve threads of current user',
        tags: ['Communication Service'],
        summary: 'returns all threads that the current communication user is a participant in',
        query: S.object()
            .prop('threadId', S.string().description("Sample value: 19:cKMSKJASjA7gesKN2HGDE_3C2HVM7cl_BolHlFlRRGE1@thread.v2\nWhere to get: threadId present in threads collection"))
            .prop('type', S.string().description("Sample value: Default\nWhere to get: type field present in threadSubject in threads collection"))
            .prop('agentId', S.string().description("Special case for agents which share same credentials, provide agentId to retrieve the thread\nSample value: e070dbf6-f3ea-4679-8f1a-66ffadceb015\nWhere to get: id field present in agents collection"))
            .prop('questionnaireId', S.string())
            .prop("organizationId", S.string())
            .prop('participant', S.string().description('Filter by participant. Must be a valid RelicChatParticipant object.'))
            .prop('status', S.enum(['active', 'closed']))
            .prop('_order', S.string().enum(['asc', 'desc']).default('desc'))
            .prop('_sort', S.string().enum(['updateDate']).default('updateDate'))
            .prop('_search', S.string().description('Search by displayName or role'))
            .prop('_start', S.number().default(0))
            .prop('_end', S.number().default(25))
        },
        preHandler: async (req: any, rep: any) => {
            const { threadId, type, status, participant, questionnaireId, organizationId, _start, _end, _search, _order, _sort } = req.query;
            const filter = {
                limit: _end - _start,
                offset: _start,
                _search
            };
            
            if(threadId){ filter['threadId'] = threadId; }
            if(type){ filter['type'] = type; }
            if(status){ filter['status'] = status; }
            if (participant) {
                try {
                    const parsedParticipant = JSON.parse(decodeURIComponent(participant)) as RelicChatParticipant;
                    if (!parsedParticipant.id || !parsedParticipant.resourceType) {
                        throw req.server.httpErrors.badRequest('Participant filter must have an id and resourceType.');
                    }
                    filter['relicChatParticipants'] = [parsedParticipant];
                } catch (error) {
                    throw req.server.httpErrors.badRequest('Invalid participant format. Must be a valid JSON object.');
                }
            }
            
            if(organizationId){ filter['organizationId'] = organizationId; }

            if(questionnaireId) { filter['questionnaireId'] = questionnaireId; }
            filter['sort'] = _sort;
            filter['order'] = _order;
            req.filter = filter;
        }
    }, getUserThreads); 

    fastify.get("/chat/threads/:threadId",{
        schema:{
            description: "Retrieve thread by threadId",
            tags: ["Communication Service"],
            summary: "returns thread based on threadId",
            params: S.object()
            .prop('threadId', S.string().description("Sample value: 19:cKMSKJASjA7gesKN2HGDE_3C2HVM7cl_BolHlFlRRGE1@thread.v2\nWhere to get: threadId present in threads collection"))
        }
    }, getUserThreadById); 

    fastify.patch("/chat/threads/:threadId",{
        schema:{
            description: "Patch/Update chat thread",
            tags: ["Communication Service"],
            summary: "Patch/Update a chat thread",
            body:  S.object()
                .prop('status', S.enum(['active', 'closed']))
                .prop('threadSubject', S.object()
                    .prop('targetPhoneNumber', S.string().description(`phone number`))
                    .prop('patientLanguage', S.object().description('patient language')
                        .prop('code', S.string())
                        .prop('display', S.string())
                        .prop('system', S.string())
                        .prop('preferred', S.boolean())
                    )
                    .prop('title', S.string().description('Thread title'))
                )
                .prop('updateDate', S.string().format("date-time").description('indicates date of recent update'))
        },
    }, updateUserThread); 

}

export default communicationRoutes