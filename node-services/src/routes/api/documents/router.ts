import { RelicDocument } from "@/types/document";
import { RelicOrganization } from "relic-ui";
import { IUserIdentity } from "relic-ui";
import { FastifyInstance, FastifyPluginOptions } from "fastify";
import { S } from "fluent-json-schema";

export default async function (fastify: FastifyInstance, options: FastifyPluginOptions) {
    fastify.get('/', {
        schema: {
            ...options.schema,
            querystring: S.object()
                .prop('_start', S.number().default(0))
                .prop('_end', S.number().default(25))
                .prop('_search', S.string().maxLength(255).description('Search document by filename or care plan details'))
                .prop('_order', S.string().enum(['asc', 'desc']).default('desc'))
                .prop('_sort', S.string().enum(['language', 'createDate', 'organizationId']).default('createDate'))
                .prop('patientId', S.string().description('Search documents accessible by a patient'))
                .prop('_status',  S.string().enum(['active', 'closed']).description("status is carePlanStatus that represents the status of the Care Plan."))
                .prop('_focusStatus',  S.string().enum(['active', 'resolved', 'cancelled']).description("focusStatus represents the status of the Focuses within the Care Plan")),
            tags: ['Document Service'],
            description: 'Get list of documents or care plans',
            summary: 'Retrieve documents or care plans',
        },
        preSerialization: attachAzureSASTokenHook,
    }, async function (req: any, rep: any) {
        //cb1-461: for documents pagination, return list for previous,current and next pages
        const { patientId, _search, _start, _end, _order, _sort } = req.query;
        const pageSize = (_end - _start);
        const currentPage = Math.floor(_end / pageSize);
        const limit = pageSize;
        const skip = pageSize * (currentPage - 1);
        let searchOptions = {
            limit: limit,
            skip: skip,
            search: _search ?? ''
        }
        if (patientId) {
            searchOptions['patientId'] = patientId;
        }
        searchOptions['sort'] = _sort;
        searchOptions['order'] = _order;

        const whoami = req.requestContext.get('whoami') as IUserIdentity;
        if (whoami.resourceType === 'Patient') {
            searchOptions['patientId'] = whoami.id;
        }
        if (whoami.role.name !== 'admin') {
            searchOptions['organizationId'] = fastify.requestContext.get('organizationId');
        }
        const { data, total } = await fastify.searchDocuments(searchOptions);
        rep.header('x-total-count', total);
        return data;
    });

    fastify.post('/', {
        schema: {
            body: S.object()
                .prop('filename', S.string().required().description("original filename (during upload) to use for display"))
                .prop('documentId', S.string().required().description("actual filename in the remote storage"))
                .prop('url', S.string().description('url of the document in remote storage, to use for view or download').required())
                .prop('language', S.string().description('language used in the document').default('En-US'))
                .prop('type', S.string().description('content type, ie. application/pdf'))
                .prop('patientId', S.string().description('patient who has access to the document'))
                .prop('header', S.number().required().description('how many lines from top are in the header?'))
                .prop('footer', S.number().required().description('how many lines from the bottom are in the footer?')),
            tags: ['Document Service'],
            description: 'Save document information',
            summary: 'Save document information to node-services database'
        }
    }, async function (req: any, rep: any) {
        const { filename, documentId, url, type, patientId, language, header, footer } = req.body;
        const organization: RelicOrganization = req.requestContext.get('organization');
        const document: RelicDocument = {
            organizationId: organization?.id,
            language: language,
            type: type,
            url: url,
            filename: filename,
            documentId: documentId,
            isUploaded: true,
            access: [],
            header: header,
            footer: footer,
            status: 'done',
            translationType: 'mono'
        }
        if (patientId) {
            document.access.push({ id: patientId, resourceType: 'Patient' });
        }
        const result = await req.server.saveDocument(document);
        //result.url is clean, so re-use the url with SAS token instead of generating new token
        result.url = url;
        return result;
    });

    fastify.delete('/:id', {
        schema: {
            params: S.object()
                .prop('id', S.string().description('document id')),
            tags: ['Document Service'],
            description: 'Delete document',
            summary: 'Delete document'
        }
    }, async function (req: any, rep: any) {
        return await req.server.deleteDocument(req.params.id);
    });

    fastify.get('/:id', {
        schema: {
            params: S.object()
                .prop('id', S.string().description('Document or CarePlan ID')),
            querystring: S.object()
                .prop('_interventionStatus', S.string().enum(['active', 'resolved', 'cancelled']).description('Filter by intervention status'))
                .prop('_goalStatus', S.string().enum(['active', 'resolved', 'cancelled']).description('Filter by goal status'))
                .prop('_latest', S.boolean().default(false).description('Fetch the latest care plan data')),
            tags: ['Document Service'],
            description: 'Retrieve details of a document or care plan',
            summary: 'Get document or care plan details',
        },
        preSerialization: attachAzureSASTokenHook,
    }, async function (req: any, rep: any) {
        return await req.server.getDocumentById(req.params.id);
    });

    fastify.patch("/:id", {
        schema: {
            params: S.object()
                .prop('id', S.string().description('document id')),
            body: S.object()
                .prop('filename', S.string().required().description('new filename of the document'))
                .prop('header', S.number().required().description('how many lines from top are in the header?'))
                .prop('footer', S.number().required().description('how many lines from the bottom are in the footer?'))
                .prop('url', S.string().default("").description('azure storage file url. If url not empty, document automatically set to status="done"')),
            tags: ['Document Service'],
            description: 'Rename document',
            summary: 'Rename document'
        },
        preSerialization: attachAzureSASTokenHook,
    }, async function (req: any, rep: any) {
        const { filename, header, footer, url } = req.body;
        const doc: RelicDocument = {
            filename: filename,
            header: header,
            footer: footer,
            language: '',
            type: '',
            url: url,
            documentId: '',
            status: (url?.length > 0)? 'done': 'pending',
            organizationId: '',
            translationType: 'mono'
        }
        return await req.server.updateDocument(req.params.id, doc);
    });

    fastify.post('/:id/translate', {
        schema: {
            params: S.object()
                .prop('id', S.string().description('document id')),
            querystring: S.object()
                .prop('targetLanguage', S.string().description('target language to be used for document translation, ie. JA for japanese')),
            body: S.object()
                .prop('targetLanguage', S.string().description('target language to be used for document translation, ie. JA for japanese'))
                .prop('translationType', S.string().default('bilingual').description('"mono" | "bilingual" //if not passed, default is bilingual')),
            tags: ['Document Service'],
            description: 'Translate document to target language',
            summary: 'Translate document to target language'
        },
        preSerialization: attachAzureSASTokenHook,
    }, async function (req: any, rep: any) {
        const options = {
            targetLanguage: req.body.targetLanguage ?? req.query.targetLanguage,
            translationType: req.body.translationType ?? 'bilingual'
        };
        if(!options.targetLanguage){ //validation here while frontend is not yet updated
            throw req.server.httpErrors.badRequest('targetLanguage is required');
        }
        return await req.server.createTranslatedDocument(req.params.id, options);
    });

    async function attachAzureSASTokenHook(req: any, rep: any, payload: any) {
        const _attachTokenToDocuments = async function (documents: RelicDocument[]) {
            if (documents.length == 0 || !documents[0].id) { //possible error object
                return payload;
            }

            const tokens: Map<string, string> = new Map();
            const containerNames: string[] = [...new Set(documents.map(document => document.organizationId))];

            for (let containerName of containerNames) {
                const token: string = await req.server.generateAzureStorageSASToken({containerName, permissions: 'r'});
                tokens.set(containerName, token);
            }

            documents.forEach(document => {
                if (document.url && document.url.length > 0) {
                    document.url = [document.url?.split('?')?.[0], '?', tokens.get(document.organizationId)].join('').trim();
                }
            })
            return documents;
        }

        if (rep.statusCode == 200) {
            if (payload?.id) {
                const result = await _attachTokenToDocuments([payload]);
                return result?.[0];
            } else {
                return await _attachTokenToDocuments(payload);
            }
        }
        return payload;
    }
}