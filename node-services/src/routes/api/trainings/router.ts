import { FastifyInstance, FastifyReply, FastifyRequest } from "fastify";
import handler from "./handlers";
import { S } from "fluent-json-schema";
import { IUserIdentity } from "relic-ui";
import { RelicSearchQuery } from "@/types/request";

export default async function (instance: FastifyInstance, options: any) {

    instance.addHook('preHandler', async function (req: FastifyRequest, rep: FastifyReply) {
        const whoami = req.requestContext.get('whoami' as never) as IUserIdentity;
        if (whoami.role.name !== 'admin') {
            (req.query as RelicSearchQuery).organizationId = whoami.portalIdentity.organizationId;
        }
    });

    instance.post('/', {
        schema: {
            ...options.schema,
            tags: ['Training Service'],
            body: S.object()
                .prop('organizationId', S.string().description('(optional) if empty, logged-in user`s organization is used'))
                .prop('name', S.string().required().description('training name'))
                .prop('description', S.string().description('short description of the training'))
                .prop('modules', S.array().description('list of training modules included').default([])
                    .items(S.object()
                        .prop('id', S.string().format('uuid').required().description('training module id'))))
                .prop('schedule', S.object().description('indicate how often this training is updated by the indexers')
                    .prop('interval', S.string().default('PT24H').description('format: https://www.w3.org/TR/xmlschema11-2/#dayTimeDuration'))
                    .prop('startTime', S.string().format('date-time').description('start time for the indexers in UTC')))
        }
    }, handler.createTraining);

    instance.get('/', {
        schema: {
            tags: ['Training Service'],
            summary: 'Search trainings',
            description: 'Obtain list of trainings based on query. API returns array of Trainings.',
            querystring: S.object()
                .prop('_search', S.string().maxLength(255).description('search training against fields [name, indexName, description]'))
                .prop('_order', S.enum(['asc', 'desc']).default('desc'))
                .prop('_sort', S.enum(['name', 'updateDate']).default('updateDate'))
                .prop('_start', S.number().default(0).minimum(0))
                .prop('_end', S.number().default(25).minimum(1))
        },
    }, handler.searchTrainings);

    instance.get('/:id', {
        schema: {
            tags: ['Training Service'],
            summary: 'Get training by ID',
            description: 'Obtain training details based on provided id. API returns Training.',
            params: S.object()
                .prop('id', S.string().format('uuid').required().description('Training ID. See db collection trainings > id (field).')),
        }
    }, handler.getTrainingDetails);

    instance.patch('/:id', {
        schema: {
            tags: ['Training Service'],
            params: S.object()
                .prop('id', S.string().format('uuid').required().description('Training ID. See db collection trainings > id (field).')),
        }
    }, handler.updateTrainingDetails);

    instance.delete('/:id', {
        schema: {
            tags: ['Training Service'],
            params: S.object()
                .prop('id', S.string().format('uuid').required().description('Training ID. See db collection trainings > id (field).')),
        }
    }, handler.deleteTraining);

    instance.post('/:id/modules/', {
        schema: {
            tags: ['Training Service'],
            summary: 'Add a training module to the training',
            description: 'Add a training module to the training. API will create an Azure Search Indexer for the module and return the updated Training details.',
            params: S.object()
                .prop('id', S.string().format('uuid').required().description('Training ID, see trainings db collection')),
            body: S.object()
                .prop('modules', S.array().required().description('List of training module IDs to add')
                    .items(
                        S.object()
                            .prop('id', S.string().format('uuid').required().description('Training Module ID, see trainingModules db collection'))
                    ))
        }
    }, handler.addModulesToTraining);

    instance.get('/:id/modules/', {
        schema: {
            tags: ['Training Service'],
            summary: 'Get list of modules assigned to a training.',
            description: 'Get list of modules assigned to a training. API returns an array of Training Modules assigned to the training.',
            params: S.object()
                .prop('id', S.string().format('uuid').required().description('Training ID, see trainings db collection'))
        }
    }, handler.getModulesAssignedToTraining);

    instance.delete('/:id/modules/', {
        schema: {
            tags: ['Training Service'],
            summary: 'Remove training modules from the training',
            description: 'Remove training modules from the training. API will delete the Azure Search Indexer for the module, delete documents from the index and return updated training',
            params: S.object()
                .prop('id', S.string().format('uuid').required().description('Training ID, see trainings db collection')),
            body: S.object()
                .prop('modules', S.array().required().description('List of training module IDs to remove')
                    .items(
                        S.object()
                            .prop('id', S.string().format('uuid').required().description('Training Module ID, see trainingModules db collection'))
                    ))
        }
    }, handler.removeModulesFromTraining);
}