import { Injectable, Logger, OnModuleInit, NotFoundException, InternalServerErrorException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import type { ChatMessageType } from '@azure/communication-chat';
import type { CommunicationUserKind } from '@azure/communication-common';
import { getIdentifierRawId } from '@azure/communication-common';
import { AzureOpenAI, AzureClientOptions } from 'openai';
import { ChatCompletion, ChatCompletionMessageParam, ChatCompletionCreateParamsNonStreaming } from 'openai/resources';
import { NodeService } from '../node/node.service';
import { ChatClientManager } from '../chat/chat-client-manager.service';
import {
  isWidgetMessage,
  isChatMessage,
  isCallMessage,
  isSummaryMessage,
  createOpenAiMessage,
} from 'src/utils/message.utils';
import {
  isCallConnectedEventData,
  type CallConnectedEventData,
  type CallDisconnectedEventData,
  type ConversationState,
} from '../types';
import type { AcsChatThreadPropertiesUpdatedEventData } from '@azure/eventgrid';
import type {
  ChatMessage,
  ChatMetadata,
  RelicAgent,
  Topic,
  Thread,
  RelicChatParticipant,
  MessageIntl,
  JsonString,
  Citation,
  RelicCommunicationLanguage,
  CallNotificationWidget,
} from 'relic-ui';
import { get_encoding } from 'tiktoken';
import { AgentResponse } from 'src/types/conversation.types';
import { ThreadSpeechService, ThreadSpeechMap } from 'src/thread/thread.speech';
import { calculateDuration } from 'src/utils/common.utils';
import { getHumanPatient, getHumanPractitioner, getThreadAssistant, getAiAssistant } from 'src/utils/participant.utils';
import { AzureKeyCredential, SearchClient } from '@azure/search-documents';

/**
 * Service for managing agents and generating responses.
 */
@Injectable()
export class AgentService implements OnModuleInit {
  private readonly logger = new Logger(AgentService.name);
  private openAiClient: AzureOpenAI;
  private chatModel: string; // OpenAI chat model
  private embeddingModel: string; // OpenAI embedding model
  private agentIdList: string[]; // list of all ACS IDs of AI counsellors and AI Assistant
  private agentList: RelicAgent[];
  private aiAssistantId: string; // ACS ID of AI Assistant
  private aiAssistantDisplayName: string; // Display name of AI Assistant
  private currentAgent: RelicAgent; // current agent
  private azureSearchConfig: { endpoint: string; apiKey: string };

  constructor(
    private configService: ConfigService,
    private nodeService: NodeService,
    private chatClientManager: ChatClientManager,
    private threadSpeechService: ThreadSpeechService,
  ) {}

  /**
   * Initializes the agent service.
   * @throws {Error} If no agents are found during initialization.
   */
  private async initialize(): Promise<void> {
    const allAgents = await this.nodeService.getAllAgents();
    if (allAgents.length === 0) {
      throw new Error('Initialization failed: No agents found.');
    }
    this.agentList = allAgents;
    this.agentIdList = this.agentList.map((d) => d.communicationIdentities && d.communicationIdentities[0].userId);
    const aiAssistant: RelicAgent = await this.nodeService.getAIAssistant(); // AI Assistant
    this.aiAssistantId = aiAssistant?.communicationIdentities?.[0]?.userId;
    this.aiAssistantDisplayName = aiAssistant.name; // AI Assistant

    const { endpoint, apiKey, chatModel, embeddingModel } = await this.loadOpenAiConfig();
    const azureClientOptions: AzureClientOptions = {
      endpoint: endpoint,
      apiKey: apiKey,
      apiVersion: '2024-11-01-preview',
    };
    this.openAiClient = new AzureOpenAI(azureClientOptions);
    this.azureSearchConfig = await this.loadAzureSearchConfig();
    this.chatModel = chatModel; // OpenAI chat model
    this.embeddingModel = embeddingModel; // OpenAI embedding model
    this.logger.log('Agent Service has been initialized.');
  }

  async onModuleInit(): Promise<void> {
    await this.initialize();
  }

  private async loadOpenAiConfig(): Promise<{ endpoint: string; apiKey: string; chatModel: string; embeddingModel: string }> {
    const endpoint = (await this.nodeService.getConfig('AZURE.OPENAI_ENDPOINT')).value;
    const apiKey = (await this.nodeService.getConfig('AZURE.OPENAI_API_KEY')).value;
    const chatModel = (await this.nodeService.getConfig('AZURE.OPENAI_CHAT_MODEL')).value;
    const embeddingModel = (await this.nodeService.getConfig('AZURE.OPENAI_EMBEDDING_MODEL')).value;
    if (!endpoint || !apiKey || !chatModel || !embeddingModel) {
      this.logger.error('OpenAI environment variables are missing or incorrect.');
      throw new NotFoundException('OpenAI configuration is missing or incorrect.');
    }
    return { endpoint, apiKey, chatModel, embeddingModel };
  }

  private async loadAzureSearchConfig(): Promise<{ endpoint: string; apiKey: string }> {
    const endpoint = (await this.nodeService.getConfig('AZURE.AI_SEARCH_ENDPOINT')).value;
    const apiKey = (await this.nodeService.getConfig('AZURE.AI_SEARCH_API_KEY')).value;
    if (!endpoint || !apiKey) {
      this.logger.error('Azure AI Search environment variables are missing or incorrect.');
      throw new NotFoundException('Azure AI Search configuration is missing or incorrect.');
    }
    return { endpoint, apiKey };
  }

  // Helper function to extract and format citations
  private extractCitations(relevantDocs: Array<any>): Citation[] {
    const citations: Citation[] = [];
    relevantDocs.forEach((doc, index) => {
      let content = doc.caption;
      if (!content && doc.content) {
        const citationContent: string = doc.content.replace(/\n+/g, '\n\n');
        const words = citationContent.match(/\b\w+\b/g) || [];
        content = words.slice(0, 200).join(' ');
      }

      if (!citations.find((c) => c.url === doc.url)) {
        citations.push({
          id: `doc${index + 1}`,
          title: doc.title,
          content: content,
          filepath: doc.originalFilepath || doc.filepath || undefined,
          url: doc.url || undefined,
        });
      }
    });
    return citations;
  }

  private getTopicFromMessages(allMessages: ChatMessage[]): Topic {
    const topicMessages = allMessages.filter((message) => message.type === 'topicUpdated');
    const lastItem = topicMessages[topicMessages.length - 1];
    const topic: Topic = JSON.parse(lastItem.content.topic);
    return topic;
  }

  private computeTokenCount(chatMessage: ChatCompletionMessageParam, enc: any): number {
    const tokenCount = enc.encode(chatMessage.content).length;
    return tokenCount + 4; // plus 4 for role and miscelaneous tokens
  }

  /**
   * Constructs recent messages for the OpenAI request based on the message history and conversation state.
   *
   * This method processes the message history to select relevant messages for the OpenAI request.
   * It considers the conversation state, token limits, and includes summary messages when available.
   *
   * The main steps involved in constructing the recent messages are:
   * 1. Filter the message history based on the conversation state (e.g., remove widget messages after agent switch).
   * 2. Convert the filtered messages to the OpenAI message format and compute token counts.
   * 3. Find the last summary message that fits within the allocated token count.
   * 4. Include messages from the summary message onwards, or until the allocated token count is reached.
   * 5. Return the selected messages in the OpenAI message format.
   *
   * Note: The method assumes that the `first_sequence_id` is not used and needs further implementation.
   *
   * @param messageHistory The message history.
   * @param senderAcsId The ACS ID of the sender.
   * @param enc The encoding object used for token count computation.
   * @param promptTokenCount The token count of the prompt.
   * @param conversationState The conversation state.
   * @returns The recent messages in the OpenAI message format.
   */
  private constructRecentMessages(
    messageHistory: ChatMessage[],
    senderAcsId: string,
    enc: any,
    promptTokenCount: number,
    conversationState: ConversationState,
  ): ChatCompletionMessageParam[] {
    // TODO: fix this logic
    // first_sequence_id etc is not used.
    const MAX_TOKEN_COUNT = 4096;
    const MAX_OUTPUT_TOKEN_COUNT = 500; // leave 500 tokens for output
    const allocatedTokenCount = MAX_TOKEN_COUNT - MAX_OUTPUT_TOKEN_COUNT - promptTokenCount; // TODO: check if this is positive

    let _messageHistory: ChatMessage[];
    if (conversationState === 'AfterAgentSwitchAccepted' || conversationState === 'AfterAgentSwitchDeclined') {
      // After Agent Switch, the last widget request-response should be removed from chat history
      const L = messageHistory.length;
      _messageHistory = messageHistory.filter((message, index) => {
        const isNotLastOrSecondLast = index < L - 2;
        const isNotWidget = message.metadata?.type != 'widget';
        return isNotWidget && isNotLastOrSecondLast;
      });
      //_messageHistory = [];
    } else {
      // Return all messages except for widget interactions.
      _messageHistory = messageHistory.filter((message) => {
        const isNotWidget = message.metadata?.type != 'widget';
        return isNotWidget;
      });
    }
    /*console.log(
      'messageHistory',
      _messageHistory.length,
      _messageHistory.map((m) => m.content.message),
      _messageHistory.map((m) => m.metadata?.type),
    ); // TODO: remove this line (Temporary logging for debugging)*/

    // Convert to OpenAI format etc
    const augmentedMessageHistory: any[] = [];
    let cumulativeCount = 0;
    for (const message of _messageHistory) {
      let openAiMessage: ChatCompletionMessageParam;
      if ((message?.sender as CommunicationUserKind).communicationUserId === senderAcsId) {
        // "sender" is patient/user
        openAiMessage = createOpenAiMessage('user', message.content.message);
      } else {
        openAiMessage = createOpenAiMessage('assistant', message.content.message); // TODO: what should give 'system' role to "summary message" (otherwise assistant would reply like summary messages)
      }
      const tokenCount = this.computeTokenCount(openAiMessage, enc);
      cumulativeCount += tokenCount;
      augmentedMessageHistory.push({
        message: message,
        openAiMessage: openAiMessage,
        tokenCount: tokenCount,
        cumulativeCount: cumulativeCount,
        isSummaryMessage: isSummaryMessage(message),
      });
    }

    // Compute cumulative token count from tail of the history
    //const cumulativeCount = 0;
    augmentedMessageHistory.reverse();
    // const output = augmentedMessageHistory.map((obj) => {
    //   cumulativeCount += obj.tokenCount;
    //   obj.cumulativeCount = cumulativeCount;
    // });
    augmentedMessageHistory.reverse();

    // Find the last message to be included
    const targetSummaryIndex = augmentedMessageHistory.findIndex(
      (obj) => obj.isSummaryMessage && obj.cumulativeCount <= allocatedTokenCount,
    );
    if (targetSummaryIndex >= 0) {
      return augmentedMessageHistory.slice(targetSummaryIndex).map((obj) => obj.openAiMessage);
    } else {
      // summary not found
      const targetIndex = augmentedMessageHistory.findIndex((obj) => obj.cumulativeCount <= allocatedTokenCount);
      return targetIndex
        ? augmentedMessageHistory.slice(targetIndex).map((obj) => obj.openAiMessage)
        : augmentedMessageHistory.map((obj) => obj.openAiMessage);
    }
  }

  /**
   * Sends a call notification to a specific thread.
   *
   * @param threadId - The ID of the thread to which the notification will be sent.
   * @param callConnectionId - The ID of the call connection.
   * @param message - The message to be sent as a call notification.
   * @returns A promise that resolves to a string, which is the result of the send operation.
   *
   * @remarks
   * This method retrieves the speech map for the thread and constructs a call notification widget.
   * It then sends the agent message with the appropriate metadata and locale.
   */
  private async sendCallNotificationToThread(
    threadId: string,
    callConnectionId: string,
    message: string,
  ): Promise<string> {
    //Get speech map if available
    const threadSpeechMap: ThreadSpeechMap = await this.threadSpeechService.getThreadSpeechMap(threadId);
    if (!threadSpeechMap) {
      return '';
    }
    const callNotificationWidget = {
      widget: 'CallNotificationWidget',
      callConnectionId: callConnectionId,
    } as CallNotificationWidget;
    const metadata: ChatMetadata = {
      type: 'widget',
      widgetData: JSON.stringify(callNotificationWidget) as JsonString<CallNotificationWidget>,
    };
    const requestMessageIntl: MessageIntl = {
      content: '',
      locale: threadSpeechMap.chatLanguage.code,
    }; // No incoming message, hence mocking locale
    return await this.sendRelicMessage(threadId, message, undefined, requestMessageIntl, metadata);
  }

  private async searchKnowledgeBase(query: string) {
    //cb1-732: issue caused by mismatch in data format (old data vs updated agent data), kbLinked[0] vs kbLinked[0].indexName
    const indexName = this.currentAgent.relicAssistantSetup.kbLinked[0];
    const relevantDocuments = [];
    if (!query || !indexName) {
      return relevantDocuments;
    }
    const azureSearchClient = new SearchClient(
      this.azureSearchConfig.endpoint,
      indexName,
      new AzureKeyCredential(this.azureSearchConfig.apiKey),
    );

    const queryVector = await this.generateEmbedding(query);
    const results = await azureSearchClient.search(query, {
      queryType: 'semantic',
      searchFields: ['content', 'title'],
      semanticSearchOptions: {
        configurationName: 'default',
        captions: {
          captionType: 'extractive',
        },
        answers: {
          answerType: 'extractive',
          count: 3,
        },
      },
      vectorSearchOptions: {
        queries: [
          {
            vector: queryVector,
            kNearestNeighborsCount: 5,
            fields: ['contentVector'],
            kind: 'vector',
          },
        ],
      },
    });

    //TODO: What is the limit for score?
    const relevantDocumentKeys = Array.from(
      results.answers?.filter(({ score }) => score >= 0.92)?.flatMap(({ key }) => key),
    );
    if (relevantDocumentKeys.length == 0) {
      return relevantDocuments;
    }

    for await (const result of results.results) {
      //include documents with high score
      if (relevantDocumentKeys.includes((result.document as any).id)) {
        relevantDocuments.push({ ...result.document, caption: result.captions?.[0]?.text });
      }
    }

    return relevantDocuments;
  }

  private async generateEmbedding(text: string) {
    const response = await this.openAiClient.embeddings.create({
      model: this.embeddingModel,
      input: text,
    });
    return response.data[0].embedding;
  }

  async setOrganizationId(organizationId: string): Promise<void> {
    this.nodeService.setOrganizationId(organizationId);
    const allAgents = await this.nodeService.getAllAgents();
    if (allAgents.length === 0) {
      throw new Error('Initialization failed: No agents found.');
    }
    this.agentList = allAgents;
    this.agentIdList = this.agentList.map((d) => d.communicationIdentities && d.communicationIdentities[0].userId);
    this.agentIdList.push(this.aiAssistantId);
  }

  getAgentList(): RelicAgent[] {
    return this.agentList;
  }

  getAgentIdList(): string[] {
    return this.agentIdList;
  }

  getAiAssistantId(): string {
    return this.aiAssistantId;
  }

  async createMessageIntl(message: string, requestMessageIntl: MessageIntl): Promise<JsonString<MessageIntl>> {
    if (requestMessageIntl && requestMessageIntl.locale) {
      const messageText = message
        .replace(/\[.*?\]/g, '') // Remove square brackets and their contents
        .replace(/<\/?[^>]+(>|$)/g, '') // Remove HTML tags
        .replace(/[^\w\s.,!?]/g, '') // Remove special symbols except punctuation
        .trim(); // Trim leading and trailing whitespace
      const { translation } = await this.nodeService.translate(messageText, requestMessageIntl.locale);
      const agentMessageIntl = JSON.stringify({
        locale: requestMessageIntl.locale,
        content: translation ?? message,
      }) as JsonString<MessageIntl>;
      return agentMessageIntl;
    } else {
      return undefined;
    }
  }

  /**
   * Generates a response from the agent.
   * @param threadId The ID of the thread.
   * @param latestMessage The latest message not in the thread yet.
   * @param conversationState The conversation state.
   * @returns The generated response.
   */
  async generateResponse(
    threadId: string,
    latestMessage: string,
    conversationState: ConversationState,
  ): Promise<AgentResponse> {
    const thread = await this.nodeService.getThreadById(threadId);
    await this.setOrganizationId(thread.threadSubject.organizationId);
    const aiAssistantThreadClient = await this.chatClientManager.getChatThreadClientForAiAssistant(threadId);
    const userAcsId = thread.participants
      .map((participant: RelicChatParticipant) => participant.id)
      .map((id: CommunicationUserKind) => id.communicationUserId) // Assuming all participant ids are CommunicationUserKind
      .find((acsId: string) => !this.agentIdList.includes(acsId));

    // Get all messages and topic
    let topic: Topic;
    const allMessages: ChatMessage[] = [];
    try {
      for await (const message of aiAssistantThreadClient.listMessages()) {
        allMessages.push(message as ChatMessage);
      }
      allMessages.sort((a, b) => Number(a.sequenceId) - Number(b.sequenceId)); // Sort by sequenceId (ascending order)
      topic = this.getTopicFromMessages(allMessages);
    } catch (error) {
      this.logger.error({ err: error }, 'Failed to get messages and topic from thread');
      throw new InternalServerErrorException('Failed to get messages and topic from thread');
    }
    //cb1-732: allMessages.at(-1).content.message can return undefined, so we use latestMessage
    //cb1-732: kbSearchQuery is used for searching knowledgebase, if empty, searchknowledgebase is skipped
    const kbSearchQuery = allMessages.at(-1).content.message || latestMessage;

    // Get Agent information
    const agentAcsId = topic.currentAgentAcsId;
    this.currentAgent = await this.nodeService.getAgent(agentAcsId);
    const agentPublicData = this.currentAgent.publicData;
    let systemPrompt = this.currentAgent.azureAssistantSetup.systemPrompt;
    const chatParameters = this.currentAgent.azureAssistantSetup.chatParameters;
    const fewShotExamples = this.currentAgent.azureAssistantSetup.fewShotExamples ?? [];
    const kbLinked = this.currentAgent.relicAssistantSetup.kbLinked;

    //Form agent response
    const agentResponse: AgentResponse = {
      content: '',
      agentAcsId,
      agentPublicData,
      userAcsId,
    };

    // In case of agent switch acceptance, return a hardcoded message to the user
    if (conversationState == 'AfterAgentSwitchAccepted') {
      agentResponse.content = `Hi! How can I assist you today?`;
      return agentResponse;
    }

    // In case of agent switch decline, return a hardcoded message to the user
    if (conversationState == 'AfterAgentSwitchDeclined') {
      agentResponse.content = 'Okay. I am happy to answer any other questions you may have.';
      return agentResponse;
    }

    // Get Patient Detail (if the sender is not Practitioner)
    let patientSummary: string;
    const patientId = thread.threadSubject.threadOwner.id;
    if (thread.threadSubject?.threadOwner?.resourceType === 'Patient') {
      const patientData = await this.nodeService.getPatientData(patientId);
      patientSummary = patientData.summary;
    }

    const promptMessages: ChatCompletionMessageParam[] = [];

    if (kbSearchQuery && kbLinked && kbLinked.length > 0) {
      const relevantDocuments = await this.searchKnowledgeBase(kbSearchQuery);
      if (relevantDocuments.length > 0) {
        const context = relevantDocuments
          .map((doc, index) => `${index + 1}. ${doc.content}` || '')
          .join('\n')
          .trim();
        systemPrompt = `${systemPrompt}\n
        Use RELEVANT_DOCUMENTS to answer the latest question.\n
        RELEVANT_DOCUMENTS:\n${context}
        `;
        agentResponse.citations = this.extractCitations(relevantDocuments);
      }
    }

    // Construct prompts
    const enc = get_encoding('cl100k_base');
    promptMessages.push(createOpenAiMessage('system', systemPrompt));
    for (const example of fewShotExamples) {
      if (example?.userInput) {
        promptMessages.push(createOpenAiMessage('user', example.userInput));
      }
      if (example?.chatbotResponse) {
        promptMessages.push(createOpenAiMessage('assistant', example.chatbotResponse));
      }
    }
    if (patientSummary) {
      promptMessages.push(createOpenAiMessage('user', patientSummary));
    }

    const promptTokenCount = promptMessages.reduce((acc, message) => acc + this.computeTokenCount(message, enc), 0);

    // Select messages
    const messageHistory = allMessages.filter(
      (message) => isChatMessage(message) || isWidgetMessage(message) || isCallMessage(message),
    ); // TODO: review this logic (modified from chat.service.ts)
    const recentMessages: ChatCompletionMessageParam[] = this.constructRecentMessages(
      messageHistory,
      userAcsId,
      enc,
      promptTokenCount + enc.encode(latestMessage).length, // TODO: temporary workaround to add latestMessage later
      conversationState,
    );
    const requestMessages: ChatCompletionMessageParam[] = [...promptMessages, ...recentMessages];

    // For chat case, `latestMessage` is empty string (the latest message is already in thread)
    if (latestMessage && latestMessage.length > 0) {
      requestMessages.push(createOpenAiMessage('user', latestMessage));
    }

    const chatCompletionOptions: ChatCompletionCreateParamsNonStreaming = {
      messages: requestMessages,
      model: this.chatModel,
      frequency_penalty: chatParameters.frequencyPenalty,
      presence_penalty: chatParameters.presencePenalty,
      // max_completion_tokens: chatParameters.maxResponseLength,
      // temperature: chatParameters.temperature,
      top_p: chatParameters.topProbabilities,
      user: patientId,
    };
    // TODO: chatParameters fields not used: maxResponseLength, pastMessagesToInclude, stopSequences
    //console.log('requestMessages', requestMessages);

    try {
      const completions: ChatCompletion = await this.openAiClient.chat.completions.create(chatCompletionOptions);
      const content = completions.choices[0].message.content;

      // Extract and format citations
      // const citations = this.extractCitations(completions);

      agentResponse.content = content.trim();
      // agentResponse.citations = citations.length > 0 ? citations : undefined;

      return agentResponse;
    } catch (error) {
      // this.logger.error({ err: error }, 'Failed to get response from OpenAI');
      this.logger.error(error, 'Failed to get response from OpenAI');
      agentResponse.content = "I'm sorry, I couldn't process your request.";
      return agentResponse;
    }
  }

  async sendRelicMessage(
    threadId: string,
    message: string,
    sender?: RelicChatParticipant | undefined,
    requestForIntl?: MessageIntl,
    metadata?: ChatMetadata,
  ): Promise<string> {
    let agentMessageIntl: JsonString<MessageIntl>;
    const threadSpeechMap = await this.threadSpeechService.getThreadSpeechMap(threadId);
    if (metadata?.callConnectionId && !threadSpeechMap) {
      // if it's call scenario but speechmap is not available, skip sending the message.
      // this happens if there are delays and call is disconnected before agent was able to respond.
      return '';
    }
    if (requestForIntl && !metadata?.messageIntl) {
      agentMessageIntl = await this.createMessageIntl(message, requestForIntl);
    }
    const metadataWithIntl = {
      ...metadata,
      ...(agentMessageIntl !== undefined && { messageIntl: agentMessageIntl }),
    } as ChatMetadata;
    const senderDisplayName = sender ? sender.displayName : this.aiAssistantDisplayName; //always have a display name for agent messsages
    const threadClient = await this.chatClientManager.getChatThreadClientForParticipant(threadId, sender);
    const result = await threadClient.sendMessage(
      { content: message },
      { metadata: metadataWithIntl, senderDisplayName },
    );
    return result.id;
  }

  async generateResponseForCrawler(subject: string, intent: string, url: string, summary: string): Promise<string> {
    try {
      const trainingAssistant = this.getAgentList().find(
        ({ role }) => role['practitioner-role'].toLowerCase() == 'training assistant',
      );
      if (!trainingAssistant) {
        throw new NotFoundException('Training assistant not found.');
      }
      
      //construct prompt
      const messages: ChatCompletionMessageParam[] = [];
      messages.push(createOpenAiMessage('system', trainingAssistant.azureAssistantSetup.systemPrompt));
      trainingAssistant.azureAssistantSetup?.fewShotExamples?.forEach((example) => {
        if (example?.userInput) {
          messages.push(createOpenAiMessage('user', example.userInput));
        }
        if (example?.chatbotResponse) {
          messages.push(createOpenAiMessage('assistant', example.chatbotResponse));
        }
      });
      messages.push(
        createOpenAiMessage(
          'user',
          `      
            Subject: ${subject}\n
            Intent: ${intent}\n
            URL: ${url}\n
            Summary: ${summary}\n
          `,
        ),
      );

      const chatParameters = trainingAssistant.azureAssistantSetup.chatParameters;
      const chatCompletionOptions: ChatCompletionCreateParamsNonStreaming = {
        messages: messages,
        model: this.chatModel,
        frequency_penalty: chatParameters.frequencyPenalty,
        presence_penalty: chatParameters.presencePenalty,
        temperature: chatParameters.temperature,
        top_p: chatParameters.topProbabilities,
      };
      const completions: ChatCompletion = await this.openAiClient.chat.completions.create(chatCompletionOptions);
      return completions.choices[0].message.content;
    } catch (err) {
      this.logger.error({ err }, 'Failed to get response from OpenAI.');
      throw err;
    }
  }

  async sendGreeting(
    threadId: string,
    data?: CallConnectedEventData | AcsChatThreadPropertiesUpdatedEventData,
  ): Promise<string> {
    const threadSpeechMap: ThreadSpeechMap = await this.threadSpeechService.getThreadSpeechMap(threadId);
    let participants: RelicChatParticipant[] = threadSpeechMap?.participants;
    let chatLanguage: RelicCommunicationLanguage = threadSpeechMap?.chatLanguage;
    let onLineParticipant: RelicChatParticipant;
    //If this is not a speech on thread use case.
    if (!threadSpeechMap) {
      const thread: Thread = await this.nodeService.getThreadById(threadId); // Fallback to get participants if speech map is not available
      participants = thread.participants;
      chatLanguage = thread.threadSubject.patientLanguage;
    }
    if (isCallConnectedEventData(data)) {
      onLineParticipant = await this.threadSpeechService.getOnCallParticipant(threadId);
    } else {
      onLineParticipant = participants.find(
        (participant) =>
          getIdentifierRawId(participant.id) === data.editedByCommunicationIdentifier.communicationUser.id,
      );
    }
    if (!participants) {
      throw new Error('Participants not found for the thread.');
    }
    if (!chatLanguage) {
      throw new Error('Chat language not found for the thread.');
    }
    const humanPatient = getHumanPatient(participants);
    const humanPractitioner = getHumanPractitioner(participants);
    const threadAssistant = getThreadAssistant(participants);
    const aiAssistant = getAiAssistant(participants);
    let language: RelicCommunicationLanguage;
    let greeting = '';
    let notification = '';
    //Thread Assistant greeting
    if (humanPatient && onLineParticipant.resourceType === 'Patient') {
      language = chatLanguage ?? humanPatient.chatLanguage;
      greeting = `Hello ${onLineParticipant.displayName}, how can I help you today?`;
      notification = threadAssistant
        ? `Call started with ${threadAssistant.displayName}`
        : `Call started with ${aiAssistant.displayName}`;
    }
    if (humanPractitioner && onLineParticipant.resourceType === 'Practitioner') {
      language = chatLanguage ?? humanPractitioner.chatLanguage;
      greeting = `Hello ${onLineParticipant.displayName}, how can I help you today?`;
      notification = threadAssistant
        ? `Call started with ${threadAssistant.displayName}`
        : `Call started with ${aiAssistant.displayName}`;
    }
    //Interpreter Agent greeting
    if (humanPatient && humanPractitioner && onLineParticipant.speechId) {
      language = chatLanguage ?? humanPatient.chatLanguage;
      greeting = `Hello ${onLineParticipant?.displayName}, this is Relic AI talking on behalf of ${humanPractitioner.displayName}. I will be acting as an interpreter for this call. Whenever I am talking to ${humanPractitioner.displayName}, you will hear waiting music. Do I have your permission to start the session now? Please say Yes to continue.`;
      notification = `Call started with ${onLineParticipant.displayName}. AI Interpereter for ${chatLanguage.display ?? humanPatient.chatLanguage.display} is online.`;
    }
    const metadata: ChatMetadata = { type: 'chat' };
    const mockMessageIntl: MessageIntl = { locale: language.code, content: '' }; // No incoming message, hence simulating incoming locale for agents
    //Send call notification if it's a voice/speech call before sending the greeting
    if (isCallConnectedEventData(data)) {
      metadata.callConnectionId = data?.callConnectionId;
      Promise.allSettled([
        await this.threadSpeechService.updateCallStartTime(threadId),
        await this.sendCallNotificationToThread(threadId, data?.callConnectionId, notification),
      ]);
    }
    return await this.sendRelicMessage(threadId, greeting, threadAssistant, mockMessageIntl, metadata);
  }

  async sendGoodbye(threadId: string, data?: CallDisconnectedEventData): Promise<string> {
    if (!data?.callConnectionId) {
      return '';
    }
    const threadSpeechMap: ThreadSpeechMap = await this.threadSpeechService.getThreadSpeechMap(threadId);
    if (threadSpeechMap && !threadSpeechMap.callStartTime) {
      //If call start notification was not sent, goodbye notification will not be sent.
      return '';
    }
    let notification = threadSpeechMap.lastEvent === 'failure' ? 'Call ended with Error:' : 'Call ended:';
    let messageId = '';
    if (threadSpeechMap) {
      if (threadSpeechMap.callStartTime) {
        notification += calculateDuration(threadSpeechMap.callStartTime, Date.now());
        messageId = await this.sendCallNotificationToThread(threadId, data?.callConnectionId, notification);
      }
      await this.threadSpeechService.deleteThreadSpeechMap(threadId);
    }
    return messageId;
  }

  async getTemplatedMessage(
    threadId: string,
    callConnectionId: string,
    template: 'confirmation' | 'clarification' | 'hangup' | 'custom',
    customMessage?: string,
  ): Promise<ChatMessage | undefined> {
    //return confirmation message only for voice calls
    if (!callConnectionId) {
      return undefined;
    }
    if (template === 'custom' && !customMessage) {
      return undefined;
    }
    const { participants, chatLanguage } = await this.threadSpeechService.getThreadSpeechMap(threadId);
    const onCallParticipant = await this.threadSpeechService.getOnCallParticipant(threadId);
    let secondHumanParticipantName: string;
    if (template === 'confirmation') {
      if (participants) {
        const secondHumanParticipant = participants.find(
          (participant) =>
            participant.resourceId !== onCallParticipant.resourceId &&
            !(
              participant.resourceType === 'Practitioner' &&
              ['Staff Agent', 'Patient Agent', 'System Agent'].includes(participant.type)
            ),
        );
        secondHumanParticipantName = secondHumanParticipant?.displayName;
        if (!secondHumanParticipant) {
          return undefined;
        }
      } else {
        return undefined;
      }
    }
    let message: string;
    let messageIntl: JsonString<MessageIntl>;
    switch (template) {
      case 'confirmation':
        message = `Thanks. Checking with ${secondHumanParticipantName} now. Please wait.`;
        break;
      case 'clarification':
        message = `Sorry, I could not understand that. Could you repeat that again please?`;
        break;
      case 'hangup':
        message = `I was waiting and did not hear anything hence I am ending this session. Thanks!`;
        break;
      case 'custom':
        message = customMessage;
        break;
      default:
        message = '';
    }
    if (chatLanguage.code != 'en-US' && chatLanguage.code != 'en') {
      const requestMessageIntl: MessageIntl = { locale: chatLanguage.code, content: '' };
      //Recognized message is in an international language message
      messageIntl = await this.createMessageIntl(message, requestMessageIntl);
    }
    const messageId = Math.floor(1000000 + Math.random() * 9000000).toString();
    const chatMetadata: ChatMetadata = {
      type: 'chat',
      callConnectionId: callConnectionId,
      messageIntl,
    };
    const templatedMessage: ChatMessage = {
      id: messageId,
      type: 'Text' as ChatMessageType,
      version: messageId,
      sequenceId: messageId, //Does not matter since we are mocking a chat message for voice play.
      content: { message: message },
      metadata: chatMetadata,
      sender: {
        communicationUserId: this.aiAssistantId,
        kind: 'communicationUser',
      },
      senderDisplayName: this.aiAssistantDisplayName,
      createdOn: new Date(),
    };
    return templatedMessage;
  }
}
