import { Injectable, OnModuleInit } from '@nestjs/common';
import { Logger } from '@nestjs/common';
import { NodeService } from 'src/node/node.service';
import { RedisService } from 'src/redis/redis.service';
import { ChatClientManager } from 'src/chat/chat-client-manager.service';
import { RelicChatParticipant, Thread } from 'relic-ui';

@Injectable()
export class ThreadService implements OnModuleInit {
  private readonly logger = new Logger(ThreadService.name);
  constructor(
    private chatClientManager: ChatClientManager,
    private readonly nodeService: NodeService,
    private readonly redisService: RedisService,
  ) {
    this.logger.log('ThreadContextService constructor called');
  }

  onModuleInit() {
    this.logger.log('ThreadContextService initialized');
  }

  async isZeroMessageThread(thread: Thread, threadAssistant: RelicChatParticipant): Promise<boolean> {
    const threadClient = await this.chatClientManager.getChatThreadClientForParticipant(
      thread.threadId,
      threadAssistant,
    );
    for await (const message of threadClient.listMessages()) {
      if (message.type === 'text' || message.type === 'html') {
        return false;
      }
    }
    return true;
  }
}
