import { Injectable, OnModuleInit } from '@nestjs/common';
import { RelicChatParticipant, RelicCommunicationLanguage } from 'relic-ui';
import {
  CommunicationIdentifier,
  CommunicationUserIdentifier,
  PhoneNumberIdentifier,
} from '@azure/communication-common';
import { NodeService } from 'src/node/node.service';
import { RedisService } from 'src/redis/redis.service';
import { Logger } from '@nestjs/common';
import { isEqual } from 'lodash';
import { AcsChatMessageReceivedInThreadEventData } from '@azure/eventgrid';
import { isSpeechMessage } from 'src/utils/message.utils';
import { getThreadAssistant, getMessageSenderParticipant } from 'src/utils/participant.utils';

/**
 * Represents a runtime mapping of speech-related information for a thread.
 * This mapping exists only for the duration of a call made on a thread.
 */
export interface ThreadSpeechMap {
  /**
   * The unique identifier of the thread.
   */
  threadId: string;

  /**
   * A copy of the thread participants with voiceId added. Locale is available in the form of language.
   * Any participant with the voice id is a targetVoiceParticipant. In case of multiple, use ResourceType to segregate.
   */
  participants: RelicChatParticipant[];

  /**
   * The unique identifier of the organization.
   */
  organizationId: string;

  /**
   * The count of speech failures encountered.
   */
  failureCount: number;

  /**
   * The assistant that was called. This assistant will be added to the thread if not already present.
   */
  calleeAgent?: RelicChatParticipant;

  /**
   * The language used for communication in the call / chat.
   */
  chatLanguage?: RelicCommunicationLanguage;

  /**
   * The start time of the call, represented as a timestamp.
   */
  callStartTime?: number;

  /**
   * The last event that occurred in the thread.
   */
  lastEvent?: 'hangup' | 'success' | 'failure';
}

@Injectable()
export class ThreadSpeechService implements OnModuleInit {
  private readonly logger = new Logger(ThreadSpeechService.name);

  constructor(
    private readonly nodeService: NodeService,
    private readonly redisService: RedisService,
  ) {
    this.logger.log('ThreadContextService constructor called');
  }

  onModuleInit() {
    this.logger.log('ThreadContextService initialized');
  }

  async setThreadSpeechMap(
    threadId: string,
    onCallParticipantIdentity: CommunicationIdentifier,
    onCallParticipantPhone: PhoneNumberIdentifier,
  ): Promise<ThreadSpeechMap> {
    //Obtain thread
    const thread = await this.nodeService.getThreadById(threadId);
    //Make onCallParticipant a speechParticipant by assigning SpeechId
    const onCallParticipant = thread.participants.find((participant) => {
      const onCallParticipantId: CommunicationIdentifier = {
        communicationUserId: (onCallParticipantIdentity as CommunicationUserIdentifier).communicationUserId,
      };
      return isEqual(participant.id, onCallParticipantId);
    });
    if (!onCallParticipant) {
      throw new Error(`On call participant not found in thread.`);
    } else {
      onCallParticipant.speechId = onCallParticipantPhone ?? onCallParticipantIdentity;
    }
    //Update speech participants in thread participants array.
    const updatedParticipants = thread.participants.map((participant) => {
      if (isEqual(participant.id, onCallParticipant.id)) {
        return onCallParticipant;
      }
      return participant;
    });
    const calleeAgent = getThreadAssistant(thread.participants);
    const threadSpeechMap: ThreadSpeechMap = {
      threadId: threadId,
      participants: updatedParticipants,
      organizationId: thread.threadSubject.organizationId,
      failureCount: 0,
      calleeAgent: calleeAgent,
      chatLanguage: thread.threadSubject.patientLanguage,
      // callStartTime: Date.now(),
      lastEvent: 'success',
    };
    this.redisService.setObject(threadId, threadSpeechMap);
    return threadSpeechMap;
  }

  async getThreadSpeechMap(threadId: string): Promise<ThreadSpeechMap> {
    return await this.redisService.getObject(threadId);
  }

  async deleteThreadSpeechMap(threadId: string): Promise<void> {
    await this.redisService.delObject(threadId);
  }

  async updateCallStartTime(threadId: string): Promise<void> {
    const threadSpeechMap = await this.getThreadSpeechMap(threadId);
    if (threadSpeechMap) {
      await this.redisService.setObject(threadId, { ...threadSpeechMap, callStartTime: Date.now() });
    }
  }

  async updateLastEvent(threadId: string, event: 'hangup' | 'success' | 'failure'): Promise<void> {
    const threadSpeechMap = await this.getThreadSpeechMap(threadId);
    // No further updates to the lastEvent after hangup event is received.
    if (threadSpeechMap && threadSpeechMap.lastEvent !== 'hangup') {
      await this.redisService.setObject(threadId, { ...threadSpeechMap, lastEvent: event });
    }
  }

  async incrementFailureCount(threadId: string): Promise<void> {
    const threadSpeechMap = await this.getThreadSpeechMap(threadId);
    if (threadSpeechMap) {
      const failureCount = threadSpeechMap.failureCount + 1;
      await this.redisService.setObject(threadId, { ...threadSpeechMap, failureCount: failureCount });
    }
  }

  async getOnCallParticipant(threadId: string): Promise<RelicChatParticipant> {
    const participants = (await this.getThreadSpeechMap(threadId)).participants;
    if (!participants || participants.length === 0) {
      throw new Error(`ThreadSpeechMap is not yet ready for thread ${threadId}`);
    }
    const onCallParticipant = participants.find((participant) => participant.speechId);
    if (!onCallParticipant) {
      throw new Error(`On call participant not found in thread ${threadId}`);
    }
    return onCallParticipant;
  }

  async isFromOnCallParticipant(message: AcsChatMessageReceivedInThreadEventData): Promise<boolean> {
    if (!isSpeechMessage(message)) {
      return false;
    }
    const onCallParticipant = await this.getOnCallParticipant(message.threadId);
    const participants = (await this.getThreadSpeechMap(message.threadId)).participants;
    const messageSender = getMessageSenderParticipant(participants, message);
    if (onCallParticipant && messageSender) {
      return isEqual(onCallParticipant.resourceId, messageSender.resourceId);
    }
    return false;
  }
}
