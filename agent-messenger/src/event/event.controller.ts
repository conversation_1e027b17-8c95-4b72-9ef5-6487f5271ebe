import { Controller, Post, Body, Param, HttpCode } from '@nestjs/common';
import { Logger } from '@nestjs/common';
import type {
  EventGridEvent,
  AcsChatMessageReceivedInThreadEventData,
  AcsChatThreadPropertiesUpdatedEventData,
  AcsIncomingCallEventData,
} from '@azure/eventgrid';
import { ChatService } from '../chat/chat.service';
import { CallService } from '../call/call.service';
import { ThreadSpeechService } from 'src/thread/thread.speech';
import { ChatClientManager } from '../chat/chat-client-manager.service';
import { isSpeechMessage, isNotification } from 'src/utils/message.utils';

function isChatMessageReceivedEvent(
  event: EventGridEvent<any>,
): event is EventGridEvent<AcsChatMessageReceivedInThreadEventData> {
  return event.eventType === 'Microsoft.Communication.ChatMessageReceivedInThread';
}

function isChatThreadPropertiesUpdatedEvent(
  event: EventGridEvent<any>,
): event is EventGridEvent<AcsChatThreadPropertiesUpdatedEventData> {
  return event.eventType === 'Microsoft.Communication.ChatThreadPropertiesUpdated';
}

function isOutgoingCallEvent(event: any): event is any {
  // TODO: implement event data validation
  return true;
}

function isIncomingCallEvent(
  event: EventGridEvent<any>,
): event is EventGridEvent<AcsIncomingCallEventData> {
  return event.eventType === 'Microsoft.Communication.IncomingCall';
}

function extractMessageType(event: EventGridEvent<AcsChatMessageReceivedInThreadEventData>): string {
  return event.data?.metadata?.type;
}

@Controller('event')
export class EventController {
  private readonly logger = new Logger(EventController.name);

  constructor(
    private readonly chatService: ChatService,
  ) {}

  @Post()
  @HttpCode(200)
  async handleEventGridEvent(@Body() event: EventGridEvent<any>[]): Promise<any> {
    const receivedEvent = event[0];

    if (receivedEvent?.data?.validationCode) {
      return { validationResponse: receivedEvent.data.validationCode };
    }
    this.logger.log(receivedEvent.eventType, 'Event received: ');

    try {
      if (isChatMessageReceivedEvent(receivedEvent)) {
        this.logger.log('Event: Message was received in thread through chat handler.');
        const chatMessage = receivedEvent.data as AcsChatMessageReceivedInThreadEventData;
        // No processing for Notification messages. They are display only.
        if (isNotification(chatMessage)) {
          this.logger.log('Event: Message is a notification message. Igoring it.');
          return { received: true };
        }
        // No processing for Speech messages. This is chat service event handler.
        if (isSpeechMessage(chatMessage)) {
          this.logger.log('Event: Message is a speech message. Ignoring it.');
          return { received: true };
        }
        this.logger.log('Event: Message received. Processing as a regular chat message.');
        //Chat service will only process messages not sent by AI Assistants.
        await this.chatService.processChatMessageReceivedInThread(chatMessage);
      }
      if (isChatThreadPropertiesUpdatedEvent(receivedEvent)) {
        await this.chatService.maybeWelcome(
          receivedEvent.data as AcsChatThreadPropertiesUpdatedEventData,
        );
      }
      this.logger.warn(
        `Event: ${receivedEvent.eventType} is not supported by Chat Listener. ${receivedEvent.data.messageBody} message will not be processed.`,
      );
    } catch (error) {
      this.logger.error(error, 'Event: AI Asst failure: ');
    }
    return { received: true };
  }
}

@Controller('call')
export class CallEventController {
  private readonly logger = new Logger(CallEventController.name);

  constructor(
    private readonly callService: CallService,
    private readonly chatService: ChatService,
    private readonly threadContextService: ThreadSpeechService,
  ) {}

  @Post('incoming')
  async handleIncomingCallEvent(@Body() event: EventGridEvent<any>[]) {
    const receivedEvent = event[0];

    if (receivedEvent?.data?.validationCode) {
      return { validationResponse: receivedEvent.data.validationCode };
    }
    try {
      if (isIncomingCallEvent(receivedEvent)) {
        this.logger.log({
          message: 'Call: Incoming call event received.',
          data: receivedEvent.data,
        })
        //For web calls, thread Id is passed through custom context.
        const { customContext } = receivedEvent.data;
        const threadId: string = customContext ? customContext.sipHeaders['User-To-User'] as string : 'NEW_THREAD';
        this.callService.handleIncomingCall(threadId, receivedEvent.data);
      }
      if (isChatMessageReceivedEvent(receivedEvent)) {
        this.logger.log('Call: Message was received in thread through call handler.');
        const chatMessage = receivedEvent.data as AcsChatMessageReceivedInThreadEventData;
        const isFromOnCallParticipant = await this.threadContextService.isFromOnCallParticipant(chatMessage);
        if (!isSpeechMessage(chatMessage)) {
          this.logger.log('Call: Message is not a speech message. Ignoring it.');
          return { received: true };
        }
        if (isNotification(chatMessage)) {
          this.logger.log('Call: Message is a notification message. Igoring it.');
          return { received: true };
        }
        if (isSpeechMessage(chatMessage) && !isFromOnCallParticipant) {
          this.logger.log('Call: Message from non on-call participant.');
          await this.callService.playVoiceMessage(chatMessage.threadId, chatMessage);
        } else {
          this.logger.log('Call: Message from chat or on-call participant. Can be for the assistant.');
          //Chat service will only process messages not sent by AI Assistants.
          await this.chatService.processChatMessageReceivedInThread(chatMessage);
        }
      }
      if (isChatThreadPropertiesUpdatedEvent(receivedEvent)) {
        await this.chatService.maybeWelcome(
          receivedEvent.data as AcsChatThreadPropertiesUpdatedEventData,
        );
      }
    } catch (error) {
      this.logger.error(error, 'Call: Call Event failure: ');
      if (receivedEvent.data?.metadata?.callConnectionId) {
        await this.callService.playVoiceError(
          receivedEvent.data.threadId,
          'Oops, something went wrong. Can you please repeat?',
          receivedEvent.data,
        );
      }
    }

    return { received: true };
  }

  @Post('callbacks/:threadId')
  async handleCallback(@Param('threadId') threadId: string, @Body() body: any) {
    this.callService.handleCallback(threadId, body[0]?.data);
  }

  @Post('outgoing')
  async handleOutgoingCallEvent(@Body() event: EventGridEvent<any>[]) {
    const receivedEvent = event[0];

    if (receivedEvent?.data?.validationCode) {
      return { validationResponse: receivedEvent.data.validationCode };
    }

    if (isOutgoingCallEvent(receivedEvent)) {
      const { threadId } = receivedEvent.data;
      this.callService.handleOutgoingCall(threadId, receivedEvent.data);
    }

    return { received: true };
  }

  @Post('hang-up/:threadId/:callConnectionId')
  async handleHangUpCallEvent(
    @Param('threadId') threadId: string,
    @Param('callConnectionId') callConnectionId: string,
  ): Promise<any> {
    try {
      await this.callService.handleCallHangup(threadId, { callConnectionId: callConnectionId });
      return { message: 'Call hang-up initiated successfully.', threadId };
    } catch (error) {
      this.logger.error(`Error during call hang-up for thread ID: ${threadId}`, error.stack);
      return { message: 'Failed to initiate call hang-up.', threadId, error: error.message };
    }
  }
}
