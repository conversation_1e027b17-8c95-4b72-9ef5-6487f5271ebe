/* eslint-disable  @typescript-eslint/no-unused-vars */
import { Injectable, OnModuleInit, NotFoundException } from '@nestjs/common';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  PhoneNumberIdentifier,
  CommunicationIdentifier,
  CommunicationIdentifierKind,
  SerializedCommunicationIdentifier,
  deserializeCommunicationIdentifier,
} from '@azure/communication-common';
import { ChatClientManager } from '../chat/chat-client-manager.service';
import { AgentService } from '../agent/agent.service';
import { NodeService } from '../node/node.service';
import {
  CallAutomationClient,
  CallMedia,
  TextSource,
  FileSource,
  CallMediaRecognizeSpeechOptions,
  PlayOptions,
} from '@azure/communication-call-automation';
import type {
  CallIntelligenceOptions,
  CreateCallOptions,
  CallInvite,
  AnswerCallOptions,
  CallConnectionProperties,
} from '@azure/communication-call-automation';
import type { AcsChatMessageReceivedInThreadEventData } from '@azure/eventgrid';
import type { ChatMetadata, MessageIntl, ChatMessage } from 'relic-ui';
import {
  CallConnectedEventData,
  RecognizeCompletedEventData,
  RecognizeFailedEventData,
  CallDisconnectedEventData,
  isCallConnectedEventData,
  isParticipantsUpdatedEventData,
  isRecognizeCompletedEventData,
  isRecognizeFailedEventData,
  isCallDisconnectedEventData,
  isPlayCompletedEventData,
  isPlayStartedEventData,
  isPlayFailedEventData,
  PlayCompletedEventData,
} from '../types';
import { split } from 'lodash';
import { ThreadSpeechService, ThreadSpeechMap } from 'src/thread/thread.speech';
import { ChatMessageType } from '@azure/communication-chat';
import { getMessageSenderParticipant } from 'src/utils/participant.utils';
import { ChatService } from 'src/chat/chat.service';

/**
 * Data types and definitions
 */

// Operation context for speech recognition (to be finalized after better understanding on how context flows across events.)
export enum OperationContext {
  PatientAudience = 'Patient',
  PractitionerAudience = 'Practitioner',
}

interface OutgoingCallData {
  threadId: string;
  targetPhoneNumber: string;
  calleeId: CommunicationIdentifier;
}

function mapLocaleToVoice(locale: string): string {
  switch (locale) {
    case 'ar':
      return 'ar-AE-FatimaNeural';
    case 'hy':
      return 'hy-AM-AnahitNeural';
    case 'bn':
      return 'bn-IN-TanishaaNeural';
    case 'bg':
      return 'bg-BG-KalinaNeural';
    case 'my':
      return 'my-MM-NilarNeural';
    case 'yue':
      return 'yue-CN-XiaoMinNeural';
    case 'zh':
      return 'zh-CN-XiaoxiaoNeural';
    case 'en':
      return 'en-US-AvaNeural';
    case 'en-US':
      return 'en-US-AvaNeural';
    case 'fil':
      return 'fil-PH-BlessicaNeural';
    case 'fr':
      return 'fr-FR-DeniseNeural';
    case 'hi':
      return 'hi-IN-SwaraNeural';
    case 'it':
      return 'it-IT-ElsaNeural';
    case 'ja':
      return 'ja-JP-NanamiNeural';
    case 'ko':
      return 'ko-KR-SunHiNeural';
    case 'fa':
      return 'fa-IR-DilaraNeural';
    case 'pa': //Punjabi
      return 'pa-IN-KaranNeural';
    case 'ru':
      return 'ru-RU-SvetlanaNeural';
    case 'es':
      return 'es-MX-DaliaNeural';
    case 'th':
      return 'th-TH-PremwadeeNeural';
    case 'vi':
      return 'vi-VN-HoaiMyNeural';
    default:
      return 'en-US-BrianNeural';
  }
}

function mapIsoCodeToSpeechLocale(isoLanguageCode: string): string {
  switch (isoLanguageCode) {
    case 'ar':
      return 'ar-AE';
    case 'hy':
      return 'hy-AM';
    case 'bn':
      return 'bn-IN';
    case 'bg':
      return 'bg-BG';
    case 'my':
      return 'my-MM';
    case 'yue':
      return 'yue-CN';
    case 'zh':
      return 'zh-CN';
    case 'en':
      return 'en-US';
    case 'fil':
      return 'fil-PH';
    case 'fr':
      return 'fr-FR';
    case 'hi':
      return 'hi-IN';
    case 'it':
      return 'it-IT';
    case 'ja':
      return 'ja-JP';
    case 'ko':
      return 'ko-KR';
    case 'fa':
      return 'fa-IR';
    case 'pa':
      return 'pa-IN';
    case 'ru':
      return 'ru-RU';
    case 'es':
      return 'es-MX';
    case 'th':
      return 'th-TH';
    case 'vi':
      return 'vi-VN';
    default:
      return isoLanguageCode;
  }
}

/**
 * CallService Implementation
 */

@Injectable()
export class CallService implements OnModuleInit {
  private readonly logger = new Logger(CallService.name);

  private callAutomationClient: CallAutomationClient;
  private callIntelligenceOptions: CallIntelligenceOptions;
  private agentMessengerUrl: string;
  private localSpeechMap: ThreadSpeechMap = null;

  constructor(
    private configService: ConfigService,
    private nodeService: NodeService,
    private agentService: AgentService,
    private chatService: ChatService,
    private chatClientManager: ChatClientManager,
    private threadSpeechService: ThreadSpeechService,
  ) {}

  async initialize(): Promise<void> {
    this.agentMessengerUrl = (await this.nodeService.getConfig('SERVER.AGENTMESSENGER_URL')).value;
    if (!this.agentMessengerUrl) {
      this.chatClientManager.handleError(
        '',
        new Error('Agent Messenger URL is missing or incorrect.'),
        'Initialization Failure. ',
      );
    }
    const acsEndpoint = (await this.nodeService.getConfig('ACS.ENDPOINT')).value;
    const acsAccessKey = (await this.nodeService.getConfig('ACS.ACCESS_KEY')).value;
    if (acsEndpoint && acsAccessKey) {
      this.callAutomationClient = new CallAutomationClient(`endpoint=${acsEndpoint};accesskey=${acsAccessKey}`);
    } else {
      this.chatClientManager.handleError(
        '',
        new Error('Communication Services connection string is missing or incorrect.'),
        'Initialization Failure. ',
      );
    }
    this.callIntelligenceOptions = {
      cognitiveServicesEndpoint: (await this.nodeService.getConfig('AZURE.COGNITIVE_SERVICE_ENDPOINT')).value,
    };
    this.logger.log('Call Service has been initialized.');
  }

  async onModuleInit(): Promise<void> {
    await this.initialize();
  }

  // Core method - Estimates speaking time based on the Text to be spoken. Needs prompt.text to be supported.
  private estimateSpeakingTime(playPrompts: TextSource[]): number {
    const textToSpeak = playPrompts.map((prompt) => prompt.text).join(' ');
    const averageWPM = 150; // Average speaking rate in words per minute (WPM)
    const wordsToSpeak = split(textToSpeak, /\s+/); // Split the text into words
    const speakingTimeInSeconds = Math.round((wordsToSpeak.length / averageWPM) * 60); // Calculate speaking time in seconds
    return speakingTimeInSeconds;
  }

  // Core method - Chunks play prompts as TextSource[] based on message length being bigger than maxPlayLength.
  // The voiceName parameter specifies the voice to be used for text-to-speech playback.  
  // It is included in the TextSource objects returned by this method.  
  // Done for English only. Can be extended for other languages if needed.
  private getPlayPrompts(message: string, messageIntl: string, locale: string, voiceName: string, maxPlayLength: number = 400): TextSource[] {
    // this.logger.log(message, 'Original Message to play');
    const txtMessage: string = message
      .replace(/\[.*?\]/g, '') // Remove square brackets and their contents
      .replace(/<\/?[^>]+(>|$)/g, '') // Remove HTML tags
      .replace(/[^\w\s.,!?]/g, '') // Remove special symbols except punctuation
      .replace(/:/g, '.') // Replace colons with full stops
      .trim(); // Trim leading and trailing whitespace
    const txtMessageIntl: string = messageIntl ? messageIntl : '';
    const textToPlay = locale === 'en' || locale === 'en-US' ? txtMessage : txtMessageIntl; // Play can be in English or non-English.
    // this.logger.log(txtMessage, 'Text to play');
    // this.logger.log(txtMessageIntl, 'Text to play Intl');
    // this.logger.log(textToPlay, 'Text to play identified');
    const playPrompts: TextSource[] = [];
    let lastIndex = 0;
    if (textToPlay.length > maxPlayLength && (locale === 'en' || locale === 'en-US')) {
      //Chunking done for long English Messages only
      while (lastIndex < textToPlay.length) {
        let nextIndex = textToPlay.lastIndexOf('.', lastIndex + maxPlayLength);
        if (nextIndex === -1 || nextIndex <= lastIndex) {
          nextIndex = Math.min(lastIndex + maxPlayLength, textToPlay.length);
        } else {
          nextIndex += 1; // Include the full stop in the chunk
        }
        const chunkText = textToPlay.substring(lastIndex, nextIndex).trim();
        const chunkTextSource: TextSource = {
          voiceName: voiceName,
          kind: 'textSource',
          text: chunkText,
        };
        playPrompts.push(chunkTextSource);
        lastIndex = nextIndex;
      }
    } else {
      const textSource: TextSource = {
        voiceName: voiceName,
        kind: 'textSource',
        text: textToPlay,
      };
      playPrompts.push(textSource);
    }
    return playPrompts;
  }

  // Core Method - Detects if the speech text contains an intent to end the call.
  private async detectEndCallIntent(speechText: string): Promise<boolean> {
    const endCallPhrases = ['no further assistance needed', "that's all", 'good bye', 'bye, bye', 'bye bye', 'goodbye'];
    return endCallPhrases.some((phrase) => speechText.toLowerCase().includes(phrase));
  }

  // Core Method - Handles the call connected event by sending a greeting message on the thread.
  private async handleCallConnected(threadId: string, callBackData: CallConnectedEventData): Promise<void> {
    Promise.allSettled([await this.agentService.sendGreeting(threadId, callBackData)]);
  }

  // Call Provider method - Used for mapping thread to a call.
  private async setupThreadForCall(
    threadId: string,
    onCallParticipantIdentity?: CommunicationIdentifier,
    onCallParticipantPhone?: PhoneNumberIdentifier,
  ): Promise<void> {
    if (!threadId) {
      throw new Error(`Cannnot continue call without a thread id.`);
    }
    if (onCallParticipantIdentity) {
      this.localSpeechMap = await this.threadSpeechService.setThreadSpeechMap(
        threadId,
        onCallParticipantIdentity,
        onCallParticipantPhone,
      );
      return;
    }
    this.localSpeechMap = await this.threadSpeechService.getThreadSpeechMap(threadId);
  }

  // Call Provider method - Play a message to the call media and by default listen.
  private async playMessage(
    threadId: string,
    chatMessage: ChatMessage,
    listenAfterPlay: boolean = true,
    loopMusic: boolean = false,
  ): Promise<void> {
    const { callConnectionId, messageIntl } = chatMessage?.metadata;
    const { message } = chatMessage.content;
    const { chatLanguage } = this.localSpeechMap;
    const onCallParticipant = await this.threadSpeechService.getOnCallParticipant(threadId);
    if (!callConnectionId) {
      throw new Error('Cannot play speech message without an active connection.');
    }
    if (!message) {
      //No message to play
      return;
    }
    const callConnection = this.callAutomationClient.getCallConnection(callConnectionId);
    const callMedia: CallMedia = callConnection.getCallMedia();
    const messageIntlText = messageIntl ? (JSON.parse(messageIntl) as MessageIntl).content : '';
    const voiceName = mapLocaleToVoice(chatLanguage.code);
    const playPrompts = this.getPlayPrompts(message, messageIntlText, chatLanguage.code, voiceName);
    // this.logger.log(playPrompts, 'Play Prompts');

    //Speech will be recognized for onCallParticipant. In the langauge of chatLanguage of the thread.
    const listenInSpeechLocale: string =
      chatLanguage.code === 'en' || chatLanguage.code === 'en-US'
        ? 'en-US'
        : mapIsoCodeToSpeechLocale(chatLanguage.code);

    const estimatedSpeakingTime: number = this.estimateSpeakingTime(playPrompts);

    // this.logger.log(estimatedSpeakingTime, 'Estimated speaking time');
    // this.logger.log(listenInSpeechLocale, 'Listening in language');

    const playOptions: PlayOptions = { operationContext: 'temp' };
    const onResponse = async (response: any) => {
      if (response) {
        this.logger.log(JSON.stringify(response, null, 2), 'received response between the play and recognition');
      }
    };
    const recognizeOptions: CallMediaRecognizeSpeechOptions = {
      operationContext: 'temp',
      playPrompts: playPrompts,
      speechLanguage: listenInSpeechLocale,
      interruptCallMediaOperation: true,
      interruptPrompt: true,
      endSilenceTimeoutInSeconds: 2,
      initialSilenceTimeoutInSeconds: 15 + estimatedSpeakingTime,
      onResponse: onResponse,
      kind: 'callMediaRecognizeSpeechOptions',
    };
    const playMusic: FileSource = await this.nodeService.getCallMusic();
    await callMedia.cancelAllOperations();
    if (listenAfterPlay) {
      //Play the message and start listening
      await callMedia.startRecognizing(onCallParticipant.speechId, recognizeOptions);
    } else {
      //Play the message
      await callMedia.playToAll(playPrompts, playOptions);
      if (loopMusic) {
        //Followed by looping music
        playOptions.loop = true;
        await callMedia.playToAll([playMusic], playOptions);
      }
    }
  }

  // Call Provider method - STT recognition success handler.
  private async handleRecognizeCompletedEvent(
    threadId: string,
    callBackData: RecognizeCompletedEventData,
  ): Promise<void> {
    const recognizedMessage = callBackData.speechResult.speech;
    if (recognizedMessage) {
      const confirmationMessage = await this.agentService.getTemplatedMessage(
        threadId,
        callBackData.callConnectionId,
        'confirmation',
      );
      if (confirmationMessage) {
        await Promise.allSettled([
          this.playMessage(threadId, confirmationMessage, false, true),
          this.chatService.sendRecognizedMessage(threadId, callBackData.callConnectionId, recognizedMessage),
        ]);
      } else {
        await Promise.allSettled([
          this.chatService.sendRecognizedMessage(threadId, callBackData.callConnectionId, recognizedMessage),
        ]);
      }
    } else {
      //Treat empty voice message as a recognize failure
      const recognizeFailEventData: RecognizeFailedEventData = {
        operationContext: callBackData.operationContext,
        resultInformation: callBackData.resultInformation,
        version: callBackData.version,
        callConnectionId: callBackData.callConnectionId,
        serverCallId: callBackData.serverCallId,
        correlationId: callBackData.correlationId,
        publicEventType: 'Microsoft.Communication.RecognizeFailed',
      };
      this.handleRecognizeFailedEvent(threadId, recognizeFailEventData);
    }
  }

  // Call Provider method - STT recognition failure handler.
  private async handleRecognizeFailedEvent(threadId: string, callBackData: RecognizeFailedEventData): Promise<void> {
    const { failureCount } = this.localSpeechMap;
    let listen: boolean = false;
    let message: ChatMessage;
    if (failureCount < 1) {
      // Update failure count in Redis
      await this.threadSpeechService.incrementFailureCount(threadId);
      listen = true;
      message = (await this.agentService.getTemplatedMessage(
        threadId,
        callBackData.callConnectionId,
        'clarification',
      )) as ChatMessage;
    } else {
      await this.threadSpeechService.updateLastEvent(threadId, 'hangup');
      listen = false;
      message = (await this.agentService.getTemplatedMessage(
        threadId,
        callBackData.callConnectionId,
        'hangup',
      )) as ChatMessage;
    }
    await this.playMessage(threadId, message, listen, false);
  }

  // Call Provider method - Play completed event handler.
  private async handlePlayCompletedEvent(threadId: string, callBackData: PlayCompletedEventData): Promise<void> {
    if (this.localSpeechMap.lastEvent === 'hangup') {
      await this.handleCallHangup(threadId, callBackData);
    }
  }

  // Call Provider method - call disconnected event handler.
  private async handleCallDisconnectedEvent(threadId: string, callBackData: CallDisconnectedEventData): Promise<void> {
    Promise.allSettled([await this.agentService.sendGoodbye(threadId, callBackData)]);
  }

  // Call Provider Public method - Process IncomingCall event
  async handleIncomingCall(threadId: string, data: any): Promise<void> {
    const { incomingCallContext, from } = data;

    // Ensure that the 'from' object and 'communicationUser' are valid
    if (!from || !from.communicationUser || !from.communicationUser.id) {
      this.logger.error('Invalid "from" object or missing communicationUser.id');
      return;
    }
    // Obtain caller from incoming call event
    const caller = deserializeCommunicationIdentifier(
      from as SerializedCommunicationIdentifier,
    ) as CommunicationIdentifier;

    await this.setupThreadForCall(threadId, caller);
    let callConnectionProperties: CallConnectionProperties;

    try {
      // TBD - Operation Context should be decided by who is calling
      const answerCallOptions: AnswerCallOptions = {
        callIntelligenceOptions: this.callIntelligenceOptions,
        operationContext: OperationContext.PractitionerAudience,
      };

      const callbackUri = `${this.agentMessengerUrl}/call/callbacks/${encodeURIComponent(threadId)}`;

      // Answer the incoming call
      ({ callConnectionProperties } = await this.callAutomationClient.answerCall(
        incomingCallContext,
        callbackUri,
        answerCallOptions,
      ));
    } catch (error) {
      if (this.callAutomationClient && callConnectionProperties?.callConnectionId) {
        this.handleCallHangup(threadId, { callConnectionId: callConnectionProperties?.callConnectionId });
      }
      this.logger.error(error, `Error handling incoming call for thread ${threadId}:`);
      await this.chatClientManager.handleError(threadId, error as Error, 'Incoming call failure.');
    }
  }

  // Call Provider Public method - Process OutgoingCall event
  async handleOutgoingCall(threadId: string, data: OutgoingCallData): Promise<void> {
    const { targetPhoneNumber, calleeId } = data;
    const calleePhone: PhoneNumberIdentifier = { phoneNumber: targetPhoneNumber };
    const callee = calleeId as CommunicationIdentifier;
    let callConnectionProperties: CallConnectionProperties;
    await this.setupThreadForCall(threadId, callee, calleePhone);

    const aiAsstPhoneNumber = (await this.nodeService.getConfig('ACS.PHONENUMBER')).value;
    const fromPhone: PhoneNumberIdentifier = { phoneNumber: aiAsstPhoneNumber };

    this.logger.log(`Initiating an outgoing call to ${targetPhoneNumber} for thread ${threadId}`);

    try {
      const callInvite: CallInvite = {
        targetParticipant: calleePhone,
        sourceCallIdNumber: fromPhone,
      };

      const createCallOptions: CreateCallOptions = {
        callIntelligenceOptions: this.callIntelligenceOptions,
      };

      const callbackUri = `${this.agentMessengerUrl}/call/callbacks/${encodeURIComponent(threadId)}`;
      ({ callConnectionProperties } = await this.callAutomationClient.createCall(
        callInvite,
        callbackUri,
        createCallOptions,
      ));
    } catch (error) {
      this.logger.error(error, 'Error initiating outgoing call:');
      if (this.callAutomationClient && callConnectionProperties?.callConnectionId) {
        this.handleCallHangup(threadId, { callConnectionId: callConnectionProperties?.callConnectionId });
      }
      Promise.allSettled([
        this.chatClientManager.handleError(threadId, error as Error, 'Outgoing call failure. '),
        this.playVoiceError(threadId, 'Sorry, something went wrong. Can you try again?', {
          operationContext: OperationContext.PractitionerAudience,
        }),
      ]);
    }
  }

  // Call Provider Public method - Process CallHangup event
  async handleCallHangup(threadId: string, data?: PlayCompletedEventData | any): Promise<void> {
    try {
      await this.setupThreadForCall(threadId);
      this.chatClientManager.updateTopic(threadId, `Disconnecting the call...`);
      const callConnectionId = data?.callConnectionId;
      if (!callConnectionId) {
        throw new Error(`No call connection Id found for Thread Id: ${threadId}`);
      }

      const currentCall = this.callAutomationClient.getCallConnection(callConnectionId);
      if (!currentCall) {
        throw new Error(`No call found for connection ID: ${callConnectionId}`);
      }
      await currentCall.hangUp(true);
    } catch (error) {
      await this.threadSpeechService.updateLastEvent(threadId, 'failure');
      await this.chatClientManager.handleError(threadId, error as Error, 'Call hangup failure. ');
    }
  }

  // Call Provider Public method - Process ChatMessageReceivedInThread event
  async playVoiceMessage(threadId: string, data: AcsChatMessageReceivedInThreadEventData): Promise<void> {
    try {
      await this.setupThreadForCall(threadId);
      const chatMessage: ChatMessage = {
        id: data.messageId,
        type: data.type as ChatMessageType,
        version: data.messageId,
        sequenceId: data.messageId, //Does not matter since we are mocking a chat message for voice play.
        content: { message: data.messageBody },
        metadata: data.metadata as ChatMetadata,
        sender: data.senderCommunicationIdentifier as CommunicationIdentifierKind,
        senderDisplayName: data.senderDisplayName,
        createdOn: new Date(data.composeTime),
      };
      //Who is the message sender and who is on the call.
      const messageSender = getMessageSenderParticipant(this.localSpeechMap.participants, data);
      const onCallParticipant = await this.threadSpeechService.getOnCallParticipant(threadId);
      //If the message is from on call participant then do not play it.
      if (onCallParticipant.resourceId === messageSender.resourceId) {
        return;
      }
      Promise.allSettled([
        await this.playMessage(threadId, chatMessage),
        this.chatClientManager.updateTopic(threadId, `${onCallParticipant?.displayName} on the call...`),
      ]);
    } catch (error) {
      this.logger.error(error, 'Voice service failure during playVoiceMessage: ');
    }
  }

  // Call Provider Public method - Voice Error Handler
  async playVoiceError(threadId: string, voiceMessage: string, eventData: any): Promise<void> {
    await this.setupThreadForCall(threadId);
    voiceMessage = voiceMessage ?? 'Sorry, I could not understand that. Can you repeat please?';
    const callConnectionId = eventData?.callConnectionId ?? eventData?.metadata?.callConnectionId;
    const message = await this.agentService.getTemplatedMessage(threadId, callConnectionId, 'custom', voiceMessage);
    await this.playMessage(threadId, message);
  }

  // Call Provider Public method - Process Callback events. Call Provider architecture dependent.
  async handleCallback(threadId: string, callBackData: any): Promise<void> {
    try {
      await this.setupThreadForCall(threadId);
      const onCallParticipant = await this.threadSpeechService.getOnCallParticipant(threadId);
      if (isParticipantsUpdatedEventData(callBackData)) {
        this.logger.log('Callback: ParticipantsUpdated event received.');
      } else if (isCallConnectedEventData(callBackData)) {
        this.logger.log('Callback: Call connected event received');
        Promise.allSettled([
          this.handleCallConnected(threadId, callBackData),
          this.chatClientManager.updateTopic(threadId, `${onCallParticipant.displayName} is on the call...`),
        ]);
        await this.threadSpeechService.updateLastEvent(threadId, 'success');
      } else if (isRecognizeCompletedEventData(callBackData)) {
        this.logger.log(callBackData, 'Callback: Recognize completed event received');
        Promise.allSettled([
          this.handleRecognizeCompletedEvent(threadId, callBackData),
          this.chatClientManager.updateTopic(threadId, 'Processing...'),
        ]);
        await this.threadSpeechService.updateLastEvent(threadId, 'success');
      } else if (isRecognizeFailedEventData(callBackData)) {
        this.logger.log('Callback: Recognize failed event received');
        await this.handleRecognizeFailedEvent(threadId, callBackData);
        await this.threadSpeechService.updateLastEvent(threadId, 'success');
      } else if (isCallDisconnectedEventData(callBackData)) {
        this.logger.log('Callback: Call disconnected received');
        await this.handleCallDisconnectedEvent(threadId, callBackData);
        await this.threadSpeechService.updateLastEvent(threadId, 'success');
      } else if (isPlayCompletedEventData(callBackData)) {
        this.logger.log('Callback: Play Completed received');
        await this.handlePlayCompletedEvent(threadId, callBackData);
        await this.threadSpeechService.updateLastEvent(threadId, 'success');
      } else if (isPlayStartedEventData(callBackData)) {
        this.logger.log('Callback: Play Started received');
      } else if (isPlayFailedEventData(callBackData)) {
        this.logger.log('Callback: Play Failed received');
      } else {
        this.logger.log(callBackData, 'Unrecognized event type inside Call Service Call Back:');
      }
    } catch (error) {
      if (this.localSpeechMap?.lastEvent !== 'hangup') {
        this.logger.error(error, 'Voice service failure: ');
        Promise.allSettled([
          this.threadSpeechService.updateLastEvent(threadId, 'failure'),
          this.chatClientManager.handleError(threadId, error as Error, 'Voice service failure: '),
          this.playVoiceError(threadId, 'Oops, something went wrong. Can you please repeat?', callBackData),
        ]);
      }
    }
  }
}
